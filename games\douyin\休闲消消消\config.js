/**
 * 休闲消消消 - 游戏配置文件
 * 统一管理所有游戏配置，减少代码冗余
 */

const GAME_CONFIG = {
    // 版本信息
    VERSION: '2.0.0',
    GAME_NAME: '休闲消消消',
    SUBTITLE: '萌宠消除大作战',
    
    // 游戏基础配置
    GAME: {
        FPS: 60,
        ANIMATION_SPEED: 0.016, // 60fps
        DEBUG: false
    },

    // 性能优化配置（平衡版本）
    PERFORMANCE: {
        // 60FPS模式配置
        HIGH_FPS_MODE: true,           // 启用高帧率模式
        MAX_PARTICLES: 15,             // 最大粒子数量（减少设置）
        MAX_SPARKLES: 6,               // 最大闪烁数量（减少设置）
        MAX_FLOATING_TEXTS: 5,         // 最大浮动文字数量（减少设置）

        // 动画优化（保留游戏特性）
        FAST_ELIMINATION: false,       // 默认不启用快速消除
        SKIP_COMPLEX_EFFECTS: false,   // 保留复杂特效
        SIMPLE_PARTICLES: false,       // 保留完整粒子效果

        // 渲染优化（智能优化）
        BATCH_RENDERING: true,         // 批量渲染
        SKIP_SHADOWS: false,           // 保留阴影效果
        REDUCE_ALPHA_BLENDING: false,  // 保留透明度混合

        // 自适应优化阈值
        FPS_THRESHOLDS: {
            ULTRA_FAST: 20,            // 超快速模式阈值
            FAST: 35,                  // 快速模式阈值
            LIGHT: 50                  // 轻度优化阈值
        }
    },
    
    // 网格配置
    GRID: {
        SIZE_X: 8,              // 网格列数
        SIZE_Y: 10,             // 网格行数
        SPACING: 2,             // 格子间距
        INSET: 3,               // 格子内边距
        PADDING: 5              // 网格背景内边距（减少以让格子更大）
    },
    
    // UI布局配置
    LAYOUT: {
        // 间距配置
        ELEMENT_SPACING: 8,         // 元素间距（调整为8px，为小屏幕优化）

        // 返回按钮配置
        BACK_BUTTON: {
            X: 20,                  // 距离屏幕左边的距离
            Y: 70,                  // 距离屏幕顶部的距离（可调节）
            WIDTH: 100,             // 按钮宽度
            HEIGHT: 32              // 按钮高度
        },

        // 统计栏配置
        STATS_BAR: {
            WIDTH_PERCENT: 0.9,     // 屏幕宽度占比（0-1之间）
            HEIGHT: 100             // 统计栏高度
        },

        // 网格配置
        GRID_AREA: {
            WIDTH_PERCENT: 0.9,    // 屏幕宽度占比（调整为85%，为小屏幕留更多边距）
            HEIGHT_AUTO: true       // 高度自动计算（基于格子大小和行数）
        },

        // 道具栏配置
        PROPS_BAR: {
            WIDTH_PERCENT: 0.9,     // 屏幕宽度占比（0-1之间）
            HEIGHT: 90,            // 道具栏高度（增加到100px）
            BUTTON_SIZE: 60,        // 道具按钮大小
            BUTTON_SPACING: 20,     // 道具按钮间距（仅用于非平分布局）
            BUTTON_MARGIN_TOP: 12,  // 道具按钮距离顶部边框的距离（增加到12px）
            TEXT_MARGIN_TOP: 5,     // 道具名称距离按钮的距离
            BUTTON_RADIUS: 8,       // 道具按钮圆角半径
            LAYOUT_MODE: 'EQUAL_SPLIT', // 布局模式：'EQUAL_SPLIT'(平分) 或 'SPACING'(间距)
            PROP_COUNT: 4           // 道具数量
        },

        // 弹窗配置
        DIALOG: {
            WIDTH: 400,
            HEIGHT: 300
        },

        // 其他按钮配置
        BUTTON_HEIGHT: 50,
        BUTTON_SPACING: 20
    },
    
    // 颜色配置
    COLORS: {
        // 主题颜色
        PRIMARY: '#FF85A1',
        SECONDARY: '#FFB6C1',
        ACCENT: '#FFD1DC',
        
        // 文字颜色
        TEXT_PRIMARY: '#FFFFFF',
        TEXT_SECONDARY: '#E0E0E0',
        TEXT_DARK: '#333333',
        TEXT_LIGHT: '#666666',
        
        // 背景颜色
        BACKGROUND: '#F9F0FA',
        WHITE: '#FFFFFF',
        
        // 按钮颜色
        BUTTON_PRIMARY: '#4CAF50',
        BUTTON_SECONDARY: '#2196F3',
        BUTTON_DANGER: '#F44336',
        BUTTON_WARNING: '#FF9800',
        
        // 网格颜色
        GRID_BACKGROUND: 'rgba(255, 255, 255, 0.8)',
        GRID_BORDER: '#FFFFFF',
        GRID_SHADOW: 'rgba(0, 0, 0, 0.1)',
        
        // 萌宠颜色
        ANIMALS: {
            'cat': { bg: '#FF6B9D', glow: '#FF8FB3' },
            'dog': { bg: '#4ECDC4', glow: '#6ED5CD' },
            'lion': { bg: '#45B7D1', glow: '#65C7E1' },
            'fox': { bg: '#96CEB4', glow: '#A6DEC4' },
            'frog': { bg: '#FFEAA7', glow: '#FFEFB7' },
            'monkey': { bg: '#DDA0DD', glow: '#E7B0E7' },
            'panda': { bg: '#98D8C8', glow: '#A8E8D8' },
            'rabbit': { bg: '#F7DC6F', glow: '#F9E67F' },
            'tiger': { bg: '#BB8FCE', glow: '#CB9FDE' }
        },
        
        // 特殊方块颜色
        SPECIAL: {
            'rocket': { bg: '#FF4500', glow: '#FF6347' },
            'bomb': { bg: '#8B0000', glow: '#DC143C' }
        },
        
        // 关卡主题颜色
        LEVEL_THEMES: {
            1: {
                primary: '#E8F5E8',
                secondary: '#B8E6B8',
                accent: '#87CEEB',
                gradient: ['#E8F5E8', '#B8E6B8', '#87CEEB', '#98FB98']
            },
            2: {
                primary: '#F0E6FF',
                secondary: '#DDA0DD',
                accent: '#9370DB',
                gradient: ['#F0E6FF', '#DDA0DD', '#9370DB', '#BA55D3']
            },
            3: {
                primary: '#FFE5F1',
                secondary: '#FFB6C1',
                accent: '#FF69B4',
                gradient: ['#FFE5F1', '#FFB6C1', '#FF69B4', '#FF1493']
            }
        }
    },
    
    // 字体配置
    FONTS: {
        PRIMARY: 'Arial, "Microsoft YaHei"',
        SIZES: {
            SMALL: 12,
            MEDIUM: 16,
            LARGE: 20,
            XLARGE: 24,
            XXLARGE: 36,
            TITLE: 56
        },
        WEIGHTS: {
            NORMAL: 'normal',
            BOLD: 'bold'
        }
    },
    
    // 间距配置
    SPACING: {
        SMALL: 8,
        MEDIUM: 16,
        LARGE: 24,
        XLARGE: 32
    },
    
    // 萌宠配置
    ANIMALS: {
        TYPES: ['cat', 'dog', 'lion', 'fox', 'frog', 'monkey', 'panda', 'rabbit', 'tiger'],
        NAMES: {
            'cat': '猫',
            'dog': '狗',
            'elephant': '狮',
            'fox': '狐',
            'frog': '蛙',
            'monkey': '猴',
            'panda': '熊',
            'rabbit': '兔',
            'tiger': '虎'
        }
    },
    
    // 特殊方块配置
    SPECIAL_BLOCKS: {
        TYPES: {
            ROCKET: 'rocket',
            BOMB: 'bomb'
        }
    },
    
    // 关卡配置
    LEVELS: [
        {
            id: 1,
            name: '萌宠新手村',
            targetScore: 1000,
            animalCount: 5
        },
        {
            id: 2,
            name: '萌宠大练兵',
            targetScore: 4000,
            animalCount: 7
        },
        {
            id: 3,
            name: '萌宠总动员',
            targetScore: 8000,
            animalCount: 9
        }
    ],
    
    // 计分规则
    SCORING: {
        MATCH_3: 20,
        MATCH_4: 30,
        MATCH_5: 50,
        ROCKET_COMBO: 100,
        BOMB_COMBO: 150,
        ROCKET_BOMB_COMBO: 300,
        BOMB_BOMB_COMBO: 500,
        COMBO_MULTIPLIERS: [1.0, 1.5, 2.0, 2.5, 3.0]
    },
    
    // 道具配置
    PROPS: {
        TYPES: {
            REFRESH: 'refresh',
            BOMB: 'bomb',
            CLEAR: 'clear',
            LEVEL_DOWN: 'levelDown'
        },
        NAMES: {
            'refresh': '刷新卡',
            'bomb': '炸弹卡',
            'clear': '清屏卡',
            'levelDown': '降级卡'
        },
        SHORT_NAMES: {
            'refresh': '刷新卡',
            'bomb': '炸弹卡',
            'clear': '清屏卡',
            'levelDown': '降级卡'
        },
        COLORS: {
            'refresh': '#4CAF50',
            'bomb': '#F44336',
            'clear': '#2196F3',
            'levelDown': '#FF9800'
        },
        ICONS: {
            'refresh': '🔄',
            'bomb': '💣',
            'clear': '✨',
            'levelDown': '⬇️'
        },
        IMAGES: {
            'refresh': 'images/prop/refresh_card.png',
            'bomb': 'images/prop/bomb_card.png',
            'clear': 'images/prop/clear_card.png',
            'levelDown': 'images/prop/level_down_card.png'
        },
        DEFAULT_COUNTS: {
            'refresh': 3,
            'bomb': 2,
            'clear': 1,
            'levelDown': 2
        },
        DESCRIPTIONS: {
            'refresh': '重新排列所有方块',
            'bomb': '消除3x3区域的方块',
            'clear': '清除所有同色方块',
            'levelDown': '减少一种萌宠类型'
        }
    },
    
    // 动画配置
    ANIMATION: {
        PARTICLE_COUNT: 8,
        SPARKLE_COUNT: 3,
        MATCH_DURATION: 300,
        DROP_DURATION: 400,
        EXPLOSION_DURATION: 500,
        FADE_IN_DURATION: 200,
        FADE_OUT_DURATION: 200
    },
    
    // 资源路径配置
    ASSETS: {
        IMAGES: {
            BASE_PATH: 'images/',
            ANIMALS: 'images/animal/',
            PROPS: 'images/prop/',
            EXTRAS: 'images/extra/',
            UI: 'images/ui/',
            BUTTONS: 'images/button/'
        },
        AUDIO: {
            BASE_PATH: 'audios/',
            BACKGROUND: 'audios/background.mp3',
            SOUNDS: {
                BOMB: 'audios/bomb.mp3',
                CAT: 'audios/cat.mp3',
                GOOD: 'audios/good.mp3',
                LOSE: 'audios/lose.mp3',
                SHUA: 'audios/shua.mp3',
                SO: 'audios/so.mp3',
                WA: 'audios/wa.mp3',
                WIN: 'audios/win.mp3'
            }
        }
    },
    
    // 存储键名配置
    STORAGE: {
        GAME_SETTINGS: 'gameSettings',
        PLAYER_DATA: 'playerData',
        HIGH_SCORE: 'highScore',
        CURRENT_LEVEL: 'currentLevel'
    },
    
    // 默认设置
    DEFAULT_SETTINGS: {
        bgmVolume: 0.5,
        effectVolume: 0.5,
        mute: false,
        muteMode: false,
        autoSave: true,
        showTips: true
    }
};

// 工具函数
const CONFIG_UTILS = {
    // 获取关卡配置
    getLevelConfig(level) {
        return GAME_CONFIG.LEVELS.find(l => l.id === level) || GAME_CONFIG.LEVELS[0];
    },
    
    // 获取关卡主题颜色
    getLevelTheme(level) {
        return GAME_CONFIG.COLORS.LEVEL_THEMES[level] || GAME_CONFIG.COLORS.LEVEL_THEMES[1];
    },
    
    // 获取萌宠颜色
    getAnimalColor(type) {
        return GAME_CONFIG.COLORS.ANIMALS[type] || GAME_CONFIG.COLORS.ANIMALS['cat'];
    },
    
    // 获取特殊方块颜色
    getSpecialColor(type) {
        return GAME_CONFIG.COLORS.SPECIAL[type] || GAME_CONFIG.COLORS.SPECIAL['rocket'];
    },
    
    // 获取道具颜色
    getPropColor(type) {
        return GAME_CONFIG.PROPS.COLORS[type] || GAME_CONFIG.COLORS.BUTTON_PRIMARY;
    },
    
    // 获取图片路径
    getImagePath(category, name) {
        const basePath = GAME_CONFIG.ASSETS.IMAGES[category.toUpperCase()];
        return basePath ? `${basePath}${name}.png` : `images/${name}.png`;
    },
    
    // 获取音频路径
    getAudioPath(name) {
        return GAME_CONFIG.ASSETS.AUDIO.SOUNDS[name.toUpperCase()] || 
               `${GAME_CONFIG.ASSETS.AUDIO.BASE_PATH}${name}.mp3`;
    },
    
    // 获取连击倍率
    getComboMultiplier(comboCount) {
        const multipliers = GAME_CONFIG.SCORING.COMBO_MULTIPLIERS;
        return multipliers[Math.min(comboCount - 1, multipliers.length - 1)] || multipliers[multipliers.length - 1];
    },

    // 计算动态格子大小（修复版）
    calculateBlockSize(gridAreaWidth, gridSizeX, spacing, padding) {
        // 网格内容区域宽度 = 网格区域宽度 - 左右内边距
        const contentWidth = gridAreaWidth - (padding * 2);
        // 间距总宽度 = 间距 × (列数 - 1)
        const totalSpacingWidth = spacing * (gridSizeX - 1);
        // 可用于格子的宽度 = 内容宽度 - 间距总宽度
        const availableWidth = contentWidth - totalSpacingWidth;
        // 格子大小 = 可用宽度 / 列数
        const blockSize = Math.floor(availableWidth / gridSizeX);

        //console.log(`格子大小计算: 网格区域${gridAreaWidth}px - 内边距${padding*2}px - 间距${totalSpacingWidth}px = 可用${availableWidth}px ÷ ${gridSizeX}列 = ${blockSize}px`);

        return Math.max(blockSize, 20); // 最小20像素
    },

    // 计算布局位置（相对定位）
    calculateLayout(canvasWidth, canvasHeight) {
        const layout = GAME_CONFIG.LAYOUT;
        const spacing = layout.ELEMENT_SPACING;

        // 返回按钮位置
        const backButton = {
            x: layout.BACK_BUTTON.X,
            y: layout.BACK_BUTTON.Y,
            width: layout.BACK_BUTTON.WIDTH,
            height: layout.BACK_BUTTON.HEIGHT
        };

        // 统计栏位置（距离返回按钮底部 + spacing）
        const statsBar = {
            x: (canvasWidth - canvasWidth * layout.STATS_BAR.WIDTH_PERCENT) / 2,  // 居中
            y: backButton.y + backButton.height + spacing,
            width: canvasWidth * layout.STATS_BAR.WIDTH_PERCENT,
            height: layout.STATS_BAR.HEIGHT
        };

        // 先计算网格宽度和格子大小
        const gridAreaWidth = canvasWidth * layout.GRID_AREA.WIDTH_PERCENT;
        const gridConfig = GAME_CONFIG.GRID;
        const dynamicBlockSize = this.calculateBlockSize(gridAreaWidth, gridConfig.SIZE_X, gridConfig.SPACING, gridConfig.PADDING);

        // 计算网格区域高度（基于格子大小和行数）
        const actualGridHeight = gridConfig.SIZE_Y * dynamicBlockSize + (gridConfig.SIZE_Y - 1) * gridConfig.SPACING;
        const gridAreaHeight = actualGridHeight + gridConfig.PADDING * 2;

        // 网格位置（距离统计栏底部 + spacing）
        const gridArea = {
            x: (canvasWidth - gridAreaWidth) / 2,  // 居中
            y: statsBar.y + statsBar.height + spacing,
            width: gridAreaWidth,
            height: gridAreaHeight  // 使用动态计算的高度
        };

        // 网格背景就是网格区域本身（因为已经按实际尺寸计算）
        const actualGridWidth = gridConfig.SIZE_X * dynamicBlockSize + (gridConfig.SIZE_X - 1) * gridConfig.SPACING;
        const gridBackgroundWidth = gridArea.width;
        const gridBackgroundHeight = gridArea.height;
        const gridBackgroundBottom = gridArea.y + gridArea.height;

        //console.log(`网格尺寸: 格子${dynamicBlockSize}px, 内容${actualGridWidth}x${actualGridHeight}px, 背景${gridBackgroundWidth}x${gridBackgroundHeight}px`);

        // 道具栏位置（距离网格背景底部 + spacing）
        const propsBar = {
            x: (canvasWidth - canvasWidth * layout.PROPS_BAR.WIDTH_PERCENT) / 2,  // 居中
            y: gridBackgroundBottom + spacing,
            width: canvasWidth * layout.PROPS_BAR.WIDTH_PERCENT,
            height: layout.PROPS_BAR.HEIGHT
        };

        return {
            backButton,
            statsBar,
            gridArea,
            propsBar,
            spacing,
            gridBackgroundBottom,  // 网格背景的实际底部位置
            dynamicBlockSize,      // 动态计算的格子大小
            actualGridWidth,       // 实际网格内容宽度
            actualGridHeight,      // 实际网格内容高度
            gridBackgroundWidth,   // 网格背景宽度
            gridBackgroundHeight   // 网格背景高度
        };
    },

    // 获取网格配置（基于计算的布局）
    getGridConfig(canvasWidth, canvasHeight) {
        const layoutCalc = this.calculateLayout(canvasWidth, canvasHeight);
        const gridConfig = GAME_CONFIG.GRID;

        // 计算实际网格内容宽度
        const actualGridWidth = gridConfig.SIZE_X * layoutCalc.dynamicBlockSize + (gridConfig.SIZE_X - 1) * gridConfig.SPACING;

        // 计算网格内容在网格区域内的居中位置
        const contentAreaWidth = layoutCalc.gridArea.width - gridConfig.PADDING * 2;
        const gridContentStartX = layoutCalc.gridArea.x + gridConfig.PADDING + (contentAreaWidth - actualGridWidth) / 2;

        //console.log(`网格居中计算: 区域宽度${layoutCalc.gridArea.width}px, 内容宽度${actualGridWidth}px, 内容起始X=${gridContentStartX}`);

        return {
            startX: gridContentStartX,  // 网格内容居中位置
            startY: layoutCalc.gridArea.y + gridConfig.PADDING,  // 加上内边距
            width: layoutCalc.gridArea.width,
            height: layoutCalc.gridArea.height,
            blockSize: layoutCalc.dynamicBlockSize,              // 使用动态计算的格子大小
            sizeX: gridConfig.SIZE_X,
            sizeY: gridConfig.SIZE_Y,
            spacing: gridConfig.SPACING,
            inset: gridConfig.INSET,
            padding: gridConfig.PADDING,
            actualGridWidth: actualGridWidth  // 实际网格内容宽度
        };
    },

    // 获取统计栏配置
    getStatsBarConfig(canvasWidth, canvasHeight) {
        const layoutCalc = this.calculateLayout(canvasWidth, canvasHeight);

        return {
            x: layoutCalc.statsBar.x,
            y: layoutCalc.statsBar.y,
            width: layoutCalc.statsBar.width,
            height: layoutCalc.statsBar.height
        };
    },

    // 获取道具栏配置
    getPropsBarConfig(canvasWidth, canvasHeight) {
        const layoutCalc = this.calculateLayout(canvasWidth, canvasHeight);
        const propsConfig = GAME_CONFIG.LAYOUT.PROPS_BAR;

        return {
            x: layoutCalc.propsBar.x,
            y: layoutCalc.propsBar.y,
            width: layoutCalc.propsBar.width,
            height: layoutCalc.propsBar.height,
            buttonSize: propsConfig.BUTTON_SIZE,
            buttonSpacing: propsConfig.BUTTON_SPACING
        };
    }
};

// 导出配置（兼容多种环境）
if (typeof module !== 'undefined' && module.exports) {
    // Node.js 环境
    module.exports = { GAME_CONFIG, CONFIG_UTILS };
} else {
    // 浏览器和小程序环境
    try {
        if (typeof window !== 'undefined') {
            // 浏览器环境
            window.GAME_CONFIG = GAME_CONFIG;
            window.CONFIG_UTILS = CONFIG_UTILS;
            console.log('配置已设置到 window 对象');
        } else if (typeof globalThis !== 'undefined') {
            // 现代环境
            globalThis.GAME_CONFIG = GAME_CONFIG;
            globalThis.CONFIG_UTILS = CONFIG_UTILS;
            console.log('配置已设置到 globalThis 对象');
        } else if (typeof global !== 'undefined') {
            // Node.js 全局环境
            global.GAME_CONFIG = GAME_CONFIG;
            global.CONFIG_UTILS = CONFIG_UTILS;
            console.log('配置已设置到 global 对象');
        } else {
            // 小程序或其他环境，尝试设置到当前作用域
            this.GAME_CONFIG = GAME_CONFIG;
            this.CONFIG_UTILS = CONFIG_UTILS;
            console.log('配置已设置到当前作用域');
        }
    } catch (error) {
        console.error('设置全局配置失败:', error);
        // 最后的回退方案
        console.log('使用回退方案设置配置');
    }
}
