/**
 * 萌宠爱消消 - 游戏主入口文件
 * 抖音小游戏入口文件，负责模块加载和游戏管理
 */

// 检查是否在抖音小游戏环境中
if (typeof tt === 'undefined') {
    console.error('请在抖音小游戏环境中运行此游戏');
}

// 动态加载游戏管理器
function loadGameManager() {
    console.log('开始加载游戏管理器...');
    
    try {
        // 尝试使用require加载GameManager模块
        if (typeof require !== 'undefined') {
            const GameManagerClass = require('./GameManager.js');
            console.log('GameManager模块加载成功');
            
            // 初始化游戏
            const gameManager = new GameManagerClass();
            console.log('游戏初始化完成！');
            
        } else {
            console.warn('require不可用，使用内联GameManager');
            createInlineGameManager();
        }
    } catch (error) {
        console.error('加载GameManager失败:', error);
        console.log('回退到内联GameManager');
        createInlineGameManager();
    }
}

// 内联游戏管理器（备用方案）
function createInlineGameManager() {
    console.log('创建内联游戏管理器...');
    
    class InlineGameManager {
        constructor() {
            this.canvas = null;
            this.ctx = null;
            this.currentPage = null;
            this.init();
        }
        
        init() {
            console.log('初始化内联游戏管理器...');
            this.createCanvas();
            this.createMainPage();
            this.startGameLoop();
            console.log('内联游戏管理器初始化完成');
        }
        
        createCanvas() {
            this.canvas = tt.createCanvas();
            this.ctx = this.canvas.getContext('2d');
            const systemInfo = tt.getSystemInfoSync();
            this.canvas.width = systemInfo.windowWidth;
            this.canvas.height = systemInfo.windowHeight;
            console.log(`画布尺寸: ${this.canvas.width} x ${this.canvas.height}`);
        }
        
        createMainPage() {
            this.currentPage = {
                gameManager: this,
                canvas: this.canvas,
                ctx: this.ctx,
                titleAnimation: 0,
                
                init: function() {
                    console.log('内联主页面初始化');
                    this.setupTouchEvents();
                },
                
                update: function() {
                    this.titleAnimation += 0.02;
                },
                
                render: function() {
                    // 清空画布
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    // 渐变背景
                    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
                    gradient.addColorStop(0, '#87CEEB');
                    gradient.addColorStop(0.5, '#98FB98');
                    gradient.addColorStop(1, '#DDA0DD');
                    
                    this.ctx.fillStyle = gradient;
                    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    // 标题
                    const centerX = this.canvas.width / 2;
                    const titleY = this.canvas.height / 3;
                    
                    this.ctx.save();
                    this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                    this.ctx.shadowBlur = 10;
                    this.ctx.shadowOffsetX = 3;
                    this.ctx.shadowOffsetY = 3;
                    
                    this.ctx.fillStyle = '#FFFFFF';
                    this.ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
                    this.ctx.textAlign = 'center';
                    this.ctx.textBaseline = 'middle';
                    
                    const scale = 1 + Math.sin(this.titleAnimation) * 0.05;
                    this.ctx.save();
                    this.ctx.translate(centerX, titleY);
                    this.ctx.scale(scale, scale);
                    this.ctx.fillText('休闲消消消', 0, 0);
                    this.ctx.restore();
                    
                    this.ctx.fillStyle = '#FFD700';
                    this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
                    this.ctx.fillText('萌宠消除大作战', centerX, titleY + 60);
                    
                    this.ctx.restore();
                    
                    // 简单提示
                    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    this.ctx.font = '20px Arial, "Microsoft YaHei"';
                    this.ctx.textAlign = 'center';
                    this.ctx.fillText('点击屏幕开始游戏', centerX, this.canvas.height - 100);
                    
                    // 版本信息
                    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                    this.ctx.font = '14px Arial, "Microsoft YaHei"';
                    this.ctx.textAlign = 'right';
                    this.ctx.textBaseline = 'bottom';
                    this.ctx.fillText('v1.0.0 - 简化版', this.canvas.width - 20, this.canvas.height - 20);
                },
                
                setupTouchEvents: function() {
                    if (typeof tt !== 'undefined') {
                        this.touchStartHandler = (e) => {
                            console.log('触摸事件 - 游戏开始');
                            // 简单的触摸响应
                            this.showMessage();
                        };
                        
                        tt.onTouchStart(this.touchStartHandler);
                        console.log('触摸事件监听器设置完成');
                    }
                },
                
                showMessage: function() {
                    console.log('显示游戏消息');
                    // 可以在这里添加更多交互逻辑
                },
                
                destroy: function() {
                    if (typeof tt !== 'undefined' && this.touchStartHandler) {
                        tt.offTouchStart(this.touchStartHandler);
                    }
                }
            };
            
            this.currentPage.init();
        }
        
        startGameLoop() {
            console.log('启动游戏循环...');
            
            const gameLoop = () => {
                try {
                    // 更新当前页面
                    if (this.currentPage) {
                        if (typeof this.currentPage.update === 'function') {
                            this.currentPage.update();
                        }
                        if (typeof this.currentPage.render === 'function') {
                            this.currentPage.render();
                        }
                    }
                    
                    // 请求下一帧
                    requestAnimationFrame(gameLoop);
                } catch (error) {
                    console.error('游戏循环错误:', error);
                    // 继续循环，避免游戏卡死
                    requestAnimationFrame(gameLoop);
                }
            };
            
            // 启动循环
            requestAnimationFrame(gameLoop);
            console.log('游戏循环已启动');
        }
    }
    
    // 创建内联游戏管理器实例
    const gameManager = new InlineGameManager();
    console.log('内联游戏管理器创建完成！');
}

// 启动游戏
console.log('开始初始化萌宠爱消消游戏...');
loadGameManager();