/**
 * 美化版主页面 - 统一风格的主页面
 */
// 添加 roundRect 兼容性支持（小程序环境兼容）
function addRoundRectSupport(ctx) {
    if (ctx && !ctx.roundRect) {
        ctx.roundRect = function(x, y, width, height, radius) {
            if (typeof radius === 'undefined') {
                radius = 5;
            }
            if (typeof radius === 'number') {
                radius = {tl: radius, tr: radius, br: radius, bl: radius};
            } else {
                var defaultRadius = {tl: 0, tr: 0, br: 0, bl: 0};
                for (var side in defaultRadius) {
                    radius[side] = radius[side] || defaultRadius[side];
                }
            }
            this.moveTo(x + radius.tl, y);
            this.lineTo(x + width - radius.tr, y);
            this.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
            this.lineTo(x + width, y + height - radius.br);
            this.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
            this.lineTo(x + radius.bl, y + height);
            this.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
            this.lineTo(x, y + radius.tl);
            this.quadraticCurveTo(x, y, x + radius.tl, y);
            this.closePath();
            return this;
        };
    }
}

// 尝试为全局 CanvasRenderingContext2D 添加支持（如果存在）
if (typeof CanvasRenderingContext2D !== 'undefined' && CanvasRenderingContext2D.prototype && !CanvasRenderingContext2D.prototype.roundRect) {
    CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
        if (typeof radius === 'undefined') {
            radius = 5;
        }
        if (typeof radius === 'number') {
            radius = {tl: radius, tr: radius, br: radius, bl: radius};
        } else {
            var defaultRadius = {tl: 0, tr: 0, br: 0, bl: 0};
            for (var side in defaultRadius) {
                radius[side] = radius[side] || defaultRadius[side];
            }
        }
        this.moveTo(x + radius.tl, y);
        this.lineTo(x + width - radius.tr, y);
        this.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
        this.lineTo(x + width, y + height - radius.br);
        this.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
        this.lineTo(x + radius.bl, y + height);
        this.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
        this.lineTo(x, y + radius.tl);
        this.quadraticCurveTo(x, y, x + radius.tl, y);
        this.closePath();
        return this;
    };
}

class BeautifulMainPage {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;

        // 获取逻辑尺寸（用于高分辨率支持）
        this.width = gameManager.logicalWidth || gameManager.canvas.width;
        this.height = gameManager.logicalHeight || gameManager.canvas.height;

        // 为当前上下文添加 roundRect 支持
        addRoundRectSupport(this.ctx);

        // 按钮配置（使用全局配置）
        const config = typeof GAME_CONFIG !== 'undefined' ? GAME_CONFIG : {};
        this.buttons = [
            {
                id: 'start',
                text: '开始游戏',
                x: 0,
                y: 0,
                width: 220,
                height: 70,
                color: config.COLORS ? config.COLORS.BUTTON_PRIMARY : '#4CAF50',
                hoverColor: '#45a049',
                icon: '🎮'
            },
            {
                id: 'rank',
                text: '排行榜',
                x: 0,
                y: 0,
                width: 220,
                height: 70,
                color: config.COLORS ? config.COLORS.BUTTON_SECONDARY : '#2196F3',
                hoverColor: '#1976D2',
                icon: '🏆'
            },
            {
                id: 'setting',
                text: '设置',
                x: 0,
                y: 0,
                width: 220,
                height: 70,
                color: config.COLORS ? config.COLORS.BUTTON_WARNING : '#FF9800',
                hoverColor: '#F57C00',
                icon: '⚙️'
            }
        ];

        // 动画状态
        this.titleAnimation = 0;
        this.buttonAnimation = 0;
        this.animationTime = 0;

        // 背景效果
        this.backgroundStars = null;
        this.particles = [];

        console.log('BeautifulMainPage初始化完成');
    }
    
    init() {
        console.log('初始化美化版主页面');
        this.calculateButtonPositions();
        this.initBackgroundEffects();
        this.setupTouchEvents();
        console.log('美化版主页面初始化完成');
    }

    // 初始化背景效果
    initBackgroundEffects() {
        try {
            if (typeof BackgroundUtils !== 'undefined') {
                this.backgroundStars = BackgroundUtils.createStars(20, this.width, this.height);
                this.initParticles();
                console.log('背景效果初始化成功');
            } else {
                console.warn('BackgroundUtils未加载，使用简化背景');
                this.backgroundStars = null;
            }
        } catch (error) {
            console.warn('背景效果初始化失败:', error);
            this.backgroundStars = null;
        }
    }

    // 初始化粒子效果
    initParticles() {
        this.particles = [];
        for (let i = 0; i < 15; i++) {
            this.particles.push({
                x: Math.random() * this.width,
                y: Math.random() * this.height,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                size: Math.random() * 3 + 1,
                color: `hsl(${Math.random() * 360}, 70%, 80%)`,
                life: 1,
                decay: 0.005
            });
        }
    }

    // 计算按钮位置
    calculateButtonPositions() {
        const centerX = this.width / 2;
        const startY = this.height / 2 + 50;
        const spacing = 90;

        this.buttons.forEach((button, index) => {
            button.x = centerX - button.width / 2;
            button.y = startY + index * spacing;
        });
    }
    
    handleTouchStart(x, y) {
        // 检查按钮点击
        for (const button of this.buttons) {
            if (this.isPointInButton(x, y, button)) {
                console.log(`点击按钮: ${button.id}`);
                this.handleButtonClick(button.id);
                return;
            }
        }
    }
    
    handleButtonClick(buttonId) {
        console.log(`按钮点击: ${buttonId}`);

        switch (buttonId) {
            case 'start':
                // 使用全局配置的关卡信息
                const config = typeof GAME_CONFIG !== 'undefined' ? GAME_CONFIG : {};
                const levelConfig = config.LEVELS ? config.LEVELS[0] : {
                    targetScore: 1000,
                    name: '萌宠新手村',
                    level: 1
                };
                this.gameManager.switchToPage('game', levelConfig);
                break;
            case 'rank':
                this.gameManager.switchToPage('rank');
                break;
            case 'setting':
                this.gameManager.switchToPage('setting');
                break;
            default:
                console.warn(`未知按钮: ${buttonId}`);
        }
    }
    
    update() {
        this.titleAnimation += 0.008; // 减慢动画速度，更流畅
        this.buttonAnimation += 0.006; // 减慢按钮动画
        this.animationTime += 0.016; // 60fps

        // 更新背景效果
        if (this.backgroundStars && typeof BackgroundUtils !== 'undefined') {
            try {
                BackgroundUtils.updateStars(this.backgroundStars, this.width, this.height);
            } catch (error) {
                console.warn('背景更新失败:', error);
            }
        }

        // 更新粒子
        this.updateParticles();
    }

    // 更新粒子效果
    updateParticles() {
        this.particles.forEach(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= particle.decay;

            // 边界检测
            if (particle.x < 0 || particle.x > this.width) particle.vx *= -1;
            if (particle.y < 0 || particle.y > this.height) particle.vy *= -1;

            // 重生粒子
            if (particle.life <= 0) {
                particle.x = Math.random() * this.width;
                particle.y = Math.random() * this.height;
                particle.life = 1;
            }
        });
    }
    
    render() {
        this.ctx.clearRect(0, 0, this.width, this.height);
        this.renderBackground();
        this.renderTitle();
        this.renderButtons();
        this.renderVersionInfo();
    }
    
    renderBackground() {
        // 渐变背景
        const gradient = this.ctx.createRadialGradient(
            this.width / 2, this.height / 2, 0,
            this.width / 2, this.height / 2,
            Math.max(this.width, this.height) / 2
        );
        gradient.addColorStop(0, '#ffecd2');
        gradient.addColorStop(0.5, '#fcb69f');
        gradient.addColorStop(1, '#ff8a80');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.width, this.height);

        // 渲染背景星星
        if (this.backgroundStars && typeof BackgroundUtils !== 'undefined') {
            try {
                BackgroundUtils.renderStars(this.ctx, this.backgroundStars);
            } catch (error) {
                console.warn('星星渲染失败:', error);
            }
        }

        // 渲染粒子
        this.renderParticles();
    }

    // 渲染粒子效果
    renderParticles() {
        this.particles.forEach(particle => {
            this.ctx.save();
            this.ctx.globalAlpha = particle.life;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }
    
    renderTitle() {
        const centerX = this.width / 2;
        const titleY = this.height / 3;
        
        this.ctx.save();
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 15;
        this.ctx.shadowOffsetX = 5;
        this.ctx.shadowOffsetY = 5;
        
        // 主标题（使用全局配置）
        const config = typeof GAME_CONFIG !== 'undefined' ? GAME_CONFIG : {};
        this.ctx.fillStyle = config.COLORS ? config.COLORS.TEXT_PRIMARY : '#FFFFFF';
        this.ctx.font = `bold ${config.FONTS ? config.FONTS.SIZES.TITLE : 56}px ${config.FONTS ? config.FONTS.PRIMARY : 'Arial, "Microsoft YaHei"'}`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        // 使用更平滑的缓动函数
        const easeInOut = (t) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
        const normalizedTime = (Math.sin(this.titleAnimation) + 1) / 2; // 0-1之间
        const easedTime = easeInOut(normalizedTime);
        const scale = 1 + easedTime * 0.03; // 减小缩放幅度

        this.ctx.save();
        this.ctx.translate(centerX, titleY);
        this.ctx.scale(scale, scale);
        this.ctx.fillText(config.GAME_NAME || '休闲消消消', 0, 0);
        this.ctx.restore();

        // 副标题 - 添加轻微的颜色变化
        const hue = 45 + Math.sin(this.titleAnimation * 0.5) * 10; // 金色色调变化
        this.ctx.fillStyle = `hsl(${hue}, 100%, 50%)`;
        this.ctx.font = `bold 28px ${config.FONTS ? config.FONTS.PRIMARY : 'Arial, "Microsoft YaHei"'}`;
        this.ctx.fillText(config.SUBTITLE || '萌宠消除大作战', centerX, titleY + 70);
        
        this.ctx.restore();
    }
    
    renderButtons() {
        this.buttons.forEach((button, index) => {
            // 取消按钮动画
            // const hoverOffset = Math.sin(this.buttonAnimation + index * 0.5) * 3;
            const hoverOffset = 0; // 静态按钮

            this.ctx.save();
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            this.ctx.shadowBlur = 10;
            this.ctx.shadowOffsetX = 3;
            this.ctx.shadowOffsetY = 3;

            // 按钮背景渐变
            const buttonGradient = this.ctx.createLinearGradient(
                button.x, button.y,
                button.x, button.y + button.height
            );
            buttonGradient.addColorStop(0, button.color);
            buttonGradient.addColorStop(1, button.hoverColor);

            this.ctx.fillStyle = buttonGradient;
            this.ctx.beginPath();
            this.ctx.roundRect(button.x, button.y + hoverOffset, button.width, button.height, 15);
            this.ctx.fill();

            // 按钮边框
            this.ctx.shadowColor = 'transparent';
            this.ctx.strokeStyle = '#FFFFFF';
            this.ctx.lineWidth = 3;
            this.ctx.stroke();

            // 按钮文字（取消icon）
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            const textY = button.y + button.height / 2 + hoverOffset;
            this.ctx.fillText(button.text, // 只显示文字，不显示icon
                button.x + button.width / 2, textY);
            
            this.ctx.restore();
        });
    }
    
    renderVersionInfo() {
        const config = typeof GAME_CONFIG !== 'undefined' ? GAME_CONFIG : {};
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        this.ctx.font = `16px ${config.FONTS ? config.FONTS.PRIMARY : 'Arial, "Microsoft YaHei"'}`;
        this.ctx.textAlign = 'right';
        this.ctx.textBaseline = 'bottom';
        this.ctx.fillText(`${config.VERSION || 'v2.0.0'} - 美化版`, this.width - 20, this.height - 20);
    }
    
    isPointInButton(x, y, button) {
        return x >= button.x && x <= button.x + button.width &&
               y >= button.y && y <= button.y + button.height;
    }
    
    setupTouchEvents() {
        console.log('设置触摸事件监听器...');
        
        if (typeof tt !== 'undefined') {
            console.log('使用抖音小游戏触摸事件API');
            
            this.touchStartHandler = (e) => {
                console.log('触摸开始事件:', e);
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchStart(touch.clientX, touch.clientY);
                }
            };
            
            tt.onTouchStart(this.touchStartHandler);
            console.log('抖音小游戏触摸事件监听器设置完成');
        } else {
            console.warn('非抖音小游戏环境，跳过触摸事件设置');
        }
    }
    
    destroy() {
        console.log('清理BeautifulMainPage资源');
        if (typeof tt !== 'undefined' && this.touchStartHandler) {
            tt.offTouchStart(this.touchStartHandler);
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BeautifulMainPage;
} else {
    window.BeautifulMainPage = BeautifulMainPage;
}
