/**
 * 游戏页面动画控制器 - 协调掉落、生成、匹配等动画
 */
// 导入GamePageCore类
const GamePageCore = require('./GamePageCore');
class GamePageAnimator {
    constructor(core) {
        this.core = core;
        this.canvas = core.canvas;
        this.ctx = core.ctx;
        
        // 子系统
        this.falling = null;
        this.generator = null;
        this.matcher = null;
        
        // 动画状态
        this.isAnimating = false;
        this.animationQueue = [];
        this.currentAnimation = null;
        
        console.log('GamePageAnimator初始化完成');
    }
    
    // 初始化子系统
    init() {
        // 创建子系统实例
        this.falling = new GamePageFalling(this.core);
        this.generator = new GamePageGenerator(this.core);
        this.matcher = new GamePageMatcher(this.core);
        
        console.log('动画控制器子系统初始化完成');
    }
    
    // 处理方块交换后的完整流程
    async processSwapResult(swapResult) {
        if (!swapResult.success) {
            return false;
        }
        
        console.log('开始处理交换结果');
        this.isAnimating = true;
        
        try {
            // 检查是否有特殊方块参与交换
            const { from, to } = swapResult;
            const block1 = this.core.grid[from.row] && this.core.grid[from.row][from.col];
            const block2 = this.core.grid[to.row] && this.core.grid[to.row][to.col];

            const hasSpecialBlock = (block1 && (block1.blockType === 'special' || block1.category === 'special')) ||
                                   (block2 && (block2.blockType === 'special' || block2.category === 'special'));

            if (hasSpecialBlock) {
                console.log('检测到特殊方块交换，直接处理特殊效果');
                console.log(`方块1: ${block1?.type} (${block1?.blockType || block1?.category})`);
                console.log(`方块2: ${block2?.type} (${block2?.blockType || block2?.category})`);

                // 处理特殊方块效果
                const specialResult = await this.processSpecialBlockSwap(block1, block2, from, to);

                if (specialResult.score > 0) {
                    this.core.updateScore(specialResult.score);
                }

                // 检查游戏状态
                this.core.checkGameStatus();

                return true;
            }

            // 普通方块交换：检查是否产生匹配
            const initialMatchResult = this.matcher.findAllMatches();
            const hasInitialMatch = initialMatchResult.matches.length > 0;

            console.log(`初始匹配检查: ${hasInitialMatch ? '有匹配' : '无匹配'}, 匹配数: ${initialMatchResult.matches.length}`);

            // 如果初始交换没有产生匹配，直接返回false
            if (!hasInitialMatch) {
                console.log('初始交换无匹配，将触发复位');
                return false;
            }

            // 循环处理匹配和掉落，直到没有新的匹配
            let hasMoreMatches = true;
            let totalScore = 0;
            let roundCount = 0;

            while (hasMoreMatches) {
                roundCount++;
                console.log(`开始第${roundCount}轮匹配处理`);

                // 1. 检查匹配
                const matchResult = this.matcher.findAllMatches();

                if (matchResult.matches.length > 0) {
                    // 2. 处理匹配消除并获取被移除的位置
                    const matchResult2 = await this.matcher.processMatches(matchResult);
                    console.log(`第${roundCount}轮匹配处理结果:`, matchResult2);
                    console.log(`本次得分: ${matchResult2.score}, 累计得分: ${totalScore}`);
                    totalScore += matchResult2.score;

                    // 3. 获取被移除的位置
                    const removedPositions = matchResult2.removedPositions;

                    // 4. 处理方块掉落和生成新方块
                    await this.falling.processFalling(removedPositions);

                    // 5. 等待所有动画完成
                    await this.waitForAnimationsComplete();

                } else {
                    hasMoreMatches = false;
                    console.log(`第${roundCount}轮无新匹配，结束处理`);
                }
            }


            // 只有当有得分时才更新分数
            if (totalScore > 0) {
                this.core.updateScore(totalScore);
                console.log(`更新总分数: ${totalScore}`);
            }

            // 检查游戏状态（胜利条件）
            this.core.checkGameStatus();

            console.log(`交换处理完成，总轮数: ${roundCount}, 总得分: ${totalScore}`);

            // 延迟检查是否还有可能的移动（确保所有动画都完成）
            setTimeout(() => {
                this.checkForPossibleMovesAfterAnimation();
            }, 1000);

            // 由于已经确认初始交换有匹配，总是返回true，避免复位
            return true;
            
        } catch (error) {
            console.error('处理交换结果时出错:', error);
            return false;
        } finally {
            this.isAnimating = false;
        }
    }
    
    // 生成新方块
    async generateNewBlocks(removedPositions) {
        // 统计每列需要生成的数量
        const columnCounts = {};
        removedPositions.forEach(pos => {
            columnCounts[pos.col] = (columnCounts[pos.col] || 0) + 1;
        });
        
        // 为每列请求生成
        const promises = [];
        Object.keys(columnCounts).forEach(col => {
            const count = columnCounts[col];
            promises.push(this.generateBlocksForColumn(parseInt(col), count));
        });
        
        // 等待所有生成完成
        await Promise.all(promises);
    }
    
    // 为指定列生成方块
    generateBlocksForColumn(col, count) {
        return new Promise((resolve) => {
            let generated = 0;
            
            const generateNext = () => {
                if (generated >= count) {
                    resolve();
                    return;
                }
                
                // 找到目标位置
                const targetRow = this.findTopEmptyRow(col);
                if (targetRow >= 0) {
                    // 创建新方块
                    const newBlock = this.core.createRandomBlock(
                        targetRow,
                        col,
                        this.core.gridStartX,
                        this.core.gridStartY
                    );
                    
                    if (newBlock) {
                        // 获取间距
                        const spacing = this.core.getSpacing();

                        // 设置从顶部掉落（考虑间距）
                        newBlock.y = this.core.gridStartY - (generated + 1) * (this.core.blockSize + spacing);
                        newBlock.targetY = this.core.gridStartY + targetRow * (this.core.blockSize + spacing);
                        newBlock.isFalling = true;
                        newBlock.fallVelocity = 2; // 设置初始速度
                        
                        // 添加到掉落系统
                        this.falling.fallingBlocks.push({
                            block: newBlock,
                            fromRow: -1 - generated,
                            toRow: targetRow,
                            col: col,
                            startTime: Date.now() + generated * 100,
                            isNew: true
                        });
                        
                        generated++;
                        
                        // 延迟生成下一个
                        setTimeout(generateNext, 100);
                    } else {
                        resolve();
                    }
                } else {
                    resolve();
                }
            };
            
            generateNext();
        });
    }
    
    // 找到列中最顶部的空位
    findTopEmptyRow(col) {
        for (let row = 0; row < this.core.gridSizeY; row++) {
            if (!this.core.grid[row] || !this.core.grid[row][col] || this.core.grid[row][col].isRemoved) {
                return row;
            }
        }
        return -1;
    }
    
    // 等待所有动画完成
    waitForAnimationsComplete() {
        return new Promise((resolve) => {
            const checkComplete = () => {
                const fallingComplete = !this.falling.isFalling();
                const generatingComplete = !this.generator.isGeneratingBlocks();
                const matchingComplete = !this.matcher.isProcessing();

                if (fallingComplete && generatingComplete && matchingComplete) {
                    resolve();
                } else {
                    setTimeout(checkComplete, 50);
                }
            };

            checkComplete();
        });
    }

    // 处理特殊方块交换
    async processSpecialBlockSwap(block1, block2, from, to) {
        console.log('=== 处理特殊方块交换 ===');

        let totalScore = 0;
        let eliminatedBlocks = [];

        // 检查哪个是特殊方块
        const specialBlock = (block1 && (block1.blockType === 'special' || block1.category === 'special')) ? block1 : block2;
        const normalBlock = specialBlock === block1 ? block2 : block1;
        const specialPos = specialBlock === block1 ? from : to;
        const normalPos = specialBlock === block1 ? to : from;

        console.log(`特殊方块: ${specialBlock.type} 在位置 (${specialPos.row}, ${specialPos.col})`);
        console.log(`普通方块: ${normalBlock?.type} 在位置 (${normalPos.row}, ${normalPos.col})`);

        // 根据特殊方块类型处理效果
        if (specialBlock.type === 'bomb_extra' || specialBlock.type === 'bomb') {
            // 小炸弹：消除周围3x3区域
            console.log('触发小炸弹效果');
            const bombResult = this.core.triggerBombEffect(specialPos.row, specialPos.col, 3);
            totalScore += bombResult.score;
            eliminatedBlocks = [...eliminatedBlocks, ...bombResult.eliminatedBlocks];

        } else if (specialBlock.type === 'rocket_horizontal' || specialBlock.type === 'rocket_vertical' || specialBlock.type === 'rocket') {
            // 小火箭：消除一行或一列
            let rocketType = specialBlock.type;
            if (rocketType === 'rocket') {
                // 旧版本的rocket，默认为纵向
                rocketType = 'rocket_vertical';
            }

            console.log(`触发${rocketType === 'rocket_horizontal' ? '横向' : '纵向'}火箭效果`);
            const rocketResult = this.core.triggerRocketEffect(specialPos.row, specialPos.col, rocketType);
            totalScore += rocketResult.score;
            eliminatedBlocks = [...eliminatedBlocks, ...rocketResult.eliminatedBlocks];
        } else {
            console.warn(`未知的特殊方块类型: ${specialBlock.type}`);
        }

        // 处理掉落并检测连锁匹配
        if (eliminatedBlocks.length > 0) {
            console.log(`特殊方块效果消除了${eliminatedBlocks.length}个方块`);

            // 先清除被消除的方块
            eliminatedBlocks.forEach(item => {
                if (this.core.grid[item.row] && this.core.grid[item.row][item.col]) {
                    this.core.grid[item.row][item.col] = null;
                }
            });

            // 延迟处理掉落并检测连锁匹配
            setTimeout(async () => {
                if (this.falling) {
                    const removedPositions = eliminatedBlocks.map(block => ({ row: block.row, col: block.col }));
                    console.log('特殊方块效果：开始掉落处理');

                    // 等待掉落完成
                    await this.falling.processFalling(removedPositions);
                    console.log('特殊方块效果：掉落完成，检查连锁匹配');

                    // 检查掉落后是否有新的匹配
                    const specialType = specialBlock.type === 'rocket' || specialBlock.type === 'rocket_horizontal' || specialBlock.type === 'rocket_vertical' ? '小火箭' : '小炸弹';
                    this.core.checkAndProcessCascadeMatches(specialType);
                }
            }, 300);
        }

        return { score: totalScore, eliminatedBlocks };
    }

    // 在动画完成后检查是否还有可能的移动
    checkForPossibleMovesAfterAnimation() {
        // 确保游戏没有结束且没有在动画中
        if (this.core.isGameOver || this.core.isLevelComplete || this.hasActiveAnimations()) {
            console.log('跳过可能移动检查：游戏已结束或正在动画中');
            return;
        }

        console.log('=== 检查是否还有可能的移动 ===');

        // 使用matcher的方法检查
        if (this.matcher && typeof this.matcher.hasPossibleMatches === 'function') {
            const hasPossibleMoves = this.matcher.hasPossibleMatches();
            console.log(`可能移动检查结果: ${hasPossibleMoves ? '有可能的移动' : '没有可能的移动'}`);

            if (!hasPossibleMoves) {
                console.log('没有找到可能的移动，触发游戏结束检查');
                // 再次确认没有可能的移动后才触发游戏结束
                setTimeout(() => {
                    if (!this.core.isGameOver && !this.core.isLevelComplete && !this.hasActiveAnimations()) {
                        this.core.checkForPossibleMoves();
                    }
                }, 500);
            }
        } else {
            // 回退到core的检查方法
            this.core.checkForPossibleMoves();
        }
    }
    
    // 更新所有动画
    update() {
        if (this.falling) {
            this.falling.update();
        }
        
        // 更新其他动画系统...
    }
    
    // 渲染所有动画效果
    render(renderer) {
        if (this.falling) {
            this.falling.renderFallingBlocks(renderer);
        }
        
        // 渲染其他动画效果...
    }
    
    // 检查是否有动画正在进行
    hasActiveAnimations() {
        return this.isAnimating || 
               (this.falling && this.falling.isFalling()) ||
               (this.generator && this.generator.isGeneratingBlocks()) ||
               (this.matcher && this.matcher.isProcessing());
    }
    
    // 暂停所有动画
    pauseAnimations() {
        if (this.generator) {
            this.generator.pause();
        }
        
        console.log('所有动画已暂停');
    }
    
    // 恢复所有动画
    resumeAnimations() {
        if (this.generator) {
            this.generator.resume();
        }
        
        console.log('所有动画已恢复');
    }
    
    // 清理所有动画
    clear() {
        this.isAnimating = false;
        this.animationQueue = [];
        this.currentAnimation = null;
        
        if (this.falling) {
            this.falling.clear();
        }
        
        if (this.generator) {
            this.generator.clear();
        }
        
        if (this.matcher) {
            this.matcher.clear();
        }
        
        console.log('动画控制器已清理');
    }
    
    // 获取动画状态
    getAnimationStatus() {
        return {
            isAnimating: this.isAnimating,
            falling: this.falling ? this.falling.fallingBlocks.length : 0,
            generating: this.generator ? this.generator.getGenerationStats() : null,
            matching: this.matcher ? this.matcher.getMatchStats() : null
        };
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageAnimator;
} else {
    window.GamePageAnimator = GamePageAnimator;
}