// GamePage核心类 - 基础功能和初始化（超美化版本）
class GamePageCore {
    
    // 简化的响应式格子大小计算
    calculateResponsiveBlockSize(canvasWidth, canvasHeight, config) {
        // 使用配置中的布局参数
        const layout = config.LAYOUT;
        const gridConfig = config.GRID;

        // 计算网格区域宽度（80%屏幕宽度）
        const gridAreaWidth = canvasWidth * (layout.GRID_AREA.WIDTH_PERCENT || 0.8);

        // 计算格子大小
        const contentWidth = gridAreaWidth - (gridConfig.PADDING * 2);
        const totalSpacingWidth = gridConfig.SPACING * (gridConfig.SIZE_X - 1);
        const availableWidth = contentWidth - totalSpacingWidth;
        const blockSize = Math.floor(availableWidth / gridConfig.SIZE_X);

        console.log(`📱 响应式计算详情:`);
        console.log(`   屏幕尺寸: ${canvasWidth}x${canvasHeight}`);
        console.log(`   网格区域宽度: ${gridAreaWidth}px (${(layout.GRID_AREA.WIDTH_PERCENT || 0.8) * 100}%)`);
        console.log(`   内容宽度: ${contentWidth}px (减去内边距${gridConfig.PADDING * 2}px)`);
        console.log(`   间距总宽度: ${totalSpacingWidth}px (${gridConfig.SPACING}px × ${gridConfig.SIZE_X - 1})`);
        console.log(`   可用宽度: ${availableWidth}px`);
        console.log(`   计算格子大小: ${blockSize}px (${availableWidth}px ÷ ${gridConfig.SIZE_X})`);

        return Math.max(blockSize, 20); // 最小20像素
    }

    // 验证网格是否在屏幕范围内
    validateGridBounds() {
        const gridStartX = this.gridStartX;
        const gridStartY = this.gridStartY;
        const gridWidth = this.gridSizeX * this.blockSize + (this.gridSizeX - 1) * this.getSpacing();
        const gridHeight = this.gridSizeY * this.blockSize + (this.gridSizeY - 1) * this.getSpacing();
        const gridEndX = gridStartX + gridWidth;
        const gridEndY = gridStartY + gridHeight;

        console.log(`🔍 网格边界检查:`);
        console.log(`   逻辑屏幕: ${this.logicalWidth} x ${this.logicalHeight}`);
        console.log(`   网格位置: (${gridStartX.toFixed(1)}, ${gridStartY.toFixed(1)})`);
        console.log(`   网格尺寸: ${gridWidth.toFixed(1)} x ${gridHeight.toFixed(1)}`);
        console.log(`   网格结束: (${gridEndX.toFixed(1)}, ${gridEndY.toFixed(1)})`);

        const isValid = gridStartX >= 0 && gridStartY >= 0 &&
                       gridEndX <= this.logicalWidth && gridEndY <= this.logicalHeight;

        if (!isValid) {
            console.warn(`⚠️ 网格超出屏幕边界!`);
            if (gridStartX < 0) console.warn(`   左边界超出: ${gridStartX.toFixed(1)}px`);
            if (gridStartY < 0) console.warn(`   上边界超出: ${gridStartY.toFixed(1)}px`);
            if (gridEndX > this.logicalWidth) console.warn(`   右边界超出: ${(gridEndX - this.logicalWidth).toFixed(1)}px`);
            if (gridEndY > this.logicalHeight) console.warn(`   下边界超出: ${(gridEndY - this.logicalHeight).toFixed(1)}px`);
        } else {
            console.log(`✅ 网格在屏幕范围内`);
        }

        return isValid;
    }

    // 自适应调整网格大小（当超出屏幕时）
    adaptiveGridResize() {
        if (this.validateGridBounds()) {
            return; // 网格在范围内，无需调整
        }

        console.log(`🔧 开始自适应调整网格大小...`);

        const config = GamePageCore.getConfig();
        const layout = config.LAYOUT;
        const gridConfig = config.GRID;

        // 计算可用的垂直空间
        const backButtonBottom = layout.BACK_BUTTON.Y + layout.BACK_BUTTON.HEIGHT;
        const statsBarHeight = layout.STATS_BAR.HEIGHT;
        const propsBarHeight = layout.PROPS_BAR.HEIGHT;
        const totalSpacing = layout.ELEMENT_SPACING * 3; // 3个间距
        const usedVerticalSpace = backButtonBottom + statsBarHeight + propsBarHeight + totalSpacing;
        const availableVerticalSpace = this.logicalHeight - usedVerticalSpace - gridConfig.PADDING * 2;

        // 计算可用的水平空间
        const maxGridAreaWidth = this.logicalWidth * 0.95; // 最大95%宽度
        const availableHorizontalSpace = maxGridAreaWidth - gridConfig.PADDING * 2;

        // 基于可用空间重新计算格子大小
        const maxBlockSizeByWidth = Math.floor((availableHorizontalSpace - gridConfig.SPACING * (gridConfig.SIZE_X - 1)) / gridConfig.SIZE_X);
        const maxBlockSizeByHeight = Math.floor((availableVerticalSpace - gridConfig.SPACING * (gridConfig.SIZE_Y - 1)) / gridConfig.SIZE_Y);

        // 取较小值确保不超出屏幕
        const newBlockSize = Math.min(maxBlockSizeByWidth, maxBlockSizeByHeight, this.blockSize);

        console.log(`🔧 自适应调整结果:`);
        console.log(`   可用水平空间: ${availableHorizontalSpace}px`);
        console.log(`   可用垂直空间: ${availableVerticalSpace}px`);
        console.log(`   原格子大小: ${this.blockSize}px`);
        console.log(`   新格子大小: ${newBlockSize}px`);

        if (newBlockSize < this.blockSize) {
            this.blockSize = Math.max(newBlockSize, 20); // 最小20px
            console.log(`✅ 格子大小已调整为: ${this.blockSize}px`);

            // 重新验证边界
            setTimeout(() => {
                this.validateGridBounds();
            }, 50);
        } else {
            console.log(`⚠️ 无法通过调整格子大小解决超出问题`);
        }
    }

    // 初始化性能监控
    initPerformanceMonitoring() {
        this.performanceStats = {
            frameCount: 0,
            lastFpsTime: Date.now(),
            fps: 60,
            updateTime: 0,
            renderTime: 0,
            particleCount: 0,
            animationCount: 0
        };

        // 每秒更新一次性能统计
        setInterval(() => {
            this.updatePerformanceStats();
        }, 1000);
    }

    // 更新性能统计
    updatePerformanceStats() {
        const now = Date.now();
        const deltaTime = now - this.performanceStats.lastFpsTime;

        if (deltaTime >= 1000) {
            this.performanceStats.fps = Math.round((this.performanceStats.frameCount * 1000) / deltaTime);
            this.performanceStats.frameCount = 0;
            this.performanceStats.lastFpsTime = now;

            // 统计当前动画数量
            this.performanceStats.particleCount = this.particles.length + this.sparkles.length;
            this.performanceStats.animationCount = this.floatingTexts.length;

            // 如果性能较差，自动优化
            if (this.performanceStats.fps < 30) {
                this.autoOptimizePerformance();
            }

            // 减少性能日志输出频率
            if (this.gameManager && this.gameManager.debug && this.performanceStats.fps < 45) {
                console.log(`🎮 性能警告: FPS=${this.performanceStats.fps}`);
            }
        }
    }

    // 智能性能优化（分级处理）
    autoOptimizePerformance() {
        const currentFps = this.performanceStats.fps;

        if (currentFps < 20) {
            // 极端情况：启用超快速模式
            this.ultraFastMode = true;
            this.fastEliminationMode = true;
            this.lowPerformanceMode = true;

            // 极严格限制
            if (this.particles.length > 5) {
                this.particles = this.particles.slice(-5);
            }
            if (this.sparkles.length > 2) {
                this.sparkles = this.sparkles.slice(-2);
            }
            if (this.floatingTexts.length > 2) {
                this.floatingTexts = this.floatingTexts.slice(-2);
            }

            if (!this.ultraOptimizationLogged) {
                console.log('🔧 启用超快速模式（FPS<20）');
                this.ultraOptimizationLogged = true;
            }
        } else if (currentFps < 35) {
            // 中等情况：启用快速模式，保留部分特效
            this.fastEliminationMode = true;
            this.lowPerformanceMode = true;
            this.ultraFastMode = false; // 不启用超快速

            // 适中限制
            if (this.particles.length > 8) {
                this.particles = this.particles.slice(-8);
            }
            if (this.sparkles.length > 3) {
                this.sparkles = this.sparkles.slice(-3);
            }
            if (this.floatingTexts.length > 3) {
                this.floatingTexts = this.floatingTexts.slice(-3);
            }

            if (!this.fastOptimizationLogged) {
                console.log('🔧 启用快速模式（FPS<35）');
                this.fastOptimizationLogged = true;
            }
        } else if (currentFps < 50) {
            // 轻微优化：只启用低性能模式
            this.lowPerformanceMode = true;
            this.fastEliminationMode = false;
            this.ultraFastMode = false;

            // 轻微限制
            if (this.particles.length > 12) {
                this.particles = this.particles.slice(-12);
            }
            if (this.sparkles.length > 5) {
                this.sparkles = this.sparkles.slice(-5);
            }

            if (!this.lightOptimizationLogged) {
                console.log('🔧 启用轻度优化（FPS<50）');
                this.lightOptimizationLogged = true;
            }
        }
    }

    // 应用高性能配置
    applyHighPerformanceConfig() {
        const config = GamePageCore.getConfig();
        const perfConfig = config.PERFORMANCE;

        if (perfConfig && perfConfig.HIGH_FPS_MODE) {
            // 启用适度的性能优化，保留游戏特性
            this.highFpsMode = true;
            this.balancedMode = true; // 新增平衡模式

            // 应用减少的数量限制
            this.maxParticles = perfConfig.MAX_PARTICLES || 10; // 减少数量
            this.maxSparkles = perfConfig.MAX_SPARKLES || 4; // 减少数量
            this.maxFloatingTexts = perfConfig.MAX_FLOATING_TEXTS || 4; // 减少数量

            // 启用渲染优化但保留特效
            this.skipComplexEffects = false; // 保留复杂特效
            this.simpleParticles = false; // 保留粒子特效
            this.skipShadows = false; // 保留阴影效果

            // 减少日志输出
            if (this.gameManager && this.gameManager.debug) {
                console.log('🚀 启用平衡性能模式');
            }
        }
    }

    // 获取配置（兼容多环境）
    static getConfig() {
        // 尝试从多个位置获取配置
        let config = null;
        let configSource = '';

        try {
            // 方式1：直接访问全局变量
            if (typeof GAME_CONFIG !== 'undefined') {
                config = GAME_CONFIG;
                configSource = '全局变量';
            }
            // 方式2：从 window 对象获取
            else if (typeof window !== 'undefined' && window.GAME_CONFIG) {
                config = window.GAME_CONFIG;
                configSource = 'window 对象';
            }
            // 方式3：从 globalThis 获取
            else if (typeof globalThis !== 'undefined' && globalThis.GAME_CONFIG) {
                config = globalThis.GAME_CONFIG;
                configSource = 'globalThis 对象';
            }
            // 方式4：从 global 获取
            else if (typeof global !== 'undefined' && global.GAME_CONFIG) {
                config = global.GAME_CONFIG;
                configSource = 'global 对象';
            }
        } catch (error) {
            console.warn('获取配置时出错:', error);
        }

        if (config) {
            console.log(`配置检查: 已加载（从${configSource}）`);
            return config;
        }

        console.log('配置检查: 未加载，配置文件可能未正确加载');
        console.error('GAME_CONFIG 全局变量未找到，请检查 config.js 是否正确加载');

        // 抛出错误而不是返回硬编码配置，确保配置问题能被及时发现
        throw new Error('游戏配置未加载，请确保 config.js 文件已正确加载');
    }
    
    constructor(gameManager, levelConfig) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;

        // 获取逻辑尺寸用于布局计算（而不是物理像素）
        this.logicalWidth = gameManager.logicalWidth || this.canvas.width;
        this.logicalHeight = gameManager.logicalHeight || this.canvas.height;
        this.pixelRatio = gameManager.pixelRatio || 1;

        console.log(`🖥️ 屏幕信息:`);
        console.log(`   物理像素: ${this.canvas.width} x ${this.canvas.height}`);
        console.log(`   逻辑像素: ${this.logicalWidth} x ${this.logicalHeight}`);
        console.log(`   像素比: ${this.pixelRatio}`);
        
        // 设置关卡配置
        this.levelConfig = levelConfig || {
            targetScore: 1000,
            name: '萌宠新手村',
            level: 1
        };
        
        // 初始化游戏数据
        this.score = 0;

        // 从配置中获取游戏参数
        this.targetScore = this.levelConfig.targetScore;
        this.levelName = this.levelConfig.name;
        this.level = this.levelConfig.level;

        // 将分数和进度设为全局变量方便直接计算和赋值（兼容小程序环境）
        this.setGlobalGameData('gameScore', this.score);
        this.setGlobalGameData('gameTargetScore', this.targetScore);
        this.setGlobalGameData('gameProgress', 0);
        
        // 网格配置（使用全局配置和动态计算）
        const config = GamePageCore.getConfig();
        this.gridSizeX = config.GRID.SIZE_X;
        this.gridSizeY = config.GRID.SIZE_Y;

        // 动态计算格子大小（确保响应式布局）
        // config 已在上面定义，直接使用

        // 尝试多种方式获取 CONFIG_UTILS
        let configUtils = null;
        try {
            if (typeof window !== 'undefined' && window.CONFIG_UTILS) {
                configUtils = window.CONFIG_UTILS;
            } else if (typeof globalThis !== 'undefined' && globalThis.CONFIG_UTILS) {
                configUtils = globalThis.CONFIG_UTILS;
            } else if (typeof global !== 'undefined' && global.CONFIG_UTILS) {
                configUtils = global.CONFIG_UTILS;
            } else {
                // 最后尝试从当前作用域获取
                try {
                    configUtils = eval('typeof CONFIG_UTILS !== "undefined" ? CONFIG_UTILS : null');
                } catch (e) {
                    configUtils = null;
                }
            }
        } catch (error) {
            console.warn('获取 CONFIG_UTILS 时出错:', error);
            configUtils = null;
        }

        if (configUtils) {
            const gridConfig = configUtils.getGridConfig(this.logicalWidth, this.logicalHeight);
            this.blockSize = gridConfig.blockSize;
            console.log(`✅ 动态计算格子大小: ${this.blockSize}px (逻辑屏幕: ${this.logicalWidth}x${this.logicalHeight})`);
            console.log(`✅ 网格配置:`, gridConfig);
        } else {
            console.warn('⚠️ CONFIG_UTILS 未找到，使用简化的响应式计算');
            // 简化的响应式计算
            this.blockSize = this.calculateResponsiveBlockSize(this.logicalWidth, this.logicalHeight, config);
            console.log(`📱 响应式计算格子大小: ${this.blockSize}px (逻辑屏幕: ${this.logicalWidth}x${this.logicalHeight})`);
        }
        
        // 验证配置是否正确加载
        // config 已在上面定义，直接使用
        console.log('构造函数中的配置验证:', config ? '配置可用' : '配置不可用');
        if (config) {
            console.log('配置版本:', config.VERSION);
            console.log('网格配置:', config.GRID);
            console.log('萌宠配置:', config.ANIMALS);
        } else {
            throw new Error('游戏配置未加载，无法初始化游戏');
        }

        // 验证网格边界（在所有尺寸计算完成后）
        setTimeout(() => {
            if (!this.validateGridBounds()) {
                // 如果网格超出屏幕，尝试自适应调整
                this.adaptiveGridResize();
            }
        }, 100);

        // 初始化性能监控
        this.initPerformanceMonitoring();

        // 应用高性能配置
        this.applyHighPerformanceConfig();

        // 根据关卡配置萌宠类型
        this.initAnimalTypes();
        this.animalImages = {};
        this.propImages = {};

        // 使用全局配置的颜色（从 config.js 获取）
        this.animalColors = {};
        if (config.COLORS && config.COLORS.ANIMALS) {
            // 转换新格式到旧格式（兼容现有代码）
            for (const [animal, colorConfig] of Object.entries(config.COLORS.ANIMALS)) {
                this.animalColors[animal] = colorConfig.bg || colorConfig;
            }
        } else {
            throw new Error('萌宠颜色配置未找到');
        }

        // 特殊方块类型（从 config.js 获取）
        if (config.SPECIAL_BLOCKS && config.SPECIAL_BLOCKS.TYPES) {
            this.specialTypes = config.SPECIAL_BLOCKS.TYPES;
        } else {
            throw new Error('特殊方块类型配置未找到');
        }

        // 特殊方块颜色（从 config.js 获取）
        this.specialColors = {};
        if (config.COLORS && config.COLORS.SPECIAL) {
            // 转换新格式到旧格式（兼容现有代码）
            for (const [special, colorConfig] of Object.entries(config.COLORS.SPECIAL)) {
                this.specialColors[special] = colorConfig.bg || colorConfig;
            }
        } else {
            throw new Error('特殊方块颜色配置未找到');
        }
        
        // 拖拽相关状态
        this.isDragging = false;
        this.dragStartRow = -1;
        this.dragStartCol = -1;
        this.dragEndRow = -1;
        this.dragEndCol = -1;
        this.dragStartX = -1;
        this.dragStartY = -1;
        this.dragCurrentX = -1;
        this.dragCurrentY = -1;
        
        // 游戏状态
        this.score = 0;
        this.isAnimating = false;
        this.selectedBlock = null;
        this.isGameOver = false;
        this.isLevelComplete = false;
        this.reviveCount = 0;
        this.isBombCardSelecting = false;
        this.showExitDialog = false;
        
        // 计分规则（从 config.js 获取）
        if (config.SCORING) {
            this.scoreRules = {
                match3: config.SCORING.MATCH_3,
                match4: config.SCORING.MATCH_4,
                match5: config.SCORING.MATCH_5,
                rocketCombo: config.SCORING.ROCKET_COMBO,
                bombCombo: config.SCORING.BOMB_COMBO,
                rocketBombCombo: config.SCORING.ROCKET_BOMB_COMBO,
                bombBombCombo: config.SCORING.BOMB_BOMB_COMBO,
                comboMultipliers: config.SCORING.COMBO_MULTIPLIERS
            };
        } else {
            throw new Error('计分规则配置未找到');
        }

        // 连击系统
        this.combo = 0;
        this.maxCombo = 0;
        this.comboMultiplier = 1.0;
        this.comboCount = 0; // 连续消除次数
        this.comboTimer = 0; // 连击计时器
        
        // 动画和效果
        this.fallingBlocks = [];
        this.particles = [];
        this.sparkles = [];
        this.floatingTexts = [];
        this.animations = [];
        this.animationTime = 0;
        
        // 道具系统（从 config.js 获取默认数量）
        if (config.PROPS && config.PROPS.DEFAULT_COUNTS) {
            this.props = {
                refresh: config.PROPS.DEFAULT_COUNTS.refresh,
                bomb: config.PROPS.DEFAULT_COUNTS.bomb,
                clear: config.PROPS.DEFAULT_COUNTS.clear,
                levelDown: config.PROPS.DEFAULT_COUNTS.levelDown
            };
        } else {
            throw new Error('道具默认数量配置未找到');
        }

        // 道具使用状态
        this.propUsing = {
            type: null,     // 当前使用的道具类型
            isActive: false // 是否正在使用道具
        };

        // 降级卡状态
        this.levelDownUsed = false; // 是否已使用降级卡
        
        // 音效管理器
        this.audioManager = gameManager.audioManager;
        
        // 背景效果
        this.backgroundStars = null;
        
        // 动画控制器
        this.animator = null;

        // 事件处理器
        this.events = null;

        // 匹配器
        this.matcher = null;

        console.log('GamePageCore初始化完成');
    }

    // 安全设置全局游戏数据（兼容小程序环境）
    setGlobalGameData(key, value) {
        try {
            if (typeof window !== 'undefined') {
                window[key] = value;
            } else if (typeof globalThis !== 'undefined') {
                globalThis[key] = value;
            } else if (typeof global !== 'undefined') {
                global[key] = value;
            }
            // 同时存储在实例中作为备份
            if (!this.globalData) {
                this.globalData = {};
            }
            this.globalData[key] = value;
        } catch (error) {
            console.warn(`设置全局数据 ${key} 失败:`, error);
            // 至少存储在实例中
            if (!this.globalData) {
                this.globalData = {};
            }
            this.globalData[key] = value;
        }
    }

    // 安全获取全局游戏数据
    getGlobalGameData(key) {
        try {
            if (typeof window !== 'undefined' && window[key] !== undefined) {
                return window[key];
            } else if (typeof globalThis !== 'undefined' && globalThis[key] !== undefined) {
                return globalThis[key];
            } else if (typeof global !== 'undefined' && global[key] !== undefined) {
                return global[key];
            }
        } catch (error) {
            console.warn(`获取全局数据 ${key} 失败:`, error);
        }

        // 从实例备份中获取
        if (this.globalData && this.globalData[key] !== undefined) {
            return this.globalData[key];
        }

        return undefined;
    }

    /**
     * 更新分数并同步全局变量
     * @param {number} points - 要增加的分数
     */
    updateScore(points) {
        this.score += points;
        this.setGlobalGameData('gameScore', this.score);
        const progress = Math.min(this.score / this.targetScore, 1.0);
        this.setGlobalGameData('gameProgress', progress);
        console.log(`分数更新: +${points}, 总分: ${this.score}, 进度: ${(progress * 100).toFixed(1)}%`);

        // 检查是否达到胜利条件
        this.checkGameStatus();
    }

    /**
     * 重置分数并同步全局变量
     */
    resetScore() {
        this.score = 0;
        this.setGlobalGameData('gameScore', this.score);
        this.setGlobalGameData('gameProgress', 0);
        console.log('分数已重置');
    }

    // 根据关卡初始化萌宠类型（使用全局配置）
    initAnimalTypes() {
        console.log('开始初始化萌宠类型...');
        const config = GamePageCore.getConfig();
        console.log('获取到的配置:', config);

        const allAnimals = config.ANIMALS ? config.ANIMALS.TYPES :
            ['cat', 'dog', 'elephant', 'fox', 'frog', 'monkey', 'panda', 'rabbit', 'tiger'];
        console.log('可用萌宠类型:', allAnimals);

        // 使用关卡配置确定萌宠数量
        let animalCount;
        if (config.LEVELS) {
            const levelConfig = config.LEVELS.find(l => l.id === this.level);
            animalCount = levelConfig ? levelConfig.animalCount : 5;
            console.log(`从关卡配置获取萌宠数量: ${animalCount}`);
        } else {
            // 回退到原有逻辑
            switch (this.level) {
                case 1: animalCount = 5; break;
                case 2: animalCount = 7; break;
                case 3:
                default: animalCount = 9; break;
            }
            console.log(`使用默认萌宠数量: ${animalCount}`);
        }

        // 应用降级卡效果（如果已使用）
        if (this.levelDownUsed) {
            animalCount = Math.max(3, animalCount - 1); // 最少保留3种萌宠
            console.log(`降级卡已使用，萌宠数量减少到: ${animalCount}`);
        }

        // 随机选择指定数量的萌宠类型
        this.animalTypes = allAnimals.slice(0, animalCount);
        console.log(`关卡${this.level}使用${animalCount}种萌宠:`, this.animalTypes);

        if (!this.animalTypes || this.animalTypes.length === 0) {
            console.error('萌宠类型初始化失败！');
            // 强制设置默认萌宠类型
            this.animalTypes = ['cat', 'dog', 'lion', 'fox', 'frog'];
            console.log('使用强制默认萌宠类型:', this.animalTypes);
        }
    }

    // 获取格子间距
    getSpacing() {
        const config = GamePageCore.getConfig();
        return config.GRID ? config.GRID.SPACING : 2;
    }

    /**
     * 公用的格子填充方法
     * @param {Array} positions - 需要填充的位置数组 [{row, col}, ...]
     * @param {Object} options - 填充选项
     * @param {string} options.fillOrder - 填充顺序: 'BOTTOM_UP'(从下往上) 或 'TOP_DOWN'(从上往下)
     * @param {number} options.startRow - 新格子的起始行（默认为-1，即网格上方）
     * @param {number} options.fallSpeed - 下落速度（默认使用falling模块的速度）
     * @returns {Array} 创建的新格子数组
     */
    fillBlocks(positions, options = {}) {
        if (!positions || positions.length === 0) {
            return [];
        }

        const {
            fillOrder = 'BOTTOM_UP',
            startRow = -1,
            fallSpeed = null
        } = options;

        console.log(`公用填充方法: 填充${positions.length}个位置, 顺序=${fillOrder}`);

        // 根据填充顺序排序位置
        let sortedPositions;
        if (fillOrder === 'BOTTOM_UP') {
            // 从下往上：按行号降序排列
            sortedPositions = [...positions].sort((a, b) => b.row - a.row);
        } else {
            // 从上往下：按行号升序排列
            sortedPositions = [...positions].sort((a, b) => a.row - b.row);
        }

        const newBlocks = [];
        const spacing = this.getSpacing();

        sortedPositions.forEach((pos, index) => {
            const { row, col } = pos;

            // 创建新格子（传递正确的起始位置）
            const newBlock = this.createRandomBlock(row, col, this.gridStartX, this.gridStartY);
            if (newBlock) {
                // 设置初始位置（在网格上方）
                newBlock.y = this.gridStartY + startRow * (this.blockSize + spacing);
                newBlock.targetY = this.gridStartY + row * (this.blockSize + spacing);
                newBlock.isFalling = true;
                newBlock.targetRow = row;
                newBlock.isNewBlock = true;

                // 设置下落速度
                if (fallSpeed !== null) {
                    newBlock.fallVelocity = fallSpeed;
                } else if (this.falling) {
                    newBlock.fallVelocity = this.falling.fallSpeed;
                } else {
                    newBlock.fallVelocity = 5; // 默认速度
                }

                // 添加到网格和下落列表
                this.grid[row][col] = newBlock;
                if (this.falling && this.falling.fallingBlocks) {
                    this.falling.fallingBlocks.push(newBlock);
                }

                newBlocks.push(newBlock);

                console.log(`填充位置(${row},${col}): 起始Y=${newBlock.y}, 目标Y=${newBlock.targetY}`);
            }
        });

        console.log(`公用填充完成: 创建了${newBlocks.length}个新格子`);
        return newBlocks;
    }
    
    // 初始化游戏网格
    initGrid() {
        console.log('开始初始化游戏网格');
        console.log('当前萌宠类型:', this.animalTypes);

        this.grid = [];

        // 使用新的布局配置
        const startX = this.gridStartX;
        const startY = this.gridStartY;

        console.log(`网格配置: ${this.gridSizeX}x${this.gridSizeY}, 起始位置: (${startX}, ${startY})`);

        let successCount = 0;
        let failCount = 0;

        for (let row = 0; row < this.gridSizeY; row++) {
            this.grid[row] = [];
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.createRandomBlock(row, col, startX, startY);
                if (block) {
                    successCount++;
                } else {
                    failCount++;
                    console.error(`创建方块失败: row=${row}, col=${col}`);
                }
                this.grid[row][col] = block;
            }
        }

        console.log(`游戏网格初始化完成: 成功创建${successCount}个方块, 失败${failCount}个`);

        if (failCount > 0) {
            console.error('网格初始化存在失败的方块，这可能导致渲染问题');
        }

        this.removeInitialMatches();
    }
    
    // 创建随机方块（增强版本）
    createRandomBlock(row = 0, col = 0, startX = 0, startY = 0, forceType = null) {
        if (!this.animalTypes || this.animalTypes.length === 0) {
            console.error('animalTypes未初始化，当前值:', this.animalTypes);
            console.error('无法创建方块 at row:', row, 'col:', col);
            return null;
        }

        let type, color, blockType;

        if (forceType) {
            // 强制指定类型（用于生成特殊方块）
            type = forceType;
            if (forceType === 'rocket' || forceType === 'bomb' ||
                forceType === 'rocket_horizontal' || forceType === 'rocket_vertical' ||
                forceType === 'bomb_extra') {
                blockType = 'special';
                color = this.specialColors[forceType] || this.specialColors['rocket'] || '#FF4500';
            } else {
                blockType = 'normal';
                color = this.animalColors[forceType] || '#FF6B9D';
            }
        } else {
            // 普通随机生成
            type = this.animalTypes[Math.floor(Math.random() * this.animalTypes.length)];
            blockType = 'normal';
            color = this.animalColors[type] || '#FF6B9D';
        }

        return {
            // 基本属性
            type: type,
            color: color,

            // 格子分类：'normal' 或 'special'
            category: blockType,
            blockType: blockType,  // 添加blockType属性，确保交换检查能找到

            // 特殊格子类型：null, 'rocket', 'bomb' 等
            specialType: blockType === 'special' ? type : null,

            // 位置信息（考虑间距）
            x: startX + col * (this.blockSize + this.getSpacing()),
            y: startY + row * (this.blockSize + this.getSpacing()),
            row: row,
            col: col,

            // 动画属性
            scale: 1,
            alpha: 1,
            rotation: 0,
            animationOffset: Math.random() * Math.PI * 2,

            // 状态
            isSelected: false,
            isMatched: false,
            isFalling: false,

            // 特效
            glowIntensity: 0,
            pulsePhase: Math.random() * Math.PI * 2
        };
    }

    // 创建特殊方块
    createSpecialBlock(row, col, startX, startY, specialType) {
        return this.createRandomBlock(row, col, startX, startY, specialType);
    }
    
    // 移除初始匹配 - 修复版，使用matcher统一检查
    removeInitialMatches() {
        let hasMatches = true;
        let attempts = 0;
        const maxAttempts = 100;

        console.log('开始移除初始匹配...');

        while (hasMatches && attempts < maxAttempts) {
            // 使用matcher统一检查匹配
            const matchResult = this.matcher ? this.matcher.findAllMatches() : this.checkForMatches();

            if (!matchResult || matchResult.matches.length === 0) {
                hasMatches = false;
                console.log('没有发现初始匹配');
            } else {
                console.log(`发现${matchResult.matches.length}个初始匹配，开始替换`);

                // 替换匹配的方块
                matchResult.matches.forEach(match => {
                    let newType;
                    let validType = false;
                    let typeAttempts = 0;

                    // 尝试找到一个不会产生新匹配的类型
                    while (!validType && typeAttempts < 20) {
                        newType = this.animalTypes[Math.floor(Math.random() * this.animalTypes.length)];

                        // 临时设置新类型
                        const originalType = this.grid[match.row][match.col].type;
                        this.grid[match.row][match.col].type = newType;
                        this.grid[match.row][match.col].color = this.animalColors[newType];

                        // 检查是否还会产生匹配
                        if (!this.wouldCreateMatch(match.row, match.col, newType)) {
                            validType = true;
                        } else {
                            // 恢复原类型继续尝试
                            this.grid[match.row][match.col].type = originalType;
                            typeAttempts++;
                        }
                    }

                    // 如果找不到合适的类型，使用随机类型
                    if (!validType) {
                        const newBlock = this.createRandomBlock(
                            match.row,
                            match.col,
                            this.gridStartX,
                            this.gridStartY
                        );
                        this.grid[match.row][match.col] = newBlock;
                    }
                });
            }
            attempts++;
        }

        if (attempts >= maxAttempts) {
            console.warn('移除初始匹配达到最大尝试次数');
        }

        console.log(`初始化完成，尝试${attempts}次移除匹配`);
    }

    // 检查指定位置放置指定类型是否会产生匹配
    wouldCreateMatch(row, col, type) {
        // 检查水平方向
        let horizontalCount = 1;

        // 向左检查
        for (let c = col - 1; c >= 0; c--) {
            if (this.grid[row][c] && this.grid[row][c].type === type && this.grid[row][c].blockType === 'normal') {
                horizontalCount++;
            } else {
                break;
            }
        }

        // 向右检查
        for (let c = col + 1; c < this.gridSizeX; c++) {
            if (this.grid[row][c] && this.grid[row][c].type === type && this.grid[row][c].blockType === 'normal') {
                horizontalCount++;
            } else {
                break;
            }
        }

        if (horizontalCount >= 3) return true;

        // 检查垂直方向
        let verticalCount = 1;

        // 向上检查
        for (let r = row - 1; r >= 0; r--) {
            if (this.grid[r][col] && this.grid[r][col].type === type && this.grid[r][col].blockType === 'normal') {
                verticalCount++;
            } else {
                break;
            }
        }

        // 向下检查
        for (let r = row + 1; r < this.gridSizeY; r++) {
            if (this.grid[r][col] && this.grid[r][col].type === type && this.grid[r][col].blockType === 'normal') {
                verticalCount++;
            } else {
                break;
            }
        }

        if (verticalCount >= 3) return true;

        return false;
    }

    // 道具系统方法

    // 使用刷新卡
    useRefreshProp() {
        if (this.props.refresh <= 0) {
            console.log('❌ 刷新卡数量不足');
            return false;
        }

        this.props.refresh--;

        // 简单高效的刷新动画
        this.executeSimpleRefresh();

        console.log('🔄 使用刷新卡，剩余数量:', this.props.refresh);
        return true;
    }

    // 简单高效的刷新动画
    executeSimpleRefresh() {
        console.log('🔄 刷新卡：开始简单刷新');

        // 设置动画状态
        this.isAnimating = true;

        // 播放刷新音效
        if (this.gameManager && this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('shua');
        }

        // 添加简单的闪烁效果
        this.addSimpleFlashEffect();

        // 500ms后重新生成网格
        setTimeout(() => {
            this.regenerateGrid();
        }, 500);
    }

    // 添加简单的闪烁效果
    addSimpleFlashEffect() {
        // 为所有方块添加闪烁效果
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block) {
                    block.isFlashing = true;
                    block.flashStartTime = Date.now();
                    block.flashDuration = 500; // 500ms闪烁
                }
            }
        }
    }

    // 重新生成网格
    regenerateGrid() {
        console.log('🔄 刷新卡：重新生成网格');

        // 收集所有现有方块的类型
        const allTypes = [];
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block && block.type) {
                    allTypes.push(block.type);
                }
            }
        }

        // 打乱类型数组
        for (let i = allTypes.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [allTypes[i], allTypes[j]] = [allTypes[j], allTypes[i]];
        }

        // 重新分配到网格
        let typeIndex = 0;
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (typeIndex < allTypes.length) {
                    const newBlock = this.createRandomBlock(row, col, this.gridStartX, this.gridStartY, allTypes[typeIndex]);
                    if (newBlock) {
                        // 添加出现动画
                        newBlock.scale = 0.5;
                        newBlock.alpha = 0.7;
                        newBlock.isAppearing = true;
                        newBlock.appearStartTime = Date.now() + (row + col) * 30; // 错开出现时间

                        this.grid[row][col] = newBlock;
                        typeIndex++;
                    }
                }
            }
        }

        // 移除初始匹配
        setTimeout(() => {
            this.removeInitialMatches();
            this.isAnimating = false;
            console.log('🔄 刷新卡：简单刷新完成');
        }, 800); // 等待出现动画完成
    }

    // 使用清屏卡
    useClearProp() {
        console.log('✨ 清屏卡：开始清空网格');

        if (this.props.clear <= 0) {
            console.log('❌ 清屏卡数量不足');
            return false;
        }

        this.props.clear--;
        let clearedCount = 0;
        const eliminatedBlocks = [];

        // 清空所有网格，记录被清除的方块位置
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block) {
                    // 添加消失特效
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                    clearedCount++;
                    // 记录被清除的位置
                    eliminatedBlocks.push({ row, col, block });
                }
                this.grid[row][col] = null;
            }
        }

        // 增加分数
        this.updateScore(800);
        this.addFloatingText('+800', this.logicalWidth / 2, this.logicalHeight / 2, '#FFD700');

        // 使用与其他消除效果相同的掉落填充机制，并检测连锁匹配
        setTimeout(async () => {
            if (this.animator && this.animator.falling) {
                const removedPositions = eliminatedBlocks.map(item => ({ row: item.row, col: item.col }));
                console.log('✨ 清屏卡：使用掉落系统重新填充网格');

                // 等待掉落完成
                await this.animator.falling.processFalling(removedPositions);
                console.log('✨ 清屏卡：掉落完成，检查连锁匹配');

                // 检查掉落后是否有新的匹配
                this.checkAndProcessCascadeMatches('清屏卡');
            } else {
                // 回退方案：使用原有方法
                this.refillGrid();
                console.log('✨ 清屏卡：使用回退方法填充网格');
            }
        }, 300); // 与炸弹卡保持相同的延迟时间

        console.log(`✨ 清屏卡：清空${clearedCount}个方块，获得800分`);
        return true;
    }

    // 使用降级卡
    useLevelDownProp() {
        if (this.props.levelDown <= 0) {
            console.log('❌ 降级卡数量不足');
            return false;
        }

        if (this.levelDownUsed) {
            console.log('❌ 降级卡在本局中已使用过');
            return false;
        }

        this.props.levelDown--;
        this.levelDownUsed = true;

        console.log('⬇️ 降级卡：开始减少萌宠类型');

        // 播放降级卡音效
        if (this.gameManager && this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('shua');
        }

        // 重新初始化萌宠类型（会应用降级效果）
        this.initAnimalTypes();

        // 刷新网格（使用简单刷新动画）
        this.executeSimpleRefresh();

        console.log('⬇️ 使用降级卡，剩余数量:', this.props.levelDown);
        return true;
    }

    // 激活炸弹卡（需要用户选择位置）
    activateBombProp() {
        if (this.props.bomb <= 0) {
            console.log('炸弹卡数量不足');
            return false;
        }

        this.propUsing.type = 'bomb';
        this.propUsing.isActive = true;

        console.log('炸弹卡已激活，请选择爆炸位置');
        return true;
    }

    // 使用炸弹卡在指定位置
    useBombPropAt(row, col) {
        console.log(`💣 炸弹卡：尝试在位置(${row}, ${col})爆炸`);

        if (!this.propUsing.isActive || this.propUsing.type !== 'bomb') {
            console.log('❌ 炸弹卡使用失败: 道具未激活');
            return false;
        }

        if (this.props.bomb <= 0) {
            console.log('❌ 炸弹卡数量不足');
            return false;
        }

        this.props.bomb--;
        this.propUsing.isActive = false;
        this.propUsing.type = null;

        // 5x5范围爆炸
        const eliminatedBlocks = this.eliminateArea(row, col, 5);

        // 增加分数
        this.updateScore(500);
        this.addFloatingText('+500', col * this.blockSize + this.gridStartX + this.blockSize/2,
                           row * this.blockSize + this.gridStartY + this.blockSize/2, '#FFD700');

        // 播放爆炸音效和特效
        if (this.gameManager && this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('bomb');
        }
        this.addExplosionEffect(col * this.blockSize + this.gridStartX + this.blockSize/2,
                              row * this.blockSize + this.gridStartY + this.blockSize/2, true);

        // 清除被消除的方块
        eliminatedBlocks.forEach(item => {
            if (this.grid[item.row] && this.grid[item.row][item.col]) {
                this.grid[item.row][item.col] = null;
            }
        });

        // 处理掉落并检测连锁匹配
        setTimeout(async () => {
            if (this.animator && this.animator.falling) {
                const removedPositions = eliminatedBlocks.map(item => ({ row: item.row, col: item.col }));
                console.log('💣 炸弹卡：开始掉落处理');

                // 等待掉落完成
                await this.animator.falling.processFalling(removedPositions);
                console.log('💣 炸弹卡：掉落完成，检查连锁匹配');

                // 检查掉落后是否有新的匹配
                this.checkAndProcessCascadeMatches('炸弹卡');
            }
        }, 300);

        console.log(`💣 炸弹卡：爆炸消除${eliminatedBlocks.length}个方块，获得500分`);
        return true;
    }

    // 重新填充网格
    refillGrid() {
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (!this.grid[row][col]) {
                    const newBlock = this.createRandomBlock(row, col, this.gridStartX, this.gridStartY);
                    this.grid[row][col] = newBlock;
                }
            }
        }

        // 确保没有初始匹配
        this.removeInitialMatches();
    }
    
    // 初始化背景效果
    initBackgroundEffects() {
        try {
            if (typeof BackgroundUtils !== 'undefined') {
                this.backgroundStars = BackgroundUtils.createStars(15, this.logicalWidth, this.logicalHeight);
            } else {
                console.warn('BackgroundUtils未加载，使用简化背景');
                this.backgroundStars = null;
            }
        } catch (error) {
            console.warn('背景效果初始化失败:', error);
            this.backgroundStars = null;
        }
    }
    
    // 创建图片对象（兼容小程序环境）
    createImage() {
        try {
            // 小程序环境
            if (typeof tt !== 'undefined' && tt.createImage) {
                return tt.createImage();
            }
            // 浏览器环境
            else if (typeof Image !== 'undefined') {
                return new Image();
            }
            // 都不支持时返回 null
            else {
                console.warn('当前环境不支持图片加载');
                return null;
            }
        } catch (error) {
            console.warn('创建图片对象失败:', error);
            return null;
        }
    }

    // 加载萌宠图片
    loadAnimalImages() {
        console.log('开始加载萌宠图片...');

        // 检查是否支持图片加载
        if (!this.createImage()) {
            console.warn('当前环境不支持图片加载，跳过图片初始化');
            return;
        }

        // 加载萌宠图片
        this.animalTypes.forEach(type => {
            const img = this.createImage();
            if (!img) return;

            img.src = `images/animal/${type}.png`;
            img.onload = () => {
                console.log(`萌宠${type}图片加载完成`);
            };
            img.onerror = () => {
                console.warn(`萌宠${type}图片加载失败，使用默认颜色`);
            };
            this.animalImages[type] = img;
        });
        
        // 加载特殊方块图片
        const specialTypes = [
            { key: 'rocket', path: 'images/extra/rocket.png' },
            { key: 'rocket_horizontal', path: 'images/extra/rocket.png' },
            { key: 'rocket_vertical', path: 'images/extra/rocket.png' },
            { key: 'bomb_extra', path: 'images/extra/bomb.png' }
        ];
        specialTypes.forEach(item => {
            const img = this.createImage();
            if (!img) return;

            img.src = item.path;
            img.onload = () => {
                console.log(`特殊方块${item.key}图片加载完成`);
            };
            img.onerror = () => {
                console.warn(`特殊方块${item.key}图片加载失败，使用默认图标`);
            };
            this.propImages[item.key] = img;
        });

        // 加载道具图片（使用配置）
        const config = GamePageCore.getConfig();
        if (config.PROPS && config.PROPS.IMAGES) {
            // 使用配置中的图片路径
            Object.keys(config.PROPS.IMAGES).forEach(type => {
                const img = this.createImage();
                if (!img) return;

                img.src = config.PROPS.IMAGES[type];
                img.onload = () => {
                    console.log(`道具${type}图片加载完成: ${img.src}`);
                };
                img.onerror = () => {
                    console.warn(`道具${type}图片加载失败: ${img.src}，使用默认图标`);
                };
                this.propImages[type] = img;
            });
        } else {
            // 回退到默认配置
            const propTypes = ['refresh', 'bomb', 'clear', 'levelDown'];
            propTypes.forEach(type => {
                const img = this.createImage();
                if (!img) return;

                img.src = `images/prop/${type}.png`;
                img.onload = () => {
                    console.log(`道具${type}图片加载完成（默认路径）`);
                };
                img.onerror = () => {
                    console.warn(`道具${type}图片加载失败（默认路径），使用默认图标`);
                };
                this.propImages[type] = img;
            });
        }
    }
    
    // 检查匹配（增强版本）- 支持3连消、4连消、5连消
    checkForMatches() {
        const matches = [];
        const visited = new Set();
        const matchGroups = []; // 存储匹配组信息

        // 检查水平匹配
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX - 2; col++) {
                const block = this.grid[row][col];
                if (!block || block.blockType !== 'normal' || !block.type) {
                    console.log(`跳过方块 (${row},${col}): ${!block ? '空' : block.blockType !== 'normal' ? '非普通' : '无type'}`);
                    continue;
                }

                const type = block.type;
                const block1 = this.grid[row][col + 1];
                const block2 = this.grid[row][col + 2];

                if (block1?.type === type && block1?.blockType === 'normal' &&
                    block2?.type === type && block2?.blockType === 'normal') {

                    let count = 3;
                    let endCol = col + 2;

                    // 扩展匹配
                    while (endCol + 1 < this.gridSizeX) {
                        const nextBlock = this.grid[row][endCol + 1];
                        if (nextBlock?.type === type && nextBlock?.blockType === 'normal') {
                            count++;
                            endCol++;
                        } else {
                            break;
                        }
                    }

                    // 记录匹配组
                    const matchGroup = {
                        type: 'horizontal',
                        count: count,
                        blocks: []
                    };

                    // 添加匹配的方块
                    for (let i = 0; i < count; i++) {
                        const key = `${row}-${col + i}`;
                        if (!visited.has(key)) {
                            const matchBlock = { row, col: col + i, type, count, matchType: 'horizontal' };
                            matches.push(matchBlock);
                            matchGroup.blocks.push(matchBlock);
                            visited.add(key);
                        }
                    }

                    matchGroups.push(matchGroup);
                    console.log(`检测到水平${count}连消: 行${row}, 列${col}-${endCol}, 类型${type}`);
                    col = endCol;
                }
            }
        }

        // 检查垂直匹配
        for (let col = 0; col < this.gridSizeX; col++) {
            for (let row = 0; row < this.gridSizeY - 2; row++) {
                const block = this.grid[row][col];
                if (!block || block.blockType !== 'normal' || !block.type) {
                    console.log(`跳过方块 (${row},${col}): ${!block ? '空' : block.blockType !== 'normal' ? '非普通' : '无type'}`);
                    continue;
                }

                const type = block.type;
                const block1 = this.grid[row + 1][col];
                const block2 = this.grid[row + 2][col];

                if (block1?.type === type && block1?.blockType === 'normal' &&
                    block2?.type === type && block2?.blockType === 'normal') {

                    let count = 3;
                    let endRow = row + 2;

                    // 扩展匹配
                    while (endRow + 1 < this.gridSizeY) {
                        const nextBlock = this.grid[endRow + 1][col];
                        if (nextBlock?.type === type && nextBlock?.blockType === 'normal') {
                            count++;
                            endRow++;
                        } else {
                            break;
                        }
                    }

                    // 记录匹配组
                    const matchGroup = {
                        type: 'vertical',
                        count: count,
                        blocks: []
                    };

                    // 添加匹配的方块
                    for (let i = 0; i < count; i++) {
                        const key = `${row + i}-${col}`;
                        if (!visited.has(key)) {
                            const matchBlock = { row: row + i, col, type, count, matchType: 'vertical' };
                            matches.push(matchBlock);
                            matchGroup.blocks.push(matchBlock);
                            visited.add(key);
                        }
                    }

                    matchGroups.push(matchGroup);
                    console.log(`检测到垂直${count}连消: 列${col}, 行${row}-${endRow}, 类型${type}`);
                    row = endRow;
                }
            }
        }

        return { matches, matchGroups };
    }
    
    // 处理匹配消除 - 支持新计分规则和特殊方块生成
    processMatches(matchResult) {
        if (!matchResult || matchResult.matches.length === 0) {
            // 没有匹配时重置连击
            this.resetCombo();
            return { score: 0, specialBlocks: [] };
        }

        const { matches, matchGroups } = matchResult;
        let totalScore = 0;
        const specialBlocks = [];

        // 增加连击数
        this.comboCount++;
        this.maxCombo = Math.max(this.maxCombo, this.comboCount);

        // 计算连击倍率
        const comboIndex = Math.min(this.comboCount - 1, this.scoreRules.comboMultipliers.length - 1);
        this.comboMultiplier = this.scoreRules.comboMultipliers[comboIndex];

        // 播放消除音效
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('so');
        }

        // 处理每个匹配组
        matchGroups.forEach((group, index) => {
            // 验证匹配组数据完整性
            if (!group || (!group.blocks && !group.length)) {
                console.error(`匹配组${index + 1}数据无效:`, group);
                return;
            }

            let baseScore;
            // 获取匹配数量，兼容不同的数据结构
            const count = group.count || group.length || (group.blocks ? group.blocks.length : 0);
            console.log(`处理匹配组${index + 1}: 类型${group.type}, 数量${count}, 结构:`, group);

            // 根据消除数量计算基础分数
            if (count === 3) {
                baseScore = this.scoreRules.match3;
            } else if (count === 4) {
                baseScore = this.scoreRules.match4;
                // 4连消生成火箭
                const blocks = group.blocks || [group];
                if (Array.isArray(blocks) && blocks.length > 0) {
                    const centerBlock = blocks[Math.floor(blocks.length / 2)];

                    // 所有火箭都是相同类型，都消除垂直列
                    const rocketType = 'rocket_vertical';

                    specialBlocks.push({
                        row: centerBlock.row,
                        col: centerBlock.col,
                        type: rocketType
                    });
                    console.log(`4连消生成火箭: 位置(${centerBlock.row}, ${centerBlock.col}), 类型: ${rocketType}`);
                }
            } else if (count >= 5) {
                baseScore = this.scoreRules.match5;
                // 5连消生成炸弹
                const blocks = group.blocks || [group];
                if (Array.isArray(blocks) && blocks.length > 0) {
                    const centerBlock = blocks[Math.floor(blocks.length / 2)];
                    specialBlocks.push({
                        row: centerBlock.row,
                        col: centerBlock.col,
                        type: 'bomb_extra'
                    });
                    console.log(`5连消生成炸弹: 位置(${centerBlock.row}, ${centerBlock.col}), 类型: bomb_extra`);
                }
            } else {
                // 默认分数，防止undefined
                baseScore = this.scoreRules.match3;
                console.warn(`未知的匹配数量: ${count}, 使用默认分数`);
            }

            const finalScore = Math.floor(baseScore * this.comboMultiplier);
            totalScore += finalScore;
            console.log(`消除组得分: 基础${baseScore} x 倍率${this.comboMultiplier} = ${finalScore}, 累计${totalScore}`);

            // 为每个方块添加特效（性能优化版）
            const blocks = group.blocks || [group]; // 兼容不同的数据结构
            if (Array.isArray(blocks)) {
                blocks.forEach(match => {
                    const block = this.grid[match.row][match.col];
                    if (block) {
                        // 标记为已匹配
                        block.isMatched = true;

                        // 添加到动画列表（仅在需要时）
                        this.addBlockAnimation(match.row, match.col, block);

                        // 在快速消除模式下大幅减少特效
                        if (this.fastEliminationMode) {
                            // 快速模式：只有20%概率显示粒子效果
                            if (Math.random() < 0.2) {
                                this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                            }
                        } else if (!this.lowPerformanceMode || Math.random() < 0.5) {
                            this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                        }
                    }
                });
            } else {
                console.error('group.blocks不是数组:', blocks, '完整group:', group);
            }

            // 添加浮动分数文字
            if (Array.isArray(blocks) && blocks.length > 0) {
                const centerBlock = blocks[Math.floor(blocks.length / 2)];
                const block = this.grid[centerBlock.row][centerBlock.col];
                if (block) {
                    this.addFloatingText(`+${baseScore}`, block.x + this.blockSize / 2, block.y + this.blockSize / 2, '#FFD700');
                }
            }
        });

        // 添加连击显示和音效
        if (this.comboCount > 1) {
            this.addComboText(this.comboCount, this.comboMultiplier);

            // 播放连击音效 - 只在特定连击数时播放一次
            if (this.gameManager.audioManager) {
                if (this.comboCount === 2) {
                    this.gameManager.audioManager.playSound('wa');
                    console.log('播放2连击音效: wa.mp3');
                } else if (this.comboCount === 5) {
                    this.gameManager.audioManager.playSound('good');
                    console.log('播放5连击音效: good.mp3');
                }
            }
        }

        // 移除过于频繁的可能移动检查，改为在合适的时机检查
        // 注释掉这个检查，因为它会在动画进行中误判游戏结束
        // setTimeout(() => {
        //     this.checkForPossibleMoves();
        // }, 500);

        return { score: totalScore, specialBlocks };
    }

    // 重置连击
    resetCombo() {
        this.comboCount = 0;
        this.comboMultiplier = 1.0;
        this.comboTimer = 0;
        console.log('连击重置');
    }

    // 检查是否还有可能的移动 - 修复版，更加保守
    checkForPossibleMoves() {
        if (this.isGameOver || this.isLevelComplete) {
            console.log('跳过可能移动检查：游戏已结束或已通关');
            return;
        }

        // 确保没有动画在进行
        if (this.isAnimating || (this.animator && this.animator.hasActiveAnimations())) {
            console.log('跳过可能移动检查：动画正在进行中');
            return;
        }

        console.log('=== 开始检查可能的移动 ===');

        let possibleMovesCount = 0;

        // 检查所有相邻位置的交换是否能产生匹配
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (!block || block.isRemoved) continue;

                // 检查右边的交换
                if (col < this.gridSizeX - 1) {
                    const rightBlock = this.grid[row][col + 1];
                    if (rightBlock && !rightBlock.isRemoved && this.canSwapAndMatch(row, col, row, col + 1)) {
                        possibleMovesCount++;
                        console.log(`找到可能的移动${possibleMovesCount}: (${row},${col}) <-> (${row},${col + 1})`);
                        // 找到一个可能的移动就足够了，游戏继续
                        console.log('找到可能的移动，游戏继续');
                        return;
                    }
                }

                // 检查下边的交换
                if (row < this.gridSizeY - 1) {
                    const bottomBlock = this.grid[row + 1][col];
                    if (bottomBlock && !bottomBlock.isRemoved && this.canSwapAndMatch(row, col, row + 1, col)) {
                        possibleMovesCount++;
                        console.log(`找到可能的移动${possibleMovesCount}: (${row},${col}) <-> (${row + 1},${col})`);
                        // 找到一个可能的移动就足够了，游戏继续
                        console.log('找到可能的移动，游戏继续');
                        return;
                    }
                }
            }
        }

        // 没有找到可能的移动，但要再次确认
        console.log('第一次检查没有找到可能的移动，进行二次确认...');

        // 延迟再次检查，确保不是临时状态
        setTimeout(() => {
            if (this.isGameOver || this.isLevelComplete || this.isAnimating) {
                return;
            }

            // 二次检查
            const hasMovesSecondCheck = this.matcher && this.matcher.hasPossibleMatches ?
                this.matcher.hasPossibleMatches() : false;

            if (!hasMovesSecondCheck) {
                console.log('二次确认：确实没有可能的移动，触发游戏结束');
                this.triggerGameOver();
            } else {
                console.log('二次确认：发现可能的移动，游戏继续');
            }
        }, 1000);
    }

    // 检查交换后是否能产生匹配
    canSwapAndMatch(row1, col1, row2, col2) {
        // 临时交换
        const temp = this.grid[row1][col1];
        this.grid[row1][col1] = this.grid[row2][col2];
        this.grid[row2][col2] = temp;

        // 检查是否有匹配
        const matchResult = this.checkForMatches();
        const hasMatch = matchResult && matchResult.matches.length > 0;

        // 交换回来
        this.grid[row2][col2] = this.grid[row1][col1];
        this.grid[row1][col1] = temp;

        return hasMatch;
    }

    // 触发游戏失败
    triggerGameOver() {
        this.isGameOver = true;

        // 播放失败音效
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('lose');
        }

        // 显示失败对话框
        setTimeout(() => {
            this.showGameOverDialog();
        }, 500);

        console.log('游戏失败');
    }

    // 显示游戏失败对话框
    showGameOverDialog() {
        this.showGameOverDialog = true;
        console.log('显示游戏失败对话框');
    }

    // 复活游戏（触发刷新卡效果但不消耗刷新卡）
    reviveGame() {
        console.log('复活游戏');
        this.isGameOver = false;
        this.showGameOverDialog = false;

        // 触发刷新卡效果但不消耗数量
        this.shuffleGrid();

        // 确保没有初始匹配
        this.removeInitialMatches();

        console.log('游戏复活成功');
    }

    // 打乱网格（刷新卡效果）
    shuffleGrid() {
        console.log('🔄 刷新卡：开始打乱网格');
        const blocks = [];

        // 收集所有方块
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (this.grid[row][col]) {
                    blocks.push(this.grid[row][col]);
                }
            }
        }

        // 打乱数组
        for (let i = blocks.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [blocks[i], blocks[j]] = [blocks[j], blocks[i]];
        }

        // 重新分配到网格
        let blockIndex = 0;
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (blockIndex < blocks.length) {
                    const block = blocks[blockIndex];
                    block.row = row;
                    block.col = col;
                    // 包含间距的位置计算
                    const spacing = this.getSpacing();
                    block.x = this.gridStartX + col * (this.blockSize + spacing);
                    block.y = this.gridStartY + row * (this.blockSize + spacing);
                    this.grid[row][col] = block;
                    blockIndex++;
                } else {
                    this.grid[row][col] = null;
                }
            }
        }

        console.log('🔄 刷新卡：网格打乱完成');
    }

    // 添加刷新动画效果 - 漩涡版本
    addRefreshAnimation() {
        console.log('🔄 开始漩涡刷新动画');

        // 计算网格中心点
        const centerX = this.gridStartX + (this.gridSizeX * this.blockSize) / 2;
        const centerY = this.gridStartY + (this.gridSizeY * this.blockSize) / 2;

        // 为所有方块添加漩涡动画
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block) {
                    // 计算方块到中心的距离和角度
                    const blockCenterX = block.x + this.blockSize / 2;
                    const blockCenterY = block.y + this.blockSize / 2;
                    const dx = blockCenterX - centerX;
                    const dy = blockCenterY - centerY;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const angle = Math.atan2(dy, dx);

                    // 添加漩涡动画属性
                    block.isVortexing = true;
                    block.vortexStartTime = Date.now();
                    block.vortexDuration = 1200; // 总动画时长增加到1200ms
                    block.vortexPhase = 'in'; // 'in' -> 'out'
                    block.originalX = block.x;
                    block.originalY = block.y;
                    block.vortexCenterX = centerX;
                    block.vortexCenterY = centerY;
                    block.vortexDistance = distance;
                    block.vortexAngle = angle;
                    block.vortexDelay = distance * 2; // 根据距离设置延迟，外圈后进入
                }
            }
        }

        // 添加漩涡中心效果
        this.addVortexEffect(centerX, centerY);

        // 播放刷新音效
        if (this.gameManager && this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('shua');
        }
    }

    // 添加漩涡中心效果
    addVortexEffect(centerX, centerY) {
        this.vortexEffect = {
            active: true,
            startTime: Date.now(),
            duration: 1200,
            centerX: centerX,
            centerY: centerY,
            maxRadius: Math.max(this.gridSizeX, this.gridSizeY) * this.blockSize / 2
        };
    }

    // 添加闪光效果
    addFlashEffect() {
        if (!this.flashEffect) {
            this.flashEffect = {
                active: true,
                startTime: Date.now(),
                duration: 200,
                intensity: 0.3
            };
        }
    }

    // 处理格子交换 - 简化版本：只负责交换，不检查匹配
    processSwap(block1, block2, row1, col1, row2, col2) {
        console.log(`交换处理: (${row1},${col1})[${block1.type}] ↔ (${row2},${col2})[${block2.type}]`);

        // 执行交换
        this.executeSwap(row1, col1, row2, col2);
        console.log('已执行交换');

        // 返回交换结果，匹配检查由调用者处理
        return {
            success: true,
            from: { row: row1, col: col1 },
            to: { row: row2, col: col2 }
        };
    }

    // 检查交换后是否有匹配
    checkSwapForMatches(row1, col1, row2, col2) {
        // 检查两个位置是否有匹配
        const matches1 = this.matcher.findMatchesAt(row1, col1);
        const matches2 = this.matcher.findMatchesAt(row2, col2);

        const hasMatches = (matches1 && matches1.length >= 3) || (matches2 && matches2.length >= 3);

        console.log(`交换匹配检查: 位置1(${row1},${col1})=${matches1?.length || 0}个匹配, 位置2(${row2},${col2})=${matches2?.length || 0}个匹配`);

        return hasMatches;
    }

    // 处理两个特殊格子的组合效果（在checkAndProcessBlock中调用）
    processTwoSpecialBlocks(block1, block2, row1, col1, row2, col2) {
        console.log(`两个特殊格子组合: ${block1.specialType || block1.special} + ${block2.specialType || block2.special}`);

        const type1 = block1.specialType || block1.special;
        const type2 = block2.specialType || block2.special;
        let eliminatedBlocks = [];
        let score = 0;

        // 火箭 + 火箭 = 十字消除
        if (type1 === 'rocket' && type2 === 'rocket') {
            console.log('火箭+火箭: 十字消除');
            eliminatedBlocks = this.eliminateCross(row1, col1);
            score = 200;
            this.playSound('shua');
        }

        // 火箭 + 炸弹 = 3列消除
        else if ((type1 === 'rocket' && type2 === 'bomb') || (type1 === 'bomb' && type2 === 'rocket')) {
            console.log('火箭+炸弹: 3列消除');
            const centerCol = Math.floor((col1 + col2) / 2);
            eliminatedBlocks = this.eliminateThreeColumns(centerCol);
            score = 300;
            this.playSound('bomb');
        }

        // 炸弹 + 炸弹 = 5x5爆炸
        else if (type1 === 'bomb' && type2 === 'bomb') {
            console.log('炸弹+炸弹: 5x5爆炸');
            const centerRow = Math.floor((row1 + row2) / 2);
            const centerCol = Math.floor((col1 + col2) / 2);
            eliminatedBlocks = this.eliminateArea(centerRow, centerCol, 5);
            score = 500;
            this.playSound('bomb');
            this.addExplosionEffect(centerCol, centerRow, true);
        }

        console.log(`两个特殊格子组合完成: 消除${eliminatedBlocks.length}个格子, 得分${score}`);
        return { eliminatedBlocks, score };
    }

    // 检查格子是否有效果（只检查，不执行）
    checkBlockForEffect(block, row, col) {
        console.log(`检查格子效果: (${row},${col})[${block.type}]`);

        // 默认结果
        const defaultResult = {
            hasEffect: false,
            effectType: null,
            matchCount: 0
        };

        // 如果格子为空，直接返回
        if (!block) {
            console.log('格子为空，无效果');
            return defaultResult;
        }

        // 1. 检查是否为特殊格子
        if (block.category === 'special' || block.blockType === 'special') {
            const specialType = block.specialType || block.special;
            console.log(`特殊格子: ${specialType} → 有效果`);

            // 检查是否有相邻特殊格子
            const neighbors = this.getAdjacentBlocks(row, col);
            for (const neighbor of neighbors) {
                if (neighbor && (neighbor.category === 'special' || neighbor.blockType === 'special')) {
                    console.log(`发现相邻特殊格子组合: ${specialType} + ${neighbor.specialType || neighbor.special}`);
                    return {
                        hasEffect: true,
                        effectType: 'special_combo',
                        specialType1: specialType,
                        specialType2: neighbor.specialType || neighbor.special,
                        neighbor: neighbor
                    };
                }
            }

            // 单个特殊格子效果
            return {
                hasEffect: true,
                effectType: 'special_single',
                specialType: specialType
            };
        }

        // 2. 普通格子：检查是否有匹配
        const matches = this.matcher.findMatchesAt(row, col);

        if (matches && matches.length >= 3) {
            console.log(`普通格子匹配: ${matches.length}连 → 有效果`);
            return {
                hasEffect: true,
                effectType: 'normal_match',
                matchCount: matches.length,
                matches: matches
            };
        }

        // 没有效果
        console.log(`普通格子无匹配 → 无效果`);
        return defaultResult;
    }

    // 执行格子效果
    executeBlockEffect(block, row, col, checkResult) {
        console.log(`执行格子效果: (${row},${col})[${block.type}] 类型=${checkResult.effectType}`);

        let eliminatedBlocks = [];
        let score = 0;

        switch (checkResult.effectType) {
            case 'special_combo':
                // 两个特殊格子组合
                const comboResult = this.processTwoSpecialBlocks(
                    block, checkResult.neighbor,
                    row, col,
                    checkResult.neighbor.row, checkResult.neighbor.col
                );
                eliminatedBlocks = comboResult.eliminatedBlocks;
                score = comboResult.score;
                break;

            case 'special_single':
                // 单个特殊格子
                if (checkResult.specialType === 'rocket') {
                    eliminatedBlocks = this.eliminateColumn(col);
                    score = 100;
                    this.playSound('shua');
                } else if (checkResult.specialType === 'bomb') {
                    eliminatedBlocks = this.eliminateArea(row, col, 3);
                    score = 150;
                    this.playSound('bomb');
                    this.addExplosionEffect(col, row);
                }
                break;

            case 'normal_match':
                // 普通格子匹配
                eliminatedBlocks = [];
                checkResult.matches.forEach(pos => {
                    const matchBlock = this.grid[pos.row][pos.col];
                    if (matchBlock) {
                        eliminatedBlocks.push({ row: pos.row, col: pos.col, block: matchBlock });

                        // 添加粒子效果
                        this.addParticleEffect(
                            matchBlock.x + this.blockSize / 2,
                            matchBlock.y + this.blockSize / 2,
                            matchBlock.color
                        );

                        // 清除格子
                        this.grid[pos.row][pos.col] = null;
                    }
                });

                // 计算得分：3连=30分，4连=40分，5连=50分...
                score = checkResult.matchCount * 10;
                break;
        }

        console.log(`效果执行完成: 消除${eliminatedBlocks.length}个格子, 得分${score}`);
        return { eliminatedBlocks, score };
    }

    // 检查并处理单个格子（保留兼容性）
    checkAndProcessBlock(block, row, col) {
        console.log(`检查格子: (${row},${col})[${block.type}]`);

        // 默认结果
        const defaultResult = {
            hasEffect: false,
            eliminatedBlocks: [],
            score: 0
        };

        // 如果格子为空，直接返回
        if (!block) {
            console.log('格子为空，跳过处理');
            return defaultResult;
        }

        // 1. 检查是否为特殊格子
        if (block.category === 'special' || block.blockType === 'special') {
            console.log(`特殊格子: ${block.specialType || block.special}`);

            // 检查是否有两个特殊格子相邻
            const neighbors = this.getAdjacentBlocks(row, col);
            for (const neighbor of neighbors) {
                if (neighbor && (neighbor.category === 'special' || neighbor.blockType === 'special')) {
                    console.log(`发现相邻特殊格子: (${neighbor.row},${neighbor.col})[${neighbor.type}]`);

                    // 处理两个特殊格子的组合效果
                    const result = this.processTwoSpecialBlocks(block, neighbor, row, col, neighbor.row, neighbor.col);
                    return {
                        hasEffect: result.eliminatedBlocks.length > 0,
                        eliminatedBlocks: result.eliminatedBlocks,
                        score: result.score
                    };
                }
            }

            // 没有相邻特殊格子，触发单个特殊格子效果
            const specialType = block.specialType || block.special;

            if (specialType === 'rocket') {
                // 火箭：消除整列
                console.log(`触发火箭效果: 消除列${col}`);
                const eliminatedBlocks = this.eliminateColumn(col);

                // 播放音效
                this.playSound('shua');

                return {
                    hasEffect: eliminatedBlocks.length > 0,
                    eliminatedBlocks,
                    score: 100
                };
            }
            else if (specialType === 'bomb') {
                // 炸弹：消除3x3区域
                console.log(`触发炸弹效果: 消除(${row},${col})周围3x3区域`);
                const eliminatedBlocks = this.eliminateArea(row, col, 3);

                // 添加爆炸特效
                this.addExplosionEffect(col, row);

                // 播放音效
                this.playSound('bomb');

                return {
                    hasEffect: eliminatedBlocks.length > 0,
                    eliminatedBlocks,
                    score: 150
                };
            }

            // 其他特殊格子类型
            return defaultResult;
        }

        // 2. 普通格子：检查是否有匹配
        const matches = this.matcher.findMatchesAt(row, col);

        if (matches && matches.length >= 3) {
            console.log(`普通格子匹配: 在(${row},${col})找到${matches.length}个匹配`);

            // 收集要消除的格子
            const eliminatedBlocks = [];
            matches.forEach(pos => {
                const block = this.grid[pos.row][pos.col];
                if (block) {
                    eliminatedBlocks.push({ row: pos.row, col: pos.col, block });

                    // 添加粒子效果
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);

                    // 清除格子
                    this.grid[pos.row][pos.col] = null;
                }
            });

            // 计算得分：3连=30分，4连=40分，5连=50分...
            const score = matches.length * 10;

            return {
                hasEffect: true,
                eliminatedBlocks,
                score
            };
        }

        // 没有匹配
        console.log(`普通格子无匹配: (${row},${col})`);
        return defaultResult;
    }

    // 消除整列
    eliminateColumn(col) {
        const eliminatedBlocks = [];
        console.log(`开始消除列${col}`);

        for (let row = 0; row < this.gridSizeY; row++) {
            const block = this.grid[row][col];
            if (block) {
                eliminatedBlocks.push({ row, col, block });
                // 标记方块为已移除
                block.isMatched = true;
                block.isRemoved = true;
                // 添加粒子特效
                this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                console.log(`火箭消除方块: (${row}, ${col}), 类型: ${block.type}`);
            }
        }

        console.log(`消除列${col}: 清除了${eliminatedBlocks.length}个格子`);
        return eliminatedBlocks;
    }

    // 获取相邻的格子
    getAdjacentBlocks(row, col) {
        const adjacent = [];
        const directions = [
            { dr: -1, dc: 0 }, // 上
            { dr: 1, dc: 0 },  // 下
            { dr: 0, dc: -1 }, // 左
            { dr: 0, dc: 1 }   // 右
        ];

        for (const dir of directions) {
            const newRow = row + dir.dr;
            const newCol = col + dir.dc;

            // 检查边界
            if (newRow >= 0 && newRow < this.gridSizeY && newCol >= 0 && newCol < this.gridSizeX) {
                const block = this.grid[newRow][newCol];
                if (block) {
                    adjacent.push(block);
                }
            }
        }

        return adjacent;
    }

    // 执行格子交换
    executeSwap(row1, col1, row2, col2) {
        const block1 = this.grid[row1][col1];
        const block2 = this.grid[row2][col2];

        console.log(`执行交换前: (${row1},${col1})[${block1.type}] ↔ (${row2},${col2})[${block2.type}]`);

        // 获取间距
        const spacing = this.getSpacing();

        // 更新block1的位置信息（移动到位置2）
        block1.row = row2;
        block1.col = col2;
        block1.x = this.gridStartX + col2 * (this.blockSize + spacing);
        block1.y = this.gridStartY + row2 * (this.blockSize + spacing);

        // 更新block2的位置信息（移动到位置1）
        block2.row = row1;
        block2.col = col1;
        block2.x = this.gridStartX + col1 * (this.blockSize + spacing);
        block2.y = this.gridStartY + row1 * (this.blockSize + spacing);

        // 交换网格中的位置
        this.grid[row1][col1] = block2;
        this.grid[row2][col2] = block1;

        console.log(`执行交换后: (${row1},${col1})[${block2.type}] ↔ (${row2},${col2})[${block1.type}]`);
    }

    // 触发特殊格子效果
    triggerSpecialEffect(specialBlock, targetPos) {
        const specialType = specialBlock.specialType;
        let eliminatedBlocks = [];
        let score = 0;

        console.log(`触发特殊效果: ${specialType} 在位置 (${targetPos.row},${targetPos.col})`);

        switch (specialType) {
            case 'rocket':
                // 火箭：消除整列
                eliminatedBlocks = this.eliminateColumn(targetPos.col);
                score = 100;
                this.playSound('shua');
                break;

            case 'bomb':
                // 炸弹：消除3x3区域
                eliminatedBlocks = this.eliminateArea(targetPos.row, targetPos.col, 3);
                score = 150;
                this.playSound('bomb');
                this.addExplosionEffect(targetPos.col, targetPos.row);
                break;
        }

        console.log(`特殊效果完成: 消除${eliminatedBlocks.length}个格子, 得分${score}`);
        return { score, eliminatedBlocks, success: true };
    }

    // 触发炸弹效果
    triggerBombEffect(row, col, size = 3) {
        console.log(`触发炸弹效果: 位置(${row}, ${col}), 范围${size}x${size}`);

        const eliminatedBlocks = this.eliminateArea(row, col, size);
        const score = size === 3 ? 150 : (size === 5 ? 500 : 100);

        // 播放音效和特效
        this.playSound('bomb');
        this.addExplosionEffect(col, row, size > 3);

        // 清除被消除的方块
        eliminatedBlocks.forEach(item => {
            if (this.grid[item.row] && this.grid[item.row][item.col]) {
                this.grid[item.row][item.col] = null;
            }
        });

        console.log(`炸弹效果完成: 消除${eliminatedBlocks.length}个方块, 得分${score}`);
        return { score, eliminatedBlocks, success: true };
    }

    // 触发火箭效果
    triggerRocketEffect(row, col, rocketType) {
        console.log(`触发火箭效果: 位置(${row}, ${col}), 类型${rocketType}`);

        let eliminatedBlocks = [];
        let score = 100;

        // 所有火箭都消除垂直列，不管是什么类型
        eliminatedBlocks = this.eliminateColumn(col);
        console.log(`小火箭消除列${col}（类型: ${rocketType}）`);

        // 播放音效
        this.playSound('shua');

        // 清除被消除的方块
        eliminatedBlocks.forEach(item => {
            if (this.grid[item.row] && this.grid[item.row][item.col]) {
                this.grid[item.row][item.col] = null;
            }
        });

        console.log(`火箭效果完成: 消除${eliminatedBlocks.length}个方块, 得分${score}`);
        return { score, eliminatedBlocks, success: true };
    }

    // 播放音效
    playSound(soundName) {
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound(soundName);
        }
    }

    // 添加爆炸效果
    addExplosionEffect(col, row, large = false) {
        const x = this.gridStartX + col * (this.blockSize + this.getSpacing()) + this.blockSize / 2;
        const y = this.gridStartY + row * (this.blockSize + this.getSpacing()) + this.blockSize / 2;

        // 这里可以添加爆炸特效的实现
        console.log(`爆炸效果: (${x}, ${y}) ${large ? '大爆炸' : '小爆炸'}`);
    }

    // 消除整行
    eliminateRow(row) {
        const eliminatedBlocks = [];
        console.log(`开始消除行${row}`);

        for (let col = 0; col < this.gridSizeX; col++) {
            const block = this.grid[row][col];
            if (block) {
                eliminatedBlocks.push({ row, col, block });
                // 标记方块为已移除
                block.isMatched = true;
                block.isRemoved = true;
                // 添加粒子特效
                this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                console.log(`火箭消除方块: (${row}, ${col}), 类型: ${block.type}`);
            }
        }

        console.log(`消除行${row}: 清除了${eliminatedBlocks.length}个格子`);
        return eliminatedBlocks;
    }

    // 消除十字形（一行一列）
    eliminateCross(centerRow, centerCol) {
        const eliminatedBlocks = [];

        // 消除整行
        eliminatedBlocks.push(...this.eliminateRow(centerRow));

        // 消除整列（避免重复消除中心点）
        for (let row = 0; row < this.gridSizeY; row++) {
            if (row !== centerRow) {
                const block = this.grid[row][centerCol];
                if (block) {
                    eliminatedBlocks.push({ row, col: centerCol, block });
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                    this.grid[row][centerCol] = null;
                }
            }
        }

        console.log(`消除十字形(${centerRow},${centerCol}): 清除了${eliminatedBlocks.length}个格子`);
        return eliminatedBlocks;
    }

    // 消除3列（中心列及其左右各一列）
    eliminateThreeColumns(centerCol) {
        const eliminatedBlocks = [];

        for (let c = Math.max(0, centerCol - 1); c <= Math.min(this.gridSizeX - 1, centerCol + 1); c++) {
            eliminatedBlocks.push(...this.eliminateColumn(c));
        }

        console.log(`消除3列(中心列${centerCol}): 清除了${eliminatedBlocks.length}个格子`);
        return eliminatedBlocks;
    }



    // 消除矩形区域
    eliminateArea(centerRow, centerCol, size) {
        const eliminatedBlocks = [];
        const radius = Math.floor(size / 2);

        console.log(`消除区域: 中心(${centerRow}, ${centerCol}), 大小${size}x${size}, 半径${radius}`);

        for (let row = Math.max(0, centerRow - radius); row <= Math.min(this.gridSizeY - 1, centerRow + radius); row++) {
            for (let col = Math.max(0, centerCol - radius); col <= Math.min(this.gridSizeX - 1, centerCol + radius); col++) {
                const block = this.grid[row][col];
                if (block) {
                    eliminatedBlocks.push({ row, col, block });
                    // 标记方块为已移除
                    block.isMatched = true;
                    block.isRemoved = true;
                    // 添加爆炸特效
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                    console.log(`炸弹消除方块: (${row}, ${col}), 类型: ${block.type}`);
                }
            }
        }

        console.log(`区域消除完成: 消除了${eliminatedBlocks.length}个方块`);
        return eliminatedBlocks;
    }



    // 添加爆炸效果
    addExplosionEffect(x, y, isBig = false) {
        const particleCount = isBig ? 20 : 12;
        const colors = ['#FF4500', '#FF6347', '#FFD700', '#FF69B4', '#00CED1'];

        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * (isBig ? 15 : 10),
                vy: (Math.random() - 0.5) * (isBig ? 15 : 10),
                size: Math.random() * (isBig ? 8 : 6) + (isBig ? 4 : 2),
                color: colors[Math.floor(Math.random() * colors.length)],
                life: 1,
                decay: 0.015
            });
        }

        // 添加爆炸闪烁效果
        for (let i = 0; i < (isBig ? 8 : 5); i++) {
            this.sparkles.push({
                x: x + (Math.random() - 0.5) * (isBig ? 40 : 25),
                y: y + (Math.random() - 0.5) * (isBig ? 40 : 25),
                size: Math.random() * (isBig ? 12 : 8) + (isBig ? 6 : 4),
                life: 1,
                decay: 0.02,
                twinkle: Math.random() * Math.PI * 2
            });
        }
    }
    
    // 添加粒子效果（平衡版本）
    addParticleEffect(x, y, color) {
        // 超快速模式：完全跳过粒子效果（仅在FPS<20时）
        if (this.ultraFastMode) {
            return;
        }

        // 快速消除模式：极简粒子效果
        if (this.fastEliminationMode) {
            // 只创建2个粒子
            for (let i = 0; i < 2; i++) {
                this.particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 4,
                    vy: (Math.random() - 0.5) * 4,
                    size: Math.random() * 2 + 0.8,
                    color: color,
                    life: 0.7,
                    decay: 0.05
                });
            }

            // 快速模式不添加闪烁效果
            return;
        }

        // 标准模式：适度的粒子效果
        const particleCount = this.lowPerformanceMode ? 2 : 4; // 减少数量

        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 5, // 减少速度
                vy: (Math.random() - 0.5) * 5,
                size: Math.random() * 2.5 + 1, // 减少大小
                color: color,
                life: 0.9, // 减少生命周期
                decay: 0.03 // 加快消失速度
            });
        }

        // 减少闪烁效果
        const sparkleCount = this.lowPerformanceMode ? 0 : 1; // 进一步减少
        for (let i = 0; i < sparkleCount; i++) {
            this.sparkles.push({
                x: x + (Math.random() - 0.5) * 12, // 减少范围
                y: y + (Math.random() - 0.5) * 12,
                size: Math.random() * 4 + 2, // 减少大小
                life: 0.8, // 减少生命周期
                decay: 0.05, // 加快消失
                twinkle: 0
            });
        }
    }
    
    // 添加浮动文字（优化响应速度）
    addFloatingText(text, x, y, color) {
        this.floatingTexts.push({
            text: text,
            x: x,
            y: y,
            vx: 0,
            vy: -3, // 增加移动速度，从-2改为-3
            color: color,
            life: 1,
            decay: 0.025, // 加快消失速度，从0.015改为0.025
            scale: 1,
            isScoreText: true // 标记为分数文字，优先处理
        });
    }

    // 添加连击文字（平衡版本）
    addComboText(comboCount, multiplier) {
        // 超快速模式：只显示高连击数（仅在FPS<20时）
        if (this.ultraFastMode && comboCount < 4) {
            return; // 降低门槛到4连击
        }

        // 快速模式：显示2连击以上
        if (this.fastEliminationMode && comboCount < 2) {
            return; // 降低门槛到2连击
        }

        // 立即清除所有现有连击文字
        this.floatingTexts = this.floatingTexts.filter(text => !text.isCombo);

        // 恢复丰富的显示逻辑
        let scale, color;
        if (comboCount >= 6) {
            scale = 2.2;
            color = '#FF0000'; // 红色 - 6次以上
        } else if (comboCount >= 4) {
            scale = 1.9;
            color = '#FF8C00'; // 橘黄色 - 4-5次
        } else if (comboCount >= 2) {
            scale = 1.6;
            color = '#32CD32'; // 绿色 - 2-3次
        }

        // 根据模式选择文字内容
        let text;
        if (this.ultraFastMode) {
            text = `${comboCount}连击`;
        } else if (this.fastEliminationMode) {
            text = `${comboCount}连击! x${multiplier}`;
        } else {
            text = `${comboCount}连击! x${multiplier}`;
        }

        this.floatingTexts.push({
            text: text,
            x: this.logicalWidth / 2,
            y: this.logicalHeight / 2 - 40,
            vx: 0,
            vy: -0.8, // 恢复适中的移动速度
            color: color,
            life: 1,
            decay: 0.02, // 恢复适中的消失速度
            scale: scale,
            isCombo: true,
            pulsePhase: 0,
            isSimple: this.ultraFastMode
        });

        // 减少日志输出
        if (this.gameManager && this.gameManager.debug) {
            console.log(`连击: ${text}`);
        }
    }
    
    // 重新开始游戏
    restartGame() {
        this.resetScore();
        this.isGameOver = false;
        this.isLevelComplete = false;
        this.isAnimating = false;
        this.showExitDialog = false;
        this.isBombCardSelecting = false;
        
        // 清空特效
        this.particles = [];
        this.sparkles = [];
        this.floatingTexts = [];
        this.animations = [];
        
        // 重置道具数量（从 config.js 获取）
        const config = GamePageCore.getConfig();
        if (config.PROPS && config.PROPS.DEFAULT_COUNTS) {
            this.props = {
                refresh: config.PROPS.DEFAULT_COUNTS.refresh,
                bomb: config.PROPS.DEFAULT_COUNTS.bomb,
                clear: config.PROPS.DEFAULT_COUNTS.clear,
                levelDown: config.PROPS.DEFAULT_COUNTS.levelDown
            };
        }
        
        this.initGrid();
        console.log('游戏重新开始');
    }
    
    // 检查游戏状态
    checkGameStatus() {
        // 检查是否达到目标分数
        if (this.score >= this.targetScore) {
            if (!this.isLevelComplete) {
                this.isLevelComplete = true;
                console.log(`恭喜过关！当前分数: ${this.score}, 目标分数: ${this.targetScore}`);

                // 播放胜利音效
                if (this.gameManager && this.gameManager.audioManager) {
                    this.gameManager.audioManager.playSound('win');
                }

                // 添加庆祝效果
                this.addCelebrationEffect();

                // 立即显示通关提示，不需要等待动画完成
                setTimeout(() => {
                    this.showLevelCompleteDialog();
                }, 500);
            }
        }
    }

    // 显示通关对话框
    showLevelCompleteDialog() {
        console.log('=== 显示通关对话框 ===');
        console.log(`最终分数: ${this.score}, 目标分数: ${this.targetScore}`);

        // 设置标志位来显示通关UI
        this.showLevelCompleteUI = true;

        // 停止游戏逻辑
        this.isGameOver = false; // 不是游戏失败
        this.isLevelComplete = true; // 是通关成功

        // 初始化倒计时
        this.levelCompleteCountdown = 3;
        this.levelCompleteStartTime = Date.now();

        // 如果有渲染器，通知渲染器显示通关界面
        if (this.renderer) {
            this.renderer.showLevelComplete = true;
        }

        // 开始倒计时，3秒后自动进入下一关
        this.startLevelCompleteCountdown();

        console.log('通关对话框已设置显示，开始3秒倒计时');
    }

    // 开始通关倒计时
    startLevelCompleteCountdown() {
        this.countdownInterval = setInterval(() => {
            const elapsed = Date.now() - this.levelCompleteStartTime;
            const remaining = Math.max(0, 3 - Math.floor(elapsed / 1000));

            this.levelCompleteCountdown = remaining;

            if (remaining <= 0) {
                this.proceedToNextLevel();
            }
        }, 100); // 每100ms更新一次，确保倒计时显示流畅
    }

    // 进入下一关
    proceedToNextLevel() {
        console.log('进入下一关');

        // 清除倒计时
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }

        // 重置通关状态
        this.showLevelCompleteUI = false;
        this.isLevelComplete = false;

        if (this.renderer) {
            this.renderer.showLevelComplete = false;
        }

        // 获取下一关配置
        const currentLevel = this.level || 1;
        const nextLevel = currentLevel + 1;

        // 检查是否有下一关
        const config = typeof GAME_CONFIG !== 'undefined' ? GAME_CONFIG : {};
        const nextLevelConfig = config.LEVELS ? config.LEVELS.find(l => l.id === nextLevel) : null;

        if (nextLevelConfig) {
            console.log(`进入第${nextLevel}关: ${nextLevelConfig.name}`);
            // 切换到下一关
            this.gameManager.switchToPage('game', nextLevelConfig);
        } else {
            console.log('已通关所有关卡，返回主页');
            // 所有关卡都通关了，返回主页
            this.gameManager.switchToPage('main');
        }
    }

    // 手动确认进入下一关（点击确定按钮时调用）
    confirmNextLevel() {
        console.log('玩家点击确定，立即进入下一关');
        this.proceedToNextLevel();
    }

    // 检查并处理连锁匹配（道具使用后的连锁检测）
    async checkAndProcessCascadeMatches(source = '未知') {
        console.log(`=== ${source}：开始检查连锁匹配 ===`);

        if (!this.matcher) {
            console.warn(`${source}：匹配器不可用，跳过连锁检测`);
            return;
        }

        if (this.isAnimating) {
            console.log(`${source}：游戏正在动画中，跳过连锁检测`);
            return;
        }

        try {
            // 设置动画状态，防止其他操作干扰
            this.isAnimating = true;

            // 循环处理匹配和掉落，直到没有新的匹配
            let hasMoreMatches = true;
            let totalScore = 0;
            let roundCount = 0;

            while (hasMoreMatches) {
                roundCount++;
                console.log(`${source}：开始第${roundCount}轮连锁匹配检测`);

                // 查找所有匹配
                const allMatches = this.matcher.findAllMatches();

                if (allMatches && allMatches.matches.length > 0) {
                    console.log(`${source}：第${roundCount}轮发现${allMatches.matches.length}个匹配方块`);

                    // 处理匹配消除
                    const result = await this.matcher.processMatches(allMatches);
                    console.log(`${source}：第${roundCount}轮匹配处理完成，得分: ${result.score}`);

                    // 累计分数
                    if (result.score > 0) {
                        totalScore += result.score;
                    }

                    // 处理掉落
                    if (result.removedPositions && result.removedPositions.length > 0) {
                        console.log(`${source}：第${roundCount}轮处理掉落: ${result.removedPositions.length}个位置`);
                        if (this.animator && this.animator.falling) {
                            // 等待掉落完成
                            await this.animator.falling.processFalling(result.removedPositions);
                            console.log(`${source}：第${roundCount}轮掉落处理完成`);
                        }
                    }
                } else {
                    hasMoreMatches = false;
                    console.log(`${source}：第${roundCount}轮无新匹配，结束连锁检测`);
                }
            }

            // 更新总分数
            if (totalScore > 0) {
                this.updateScore(totalScore);
                console.log(`${source}：连锁匹配完成，总轮数: ${roundCount}, 总得分: ${totalScore}`);
            } else {
                console.log(`${source}：无连锁匹配`);
            }

            // 检查游戏状态
            this.checkGameStatus();

        } catch (error) {
            console.error(`${source}：连锁匹配处理出错:`, error);
        } finally {
            // 重置动画状态
            this.isAnimating = false;
            console.log(`${source}：连锁匹配检测完成，动画状态已重置`);
        }

        console.log(`=== ${source}：连锁匹配检测结束 ===`);
    }
    
    // 添加庆祝效果
    addCelebrationEffect() {
        // 添加大量彩色粒子
        for (let i = 0; i < 50; i++) {
            this.particles.push({
                x: Math.random() * this.logicalWidth,
                y: Math.random() * this.logicalHeight,
                vx: (Math.random() - 0.5) * 10,
                vy: (Math.random() - 0.5) * 10,
                size: Math.random() * 8 + 4,
                color: ['#FFD700', '#FF69B4', '#00CED1', '#98FB98', '#DDA0DD'][Math.floor(Math.random() * 5)],
                life: 1,
                decay: 0.01
            });
        }
        
        // 添加闪烁星星
        for (let i = 0; i < 20; i++) {
            this.sparkles.push({
                x: Math.random() * this.logicalWidth,
                y: Math.random() * this.logicalHeight,
                size: Math.random() * 10 + 5,
                life: 1,
                decay: 0.008,
                twinkle: Math.random() * Math.PI * 2
            });
        }
    }
    
    // 更新游戏状态（性能优化版）
    update() {
        // 性能计数
        if (this.performanceStats) {
            this.performanceStats.frameCount++;
        }

        const updateStartTime = Date.now();

        this.animationTime += 0.016; // 60fps

        // 更新背景效果
        try {
            if (this.backgroundStars && typeof BackgroundUtils !== 'undefined') {
                BackgroundUtils.updateStars(this.backgroundStars);
            }
        } catch (error) {
            console.warn('背景更新失败:', error);
        }

        // 更新动画控制器
        if (this.animator) {
            this.animator.update();
            this.isAnimating = this.animator.hasActiveAnimations();
        }

        // 优化特效更新频率，确保分数文字流畅显示
        if (this.lowPerformanceMode) {
            // 分数文字始终每帧更新，确保响应速度
            this.updateFloatingTexts();

            // 其他特效每隔一帧更新
            if (this.performanceStats && this.performanceStats.frameCount % 2 === 0) {
                this.updateParticles();
                this.updateSparkles();
            }
        } else {
            // 正常模式下每帧更新所有特效
            this.updateParticles();
            this.updateSparkles();
            this.updateFloatingTexts();
        }

        // 方块动画始终更新（游戏核心逻辑）
        this.updateBlockAnimations();

        // 记录更新时间
        if (this.performanceStats) {
            this.performanceStats.updateTime = Date.now() - updateStartTime;
        }
    }
    
    // 更新粒子效果（减少粒子版本）
    updateParticles() {
        if (this.particles.length === 0) return;

        // 严格限制粒子数量
        const maxParticles = this.highFpsMode ? (this.maxParticles || 8) : 15; // 进一步减少
        if (this.particles.length > maxParticles) {
            this.particles = this.particles.slice(-maxParticles);
        }

        // 减少更新频率
        if (this.highFpsMode || this.fastEliminationMode) {
            // 每两帧更新一次粒子，减少计算负担
            if (this.performanceStats && this.performanceStats.frameCount % 2 !== 0) {
                return;
            }
        }

        // 更激进的生命周期检查
        this.particles = this.particles.filter(particle => {
            if (particle.life <= 0.2) return false; // 提前移除更多粒子

            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= particle.decay * 1.3; // 加快消失
            particle.vx *= 0.88; // 更快减速
            particle.vy *= 0.88;
            return true;
        });
    }

    // 更新闪烁效果（减少闪烁版本）
    updateSparkles() {
        if (this.sparkles.length === 0) return;

        // 严格限制闪烁数量
        const maxSparkles = this.highFpsMode ? (this.maxSparkles || 3) : 6; // 进一步减少
        if (this.sparkles.length > maxSparkles) {
            this.sparkles = this.sparkles.slice(-maxSparkles);
        }

        // 减少更新频率
        if (this.highFpsMode || this.fastEliminationMode) {
            // 每三帧更新一次闪烁效果
            if (this.performanceStats && this.performanceStats.frameCount % 3 !== 0) {
                return;
            }
        }

        this.sparkles = this.sparkles.filter(sparkle => {
            sparkle.life -= sparkle.decay * 1.4; // 更快消失
            sparkle.twinkle += 0.08; // 减少计算频率
            return sparkle.life > 0.15; // 提前移除
        });
    }

    // 更新浮动文字（60FPS极限优化版）
    updateFloatingTexts() {
        if (this.floatingTexts.length === 0) return;

        // 60FPS模式：严格限制浮动文字数量
        const maxTexts = this.highFpsMode ? (this.maxFloatingTexts || 3) : 10;
        if (this.floatingTexts.length > maxTexts) {
            this.floatingTexts = this.floatingTexts.slice(-maxTexts);
        }

        this.floatingTexts = this.floatingTexts.filter(text => {
            text.x += text.vx;
            text.y += text.vy;
            text.life -= text.decay * (this.highFpsMode ? 1.3 : 1); // 60FPS模式下更快消失

            // 60FPS模式：跳过缩放计算以提升性能
            if (!this.highFpsMode && !text.isCombo) {
                text.scale = Math.max(0.5, text.scale - 0.008);
            }

            return text.life > (this.highFpsMode ? 0.1 : 0.05);
        });
    }
    
    // 更新方块动画（平衡版本）
    updateBlockAnimations() {
        if (!this.grid || !Array.isArray(this.grid)) return;

        // 超快速模式：跳过动画，直接设置最终状态（仅在FPS<20时）
        if (this.ultraFastMode) {
            if (this.animatedBlocks && this.animatedBlocks.length > 0) {
                this.animatedBlocks.forEach(animatedBlock => {
                    const { block } = animatedBlock;
                    if (block && block.isMatched) {
                        // 直接设置为消失状态
                        block.alpha = 0;
                        block.scale = 0;
                    }
                });
                this.animatedBlocks = []; // 清空动画列表
            }
            return;
        }

        // 其他模式：使用流畅的动画
        if (this.animatedBlocks && this.animatedBlocks.length > 0) {
            this.animatedBlocks = this.animatedBlocks.filter(animatedBlock => {
                const { row, col, block } = animatedBlock;

                if (!block || !this.grid[row] || !this.grid[row][col]) {
                    return false;
                }

                // 匹配状态的流畅消失动画
                if (block.isMatched) {
                    // 根据模式调整动画速度
                    let alphaDecay, scaleDecay;
                    if (this.fastEliminationMode) {
                        alphaDecay = 0.18; // 快速但不过快
                        scaleDecay = 0.12;
                    } else {
                        alphaDecay = 0.12; // 标准流畅速度
                        scaleDecay = 0.08;
                    }

                    block.alpha = Math.max(0, block.alpha - alphaDecay);
                    block.scale = Math.max(0, block.scale - scaleDecay);

                    if (block.alpha <= 0 || block.scale <= 0) {
                        block.alpha = 0;
                        block.scale = 0;
                        return false;
                    }
                    return true;
                }

                // 选中状态的流畅效果
                if (block.isSelected) {
                    if (!block.glowIntensity) block.glowIntensity = 0;
                    block.glowIntensity = Math.min(1, block.glowIntensity + 0.15);
                } else {
                    if (block.glowIntensity > 0) {
                        block.glowIntensity = Math.max(0, block.glowIntensity - 0.1);
                        if (block.glowIntensity <= 0) {
                            return false;
                        }
                    }
                }

                return block.glowIntensity > 0;
            });
        }
    }

    // 添加方块到动画列表（仅在需要时）
    addBlockAnimation(row, col, block) {
        if (!this.animatedBlocks) {
            this.animatedBlocks = [];
        }

        // 检查是否已存在
        const exists = this.animatedBlocks.some(ab => ab.row === row && ab.col === col);
        if (!exists) {
            this.animatedBlocks.push({ row, col, block });
        }
    }
    
    // 创建粒子效果
    createParticles(x, y, color, count = 8) {
        for (let i = 0; i < count; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 8,
                vy: (Math.random() - 0.5) * 8,
                size: Math.random() * 4 + 2,
                color: color,
                life: 1,
                decay: 0.02
            });
        }
    }
    
    // 创建闪烁效果
    createSparkles(x, y, count = 3) {
        for (let i = 0; i < count; i++) {
            this.sparkles.push({
                x: x + (Math.random() - 0.5) * 20,
                y: y + (Math.random() - 0.5) * 20,
                size: Math.random() * 6 + 4,
                life: 1,
                decay: 0.03,
                twinkle: 0
            });
        }
    }
    
    // 创建浮动文字（优化响应速度）
    createFloatingText(x, y, text, color) {
        // 性能监控：记录分数文字创建时间
        const createTime = Date.now();

        this.floatingTexts.push({
            text: text,
            x: x,
            y: y,
            vx: 0,
            vy: -3, // 增加移动速度，与addFloatingText保持一致
            color: color,
            life: 1,
            decay: 0.025, // 加快消失速度，与addFloatingText保持一致
            scale: 1,
            isScoreText: true, // 标记为分数文字，优先处理
            createTime: createTime // 记录创建时间
        });

        // 减少日志输出，只在调试模式下显示
        if (this.gameManager && this.gameManager.debug) {
            console.log(`💰 分数文字创建: "${text}"`);
        }
    }
    
    // 网格起始位置（供事件处理使用）
    get gridStartX() {
        // 尝试获取 CONFIG_UTILS
        let configUtils = this.getConfigUtils();

        if (configUtils) {
            const gridConfig = configUtils.getGridConfig(this.logicalWidth, this.logicalHeight);
            return gridConfig.startX;
        } else {
            // 使用响应式计算（基于逻辑尺寸）
            const config = GamePageCore.getConfig();
            const layout = config.LAYOUT;
            const gridAreaWidth = this.logicalWidth * (layout.GRID_AREA.WIDTH_PERCENT || 0.8);
            const gridStartX = (this.logicalWidth - gridAreaWidth) / 2;
            const actualGridWidth = this.gridSizeX * this.blockSize + (this.gridSizeX - 1) * config.GRID.SPACING;
            const contentAreaWidth = gridAreaWidth - config.GRID.PADDING * 2;
            return gridStartX + config.GRID.PADDING + (contentAreaWidth - actualGridWidth) / 2;
        }
    }

    get gridStartY() {
        // 尝试获取 CONFIG_UTILS
        let configUtils = this.getConfigUtils();

        if (configUtils) {
            const gridConfig = configUtils.getGridConfig(this.logicalWidth, this.logicalHeight);
            return gridConfig.startY;
        } else {
            // 使用响应式计算（基于逻辑尺寸）
            const config = GamePageCore.getConfig();
            const layout = config.LAYOUT;
            const spacing = layout.ELEMENT_SPACING;

            // 计算各元素位置
            const backButtonBottom = layout.BACK_BUTTON.Y + layout.BACK_BUTTON.HEIGHT;
            const statsBarY = backButtonBottom + spacing;
            const statsBarBottom = statsBarY + layout.STATS_BAR.HEIGHT;
            const gridAreaY = statsBarBottom + spacing;

            return gridAreaY + config.GRID.PADDING;
        }
    }

    // 获取 CONFIG_UTILS 的辅助方法
    getConfigUtils() {
        try {
            if (typeof window !== 'undefined' && window.CONFIG_UTILS) {
                return window.CONFIG_UTILS;
            } else if (typeof globalThis !== 'undefined' && globalThis.CONFIG_UTILS) {
                return globalThis.CONFIG_UTILS;
            } else if (typeof global !== 'undefined' && global.CONFIG_UTILS) {
                return global.CONFIG_UTILS;
            } else {
                try {
                    return eval('typeof CONFIG_UTILS !== "undefined" ? CONFIG_UTILS : null');
                } catch (e) {
                    return null;
                }
            }
        } catch (error) {
            return null;
        }
    }

    // 静态方法：获取布局配置
    static getLayout() {
        const config = GamePageCore.getConfig();
        return config.LAYOUT || {};
    }
    
    // 静态方法：更新网格Y坐标
    static setGridStartY(y) {
        // 注意：这个方法现在不能直接修改配置，因为配置是只读的
        console.warn('setGridStartY: 配置现在是只读的，请在config.js中修改');
    }

    // 静态方法：获取网格起始X坐标（基于逻辑尺寸）
    static getGridStartX(logicalWidth, logicalHeight) {
        // 尝试获取 CONFIG_UTILS
        let configUtils = GamePageCore.getStaticConfigUtils();

        if (configUtils) {
            const gridConfig = configUtils.getGridConfig(logicalWidth, logicalHeight);
            return gridConfig.startX;
        } else {
            // 使用响应式计算（基于逻辑尺寸）
            const config = GamePageCore.getConfig();
            const layout = config.LAYOUT;
            const gridAreaWidth = logicalWidth * (layout.GRID_AREA.WIDTH_PERCENT || 0.8);
            const gridStartX = (logicalWidth - gridAreaWidth) / 2;

            const gridSizeX = config.GRID ? config.GRID.SIZE_X : 8;
            const blockSize = config.GRID ? config.GRID.BLOCK_SIZE : 45;
            const actualGridWidth = gridSizeX * blockSize + (gridSizeX - 1) * config.GRID.SPACING;
            const contentAreaWidth = gridAreaWidth - config.GRID.PADDING * 2;

            return gridStartX + config.GRID.PADDING + (contentAreaWidth - actualGridWidth) / 2;
        }
    }

    // 静态方法：获取网格起始Y坐标（基于逻辑尺寸）
    static getGridStartY(logicalWidth, logicalHeight) {
        // 尝试获取 CONFIG_UTILS
        let configUtils = GamePageCore.getStaticConfigUtils();

        if (configUtils) {
            const gridConfig = configUtils.getGridConfig(logicalWidth, logicalHeight);
            return gridConfig.startY;
        } else {
            // 使用响应式计算（基于逻辑尺寸）
            const config = GamePageCore.getConfig();
            const layout = config.LAYOUT;
            const spacing = layout.ELEMENT_SPACING;

            // 计算各元素位置
            const backButtonBottom = layout.BACK_BUTTON.Y + layout.BACK_BUTTON.HEIGHT;
            const statsBarY = backButtonBottom + spacing;
            const statsBarBottom = statsBarY + layout.STATS_BAR.HEIGHT;
            const gridAreaY = statsBarBottom + spacing;

            return gridAreaY + config.GRID.PADDING;
        }
    }

    // 静态方法：获取 CONFIG_UTILS
    static getStaticConfigUtils() {
        try {
            if (typeof window !== 'undefined' && window.CONFIG_UTILS) {
                return window.CONFIG_UTILS;
            } else if (typeof globalThis !== 'undefined' && globalThis.CONFIG_UTILS) {
                return globalThis.CONFIG_UTILS;
            } else if (typeof global !== 'undefined' && global.CONFIG_UTILS) {
                return global.CONFIG_UTILS;
            } else {
                try {
                    return eval('typeof CONFIG_UTILS !== "undefined" ? CONFIG_UTILS : null');
                } catch (e) {
                    return null;
                }
            }
        } catch (error) {
            return null;
        }
    }
    
    // 初始化方法
    init() {
        console.log('GamePageCore初始化');
        this.initMatcher();
        this.initGrid();
        this.initBackgroundEffects();
        this.loadAnimalImages();
        this.initAnimator();

        // 延迟检查初始匹配（确保所有组件都已初始化）
        setTimeout(() => {
            this.checkInitialMatches();
        }, 100);
    }

    // 初始化匹配器
    initMatcher() {
        try {
            if (typeof GamePageMatcher !== 'undefined') {
                this.matcher = new GamePageMatcher(this);
                console.log('匹配器初始化成功');
            } else {
                console.warn('GamePageMatcher未加载');
                this.matcher = null;
            }
        } catch (error) {
            console.error('匹配器初始化失败:', error);
            this.matcher = null;
        }
    }

    // 检查并处理初始匹配
    checkInitialMatches() {
        console.log('=== 检查初始匹配 ===');

        if (!this.matcher) {
            console.log('匹配器未初始化，跳过初始匹配检查');
            return;
        }

        const allMatches = this.matcher.findAllMatches();
        console.log(`初始匹配检查结果: 发现${allMatches?.matches?.length || 0}个匹配方块`);

        if (allMatches && allMatches.matches.length > 0) {
            console.log('发现初始匹配，开始自动消除');
            this.processInitialMatches(allMatches);
        } else {
            console.log('没有发现初始匹配');
        }
    }

    // 处理初始匹配
    async processInitialMatches(matchResult) {
        console.log('开始处理初始匹配');

        try {
            // 使用matcher处理匹配
            const result = await this.matcher.processMatches(matchResult);
            console.log('初始匹配处理完成:', result);

            // 更新分数
            if (result.score > 0) {
                this.updateScore(result.score);
                console.log(`初始匹配得分: +${result.score}`);
            }

            // 处理掉落
            if (result.removedPositions && result.removedPositions.length > 0 && this.animator && this.animator.falling) {
                await this.animator.falling.processFalling(result.removedPositions);
                console.log('初始匹配掉落处理完成');
            }

        } catch (error) {
            console.error('处理初始匹配时出错:', error);
        }
    }
    
    // 初始化动画控制器
    initAnimator() {
        try {
            if (typeof GamePageAnimator !== 'undefined') {
                this.animator = new GamePageAnimator(this);
                this.animator.init();
                console.log('动画控制器初始化成功');
            } else {
                console.warn('GamePageAnimator未加载，使用简化版本');
                this.animator = null;
            }
        } catch (error) {
            console.error('动画控制器初始化失败:', error);
            this.animator = null;
        }
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageCore;
} else {
    window.GamePageCore = GamePageCore;
}