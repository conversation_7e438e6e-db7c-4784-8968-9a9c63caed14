// 导入GamePageCore类
const GamePageCore = require('./GamePageCore');

// 游戏页面事件处理器 - 负责触摸和交互事件
class GamePageEvents {
    constructor(gameCore, gameRenderer) {
        this.core = gameCore;
        this.renderer = gameRenderer;
        this.canvas = gameCore.canvas;
        
        // 触摸状态
        this.isDragging = false;
        this.dragStartRow = -1;
        this.dragStartCol = -1;
        this.dragEndRow = -1;
        this.dragEndCol = -1;
        this.lastTouchTime = 0;
        this.touchStartPos = { x: 0, y: 0 };

        // 目标高亮状态
        this.targetHighlightRow = -1;
        this.targetHighlightCol = -1;

        // 炸弹卡拖拽状态
        this.isDraggingBomb = false;
        this.bombDragStartX = -1;
        this.bombDragStartY = -1;
        this.bombDragCurrentX = -1;
        this.bombDragCurrentY = -1;
        
        // 事件处理器引用
        this.touchStartHandler = null;
        this.touchMoveHandler = null;
        this.touchEndHandler = null;
        
        console.log('GamePageEvents初始化完成');
    }
    
    // 初始化事件监听
    init() {
        this.setupTouchEvents();
        console.log('游戏事件监听器设置完成');
    }
    
    // 设置触摸事件监听
    setupTouchEvents() {
        if (typeof tt !== 'undefined') {
            console.log('使用抖音小游戏触摸事件API');
            
            // 触摸开始事件
            this.touchStartHandler = (e) => {
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchStart(touch.clientX, touch.clientY);
                }
            };
            
            // 触摸移动事件
            this.touchMoveHandler = (e) => {
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchMove(touch.clientX, touch.clientY);
                }
            };
            
            // 触摸结束事件
            this.touchEndHandler = (e) => {
                this.handleTouchEnd();
            };
            
            // 注册事件监听器
            tt.onTouchStart(this.touchStartHandler);
            tt.onTouchMove(this.touchMoveHandler);
            tt.onTouchEnd(this.touchEndHandler);
            
            console.log('抖音小游戏触摸事件监听器设置完成');
        } else {
            console.warn('非抖音小游戏环境，跳过触摸事件设置');
        }
    }
    
    // 处理触摸开始事件
    handleTouchStart(x, y) {
        this.touchStartPos = { x, y };
        this.lastTouchTime = Date.now();
        
        // 检查按钮点击
        if (this.handleButtonClick(x, y)) {
            return;
        }

        // 检查道具栏点击
        if (this.handlePropBarClick(x, y)) {
            return;
        }

        // 检查游戏网格点击
        this.handleGridTouchStart(x, y);
    }
    
    // 处理触摸移动事件
    handleTouchMove(x, y) {
        if (this.isDragging) {
            this.handleGridTouchMove(x, y);
        } else if (this.isDraggingBomb) {
            // 更新炸弹卡拖拽位置
            this.bombDragCurrentX = x;
            this.bombDragCurrentY = y;
        }
    }
    
    // 处理触摸结束事件
    handleTouchEnd(x, y) {
        if (this.isDragging) {
            // 传递最后的触摸位置给网格触摸结束处理
            this.handleGridTouchEnd(x, y);
        } else if (this.isDraggingBomb) {
            // 处理炸弹卡拖拽结束
            this.handleBombDragEnd(x, y);
        }

        // 重置拖拽状态
        this.resetDragState();
    }

    // 重置拖拽状态
    resetDragState() {
        this.isDragging = false;
        this.isDraggingBomb = false;
        this.dragStartRow = -1;
        this.dragStartCol = -1;
        this.dragEndRow = -1;
        this.dragEndCol = -1;
    }

    // 处理炸弹卡拖拽结束
    handleBombDragEnd(x, y) {
        // 如果坐标无效，使用当前拖拽位置
        const finalX = (x !== undefined && x !== null) ? x : this.bombDragCurrentX;
        const finalY = (y !== undefined && y !== null) ? y : this.bombDragCurrentY;

        console.log(`💣 炸弹拖拽结束: 参数坐标(${x}, ${y}), 使用坐标(${finalX}, ${finalY})`);

        const gridPos = this.getGridPosition(finalX, finalY);

        if (gridPos.row >= 0 && gridPos.col >= 0) {
            // 在网格内释放，使用炸弹卡
            console.log(`💣 尝试在位置(${gridPos.row}, ${gridPos.col})使用炸弹卡`);
            if (this.core.useBombPropAt(gridPos.row, gridPos.col)) {
                console.log(`💣 炸弹卡使用成功: (${gridPos.row}, ${gridPos.col})`);
            } else {
                console.log('❌ 炸弹卡使用失败');
            }
        } else {
            console.log('❌ 炸弹卡拖拽取消 - 不在网格内');
        }

        // 重置炸弹拖拽状态（道具使用状态在useBombPropAt中重置）
        this.isDraggingBomb = false;
        this.bombDragStartX = -1;
        this.bombDragStartY = -1;
        this.bombDragCurrentX = -1;
        this.bombDragCurrentY = -1;

        // 如果没有成功使用炸弹卡，重置道具使用状态
        if (this.core.propUsing.isActive && this.core.propUsing.type === 'bomb') {
            console.log('炸弹卡拖拽取消，重置道具使用状态');
            this.core.propUsing.isActive = false;
            this.core.propUsing.type = null;
        }
    }
    
    // 处理按钮点击
    handleButtonClick(x, y) {
        const buttons = this.renderer.getButtons();

        // 如果显示通关弹框，优先处理通关弹框按钮
        if (this.core.showLevelCompleteUI && this.renderer.levelCompleteButton) {
            const button = this.renderer.levelCompleteButton;
            if (x >= button.x && x <= button.x + button.width &&
                y >= button.y && y <= button.y + button.height) {
                console.log('点击通关确定按钮');
                this.core.confirmNextLevel();
                return true;
            }
            return true; // 通关弹框显示时阻止其他操作
        }

        // 如果显示退出弹框，优先处理弹框按钮
        if (this.core.showExitDialog) {
            // 检查继续游戏按钮
            if (buttons.continueGame && this.renderer.isPointInButton(x, y, buttons.continueGame)) {
                console.log('点击继续游戏按钮');
                this.core.showExitDialog = false;
                return true;
            }

            // 检查返回主页按钮
            if (buttons.exitGame && this.renderer.isPointInButton(x, y, buttons.exitGame)) {
                console.log('点击返回主页按钮');
                this.core.showExitDialog = false;
                this.core.gameManager.switchToPage('main');
                return true;
            }

            // 点击弹框外部区域，关闭弹框
            const centerX = this.canvas.width / 2;
            const centerY = this.canvas.height / 2;
            const dialogWidth = 300;
            const dialogHeight = 180;
            const dialogX = centerX - dialogWidth / 2;
            const dialogY = centerY - dialogHeight / 2;

            if (x < dialogX || x > dialogX + dialogWidth || y < dialogY || y > dialogY + dialogHeight) {
                console.log('点击弹框外部，关闭弹框');
                this.core.showExitDialog = false;
                return true;
            }

            return true; // 弹框显示时阻止其他操作
        }

        // 检查返回按钮
        if (buttons.back && this.renderer.isPointInButton(x, y, buttons.back)) {
            console.log('点击返回按钮，显示确认弹框');
            this.core.showExitDialog = true;
            return true;
        }

        return false;
    }

    // 处理道具栏点击
    handlePropBarClick(x, y) {
        // 使用与渲染器相同的位置计算逻辑
        let propsBarConfig;
        if (typeof CONFIG_UTILS !== 'undefined') {
            propsBarConfig = CONFIG_UTILS.getPropsBarConfig(this.canvas.width, this.canvas.height);
        } else {
            // 回退到默认配置（与渲染器保持一致）
            const gridBottom = this.core.gridStartY + this.core.gridSizeY * this.core.blockSize;
            propsBarConfig = {
                x: this.canvas.width * 0.05,
                y: gridBottom + 10,  // 与渲染器一致
                width: this.canvas.width * 0.9,
                height: 80,
                buttonSize: 60,
                buttonSpacing: 20
            };
        }

        const barX = propsBarConfig.x;
        const propBarY = propsBarConfig.y;
        const barWidth = propsBarConfig.width;
        const barHeight = propsBarConfig.height;
        const propSize = propsBarConfig.buttonSize;

        console.log(`🎯 点击检测: 坐标(${x}, ${y}), 道具栏区域[${barX}, ${propBarY}, ${barWidth}, ${barHeight}]`);

        // 检查是否在道具栏区域内
        if (x < barX || x > barX + barWidth || y < propBarY || y > propBarY + barHeight) {
            console.log(`❌ 点击不在道具栏区域内`);
            return false;
        }

        // 使用与渲染器相同的布局计算
        const layoutMode = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.LAYOUT_MODE) ?
                          GAME_CONFIG.LAYOUT.PROPS_BAR.LAYOUT_MODE : 'EQUAL_SPLIT';
        const propCount = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.PROP_COUNT) ?
                         GAME_CONFIG.LAYOUT.PROPS_BAR.PROP_COUNT : 3;

        let startX, propSpacing;

        if (layoutMode === 'EQUAL_SPLIT') {
            // 平分布局：道具栏宽度平分成N份
            const sectionWidth = barWidth / propCount;
            propSpacing = sectionWidth;
            startX = barX + sectionWidth / 2; // 每个区域的中心点
        } else {
            // 传统间距布局
            const buttonSpacing = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.BUTTON_SPACING) ?
                                 GAME_CONFIG.LAYOUT.PROPS_BAR.BUTTON_SPACING : 20;
            const totalPropsWidth = propCount * propSize + (propCount - 1) * buttonSpacing;
            startX = barX + (barWidth - totalPropsWidth) / 2;
            propSpacing = propSize + buttonSpacing;
        }

        // 道具配置
        const props = [
            { type: 'refresh', count: this.core.props.refresh },
            { type: 'bomb', count: this.core.props.bomb },
            { type: 'clear', count: this.core.props.clear },
            { type: 'levelDown', count: this.core.props.levelDown }
        ];

        console.log(`📦 道具状态: 刷新${props[0].count} 炸弹${props[1].count} 清屏${props[2].count} 降级${props[3].count}`);
        console.log(`📐 布局参数: 模式=${layoutMode}, 起始X=${startX.toFixed(1)}, 间距=${propSpacing.toFixed(1)}`);

        for (let i = 0; i < props.length; i++) {
            let propX, propXEnd;

            if (layoutMode === 'EQUAL_SPLIT') {
                // 平分布局：每个道具在其区域中心
                propX = startX + i * propSpacing - propSize / 2;
                propXEnd = propX + propSize;
            } else {
                // 传统布局
                propX = startX + i * propSpacing;
                propXEnd = propX + propSize;
            }

            console.log(`🔍 检查道具${i}(${props[i].type}): X范围[${propX.toFixed(1)}, ${propXEnd.toFixed(1)}], 数量${props[i].count}`);

            if (x >= propX && x <= propXEnd) {
                if (props[i].count > 0) {
                    console.log(`✅ 点击道具: ${props[i].type}`);

                    // 炸弹卡需要拖拽，其他道具直接使用
                    if (props[i].type === 'bomb') {
                        // 开始拖拽炸弹卡
                        this.startBombDrag(x, y);
                    } else {
                        this.useProp(props[i].type);
                    }
                    return true;
                } else {
                    console.log(`❌ 道具${props[i].type}数量不足，已禁用`);
                    // 播放禁用音效
                    if (this.core.gameManager && this.core.gameManager.audioManager) {
                        this.core.gameManager.audioManager.playSound('error');
                    }
                    return false;
                }
            }
        }

        console.log(`❌ 点击不在任何道具按钮上`);
        return false;
    }

    // 开始拖拽炸弹卡
    startBombDrag(x, y) {
        if (this.core.props.bomb <= 0) {
            console.log('炸弹卡数量不足');
            return;
        }

        // 激活炸弹卡使用状态
        this.core.propUsing.isActive = true;
        this.core.propUsing.type = 'bomb';

        this.isDraggingBomb = true;
        this.bombDragStartX = x;
        this.bombDragStartY = y;
        this.bombDragCurrentX = x;
        this.bombDragCurrentY = y;

        console.log('开始拖拽炸弹卡，已激活使用状态');
    }

    // 使用道具
    useProp(propType) {
        console.log(`🎮 尝试使用道具: ${propType}`);

        switch (propType) {
            case 'refresh':
                console.log('🔄 调用刷新卡方法');
                if (this.core.useRefreshProp()) {
                    console.log('✅ 刷新卡使用成功');
                    // 播放道具音效
                    if (this.core.gameManager && this.core.gameManager.audioManager) {
                        this.core.gameManager.audioManager.playSound('prop');
                    }
                } else {
                    console.log('❌ 刷新卡使用失败');
                }
                break;

            case 'clear':
                console.log('✨ 调用清屏卡方法');
                if (this.core.useClearProp()) {
                    console.log('✅ 清屏卡使用成功');
                    // 播放道具音效
                    if (this.core.gameManager && this.core.gameManager.audioManager) {
                        this.core.gameManager.audioManager.playSound('prop');
                    }
                } else {
                    console.log('❌ 清屏卡使用失败');
                }
                break;

            case 'bomb':
                console.log('💣 调用炸弹卡方法');
                if (this.core.activateBombProp()) {
                    console.log('✅ 炸弹卡已激活，等待选择位置');
                    // 播放道具音效
                    if (this.core.gameManager && this.core.gameManager.audioManager) {
                        this.core.gameManager.audioManager.playSound('prop');
                    }
                } else {
                    console.log('❌ 炸弹卡激活失败');
                }
                break;

            case 'levelDown':
                console.log('⬇️ 调用降级卡方法');
                if (this.core.useLevelDownProp()) {
                    console.log('✅ 降级卡使用成功');
                    // 播放道具音效
                    if (this.core.gameManager && this.core.gameManager.audioManager) {
                        this.core.gameManager.audioManager.playSound('prop');
                    }
                } else {
                    console.log('❌ 降级卡使用失败');
                }
                break;

            default:
                console.warn('❌ 未知道具类型:', propType);
        }
    }
    
    // 处理网格触摸开始
    handleGridTouchStart(x, y) {
        if (this.core.isAnimating || this.core.isGameOver || this.core.isLevelComplete ||
            this.core.showExitDialog || this.core.showLevelCompleteUI) {
            return;
        }

        const gridPos = this.getGridPosition(x, y);
        if (gridPos.row >= 0 && gridPos.col >= 0) {
            // 检查是否正在使用炸弹卡
            if (this.core.propUsing.isActive && this.core.propUsing.type === 'bomb') {
                // 使用炸弹卡在指定位置
                this.core.useBombPropAt(gridPos.row, gridPos.col);
                return;
            }

            this.isDragging = true;
            this.dragStartRow = gridPos.row;
            this.dragStartCol = gridPos.col;

            // 重置连击计数 - 用户开始新的拖动操作时重置
            this.core.resetCombo();

            // 选中方块
            this.selectBlock(gridPos.row, gridPos.col);

            console.log(`选中方块: (${gridPos.row}, ${gridPos.col})`);
        }
    }
    
    // 处理网格触摸移动
    handleGridTouchMove(x, y) {
        if (!this.isDragging || this.dragStartRow < 0 || this.dragStartCol < 0) {
            return;
        }

        const gridPos = this.getGridPosition(x, y);
        if (gridPos.row >= 0 && gridPos.col >= 0) {
            // 更新当前拖拽结束位置
            this.dragEndRow = gridPos.row;
            this.dragEndCol = gridPos.col;

            // 检查是否移动到相邻方块
            if (this.isAdjacentBlock(this.dragStartRow, this.dragStartCol, gridPos.row, gridPos.col)) {
                // 高亮目标方块
                this.highlightTargetBlock(gridPos.row, gridPos.col);
                console.log(`拖拽到相邻方块: (${gridPos.row}, ${gridPos.col})`);
            } else {
                // 清除高亮（如果不是相邻方块）
                this.clearTargetHighlight();
            }
        }
    }
    
    // 处理网格触摸结束
    handleGridTouchEnd(x, y) {
        console.log(`触摸结束: 开始(${this.dragStartRow}, ${this.dragStartCol}), 结束(${this.dragEndRow}, ${this.dragEndCol})`);

        // 如果没有有效的拖拽开始位置，直接返回
        if (this.dragStartRow < 0 || this.dragStartCol < 0) {
            this.clearAllSelections();
            return;
        }

        // 如果提供了坐标，使用最新的坐标更新结束位置
        if (x !== undefined && y !== undefined) {
            const gridPos = this.getGridPosition(x, y);
            if (gridPos.row >= 0 && gridPos.col >= 0) {
                this.dragEndRow = gridPos.row;
                this.dragEndCol = gridPos.col;
            }
        }

        // 检查是否有有效的结束位置
        if (this.dragEndRow >= 0 && this.dragEndCol >= 0) {
            // 检查是否是相邻方块的有效交换
            if (this.isAdjacentBlock(this.dragStartRow, this.dragStartCol, this.dragEndRow, this.dragEndCol)) {
                console.log(`尝试交换相邻方块: (${this.dragStartRow}, ${this.dragStartCol}) <-> (${this.dragEndRow}, ${this.dragEndCol})`);
                this.attemptSwap(
                    this.dragStartRow, this.dragStartCol,
                    this.dragEndRow, this.dragEndCol
                );
            } else if (this.dragStartRow === this.dragEndRow && this.dragStartCol === this.dragEndCol) {
                // 单击同一个方块，处理点击事件
                console.log(`单击方块: (${this.dragStartRow}, ${this.dragStartCol})`);
                this.handleBlockClick(this.dragStartRow, this.dragStartCol);
            } else {
                console.log('无效的拖拽：不是相邻方块');
            }
        } else {
            console.log('无效的拖拽：没有有效的结束位置');
        }

        // 清除所有选中状态和高亮
        this.clearAllSelections();
        this.clearTargetHighlight();
    }
    
    // 获取网格位置（考虑间距）
    getGridPosition(x, y) {
        const spacing = this.core.getSpacing();
        const blockWithSpacing = this.core.blockSize + spacing;

        const col = Math.floor((x - this.core.gridStartX) / blockWithSpacing);
        const row = Math.floor((y - this.core.gridStartY) / blockWithSpacing);

        console.log(`🎯 网格位置检测: 点击(${x}, ${y}) → 网格(${row}, ${col}), 网格范围[0-${this.core.gridSizeY-1}, 0-${this.core.gridSizeX-1}]`);

        if (row >= 0 && row < this.core.gridSizeY && col >= 0 && col < this.core.gridSizeX) {
            return { row, col };
        }

        return { row: -1, col: -1 };
    }
    
    // 检查是否是相邻方块
    isAdjacentBlock(row1, col1, row2, col2) {
        const rowDiff = Math.abs(row1 - row2);
        const colDiff = Math.abs(col1 - col2);
        
        return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
    }
    
    // 选中方块
    selectBlock(row, col) {
        if (this.core.grid[row] && this.core.grid[row][col]) {
            // 清除之前的选中状态
            this.clearAllSelections();

            // 选中当前方块
            const block = this.core.grid[row][col];
            block.isSelected = true;
            block.isDragging = true; // 标记为拖拽状态
            block.dragStartTime = Date.now(); // 记录拖拽开始时间
            block.originalScale = block.scale || 1; // 保存原始缩放
            block.scale = 1.1; // 轻微放大

            this.core.selectedBlock = { row, col };

            // 创建选中特效
            this.core.createSparkles(
                block.x + this.core.blockSize / 2,
                block.y + this.core.blockSize / 2,
                5 // 增加粒子数量
            );

            // 添加选中音效
            if (this.core.gameManager && this.core.gameManager.audioManager) {
                this.core.gameManager.audioManager.playSound('select');
            }
        }
    }
    
    // 高亮目标方块
    highlightTargetBlock(row, col) {
        // 先清除之前的目标高亮
        this.clearTargetHighlight();

        if (this.core.grid[row] && this.core.grid[row][col]) {
            const block = this.core.grid[row][col];
            block.isTargetHighlighted = true;
            block.highlightStartTime = Date.now(); // 记录高亮开始时间
            block.originalScale = block.scale || 1; // 保存原始缩放
            block.scale = 1.05; // 轻微放大

            this.targetHighlightRow = row;
            this.targetHighlightCol = col;

            // 创建目标高亮特效
            this.core.createSparkles(
                block.x + this.core.blockSize / 2,
                block.y + this.core.blockSize / 2,
                3
            );
        }
    }

    // 清除目标高亮
    clearTargetHighlight() {
        if (this.targetHighlightRow >= 0 && this.targetHighlightCol >= 0) {
            const block = this.core.grid[this.targetHighlightRow] && this.core.grid[this.targetHighlightRow][this.targetHighlightCol];
            if (block) {
                block.isTargetHighlighted = false;
                // 恢复原始缩放
                if (block.originalScale !== undefined) {
                    block.scale = block.originalScale;
                    delete block.originalScale;
                }
            }
        }
        this.targetHighlightRow = -1;
        this.targetHighlightCol = -1;
    }

    // 清除所有选中状态
    clearAllSelections() {
        for (let row = 0; row < this.core.gridSizeY; row++) {
            for (let col = 0; col < this.core.gridSizeX; col++) {
                const block = this.core.grid[row] && this.core.grid[row][col];
                if (block) {
                    block.isSelected = false;
                    block.isTargetHighlighted = false;
                    block.isDragging = false;

                    // 恢复原始缩放
                    if (block.originalScale !== undefined) {
                        block.scale = block.originalScale;
                        delete block.originalScale;
                    } else if (block.scale !== 1) {
                        // 平滑恢复到原始大小
                        this.animateScaleRestore(block);
                    }
                }
            }
        }
        this.core.selectedBlock = null;
        this.targetHighlightRow = -1;
        this.targetHighlightCol = -1;
    }

    // 平滑恢复缩放动画
    animateScaleRestore(block) {
        const startScale = block.scale;
        const targetScale = 1;
        const duration = 200; // 200ms
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeProgress = 1 - Math.pow(1 - progress, 3); // easeOutCubic
            block.scale = startScale + (targetScale - startScale) * easeProgress;

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                block.scale = targetScale;
            }
        };

        animate();
    }
    
    // 处理方块点击
    handleBlockClick(row, col) {
        if (this.core.selectedBlock) {
            // 如果已经有选中的方块，尝试交换
            if (this.core.selectedBlock.row !== row || this.core.selectedBlock.col !== col) {
                if (this.isAdjacentBlock(this.core.selectedBlock.row, this.core.selectedBlock.col, row, col)) {
                    this.attemptSwap(this.core.selectedBlock.row, this.core.selectedBlock.col, row, col);
                } else {
                    // 选中新的方块
                    this.selectBlock(row, col);
                }
            } else {
                // 取消选中
                this.clearAllSelections();
            }
        } else {
            // 选中方块
            this.selectBlock(row, col);
        }
    }
    
    // 尝试交换方块 - 重写简化版本
    async attemptSwap(row1, col1, row2, col2) {
        console.log(`尝试交换方块: (${row1}, ${col1}) <-> (${row2}, ${col2})`);

        // 检查方块是否存在
        const block1 = this.core.grid[row1] && this.core.grid[row1][col1];
        const block2 = this.core.grid[row2] && this.core.grid[row2][col2];

        if (!block1 || !block2) {
            console.log('交换失败：方块不存在');
            return false;
        }

        // 防止游戏状态异常时的交换
        if (this.core.isAnimating || this.core.isGameOver || this.core.isLevelComplete || this.core.showLevelCompleteUI) {
            console.log('交换失败：游戏状态异常');
            return false;
        }

        // 1. 先执行交换
        this.swapBlocks(row1, col1, row2, col2);
        console.log('已执行方块交换');

        // 2. 检查交换后是否有匹配
        const hasMatches = this.checkForMatchesAfterSwap(row1, col1, row2, col2);

        if (hasMatches) {
            console.log('交换成功：发现匹配，开始处理消除');
            // 有匹配，使用动画控制器处理后续的匹配和掉落
            if (this.core.animator) {
                // 延迟处理匹配，让交换动画先完成
                setTimeout(async () => {
                    try {
                        const swapResult = {
                            success: true,
                            from: { row: row1, col: col1 },
                            to: { row: row2, col: col2 }
                        };

                        const result = await this.core.animator.processSwapResult(swapResult);
                        console.log('动画控制器处理结果:', result);
                    } catch (error) {
                        console.error('处理匹配时出错:', error);
                    }
                }, 100);
            } else {
                // 如果没有动画控制器，使用简单的匹配处理
                console.log('使用简单匹配处理');
                this.handleSimpleMatches(row1, col1, row2, col2);
            }
            return true;
        } else {
            console.log('交换失败：无匹配，还原交换');
            // 没有匹配，延迟还原交换
            setTimeout(() => {
                this.swapBlocks(row1, col1, row2, col2);
                console.log('交换已还原');
            }, 200);
            return false;
        }
    }

    // 检查交换后是否有匹配 - 修复版，支持特殊方块交换
    checkForMatchesAfterSwap(row1, col1, row2, col2) {
        console.log(`=== 检查交换后匹配 ===`);
        console.log(`交换位置: (${row1},${col1}) <-> (${row2},${col2})`);

        const block1 = this.core.grid[row1] && this.core.grid[row1][col1];
        const block2 = this.core.grid[row2] && this.core.grid[row2][col2];

        // 检查是否有特殊方块参与交换
        const hasSpecialBlock = (block1 && (block1.blockType === 'special' || block1.category === 'special')) ||
                               (block2 && (block2.blockType === 'special' || block2.category === 'special'));

        if (hasSpecialBlock) {
            console.log('检测到特殊方块交换，允许交换');
            console.log(`方块1: ${block1?.type} (${block1?.blockType || block1?.category})`);
            console.log(`方块2: ${block2?.type} (${block2?.blockType || block2?.category})`);
            return true; // 特殊方块可以和任何方块交换
        }

        // 普通方块的匹配检查
        // 方法1：检查两个交换位置
        const matches1 = this.core.matcher.findMatchesAt(row1, col1);
        const matches2 = this.core.matcher.findMatchesAt(row2, col2);
        const localMatches = (matches1 && matches1.length >= 3) || (matches2 && matches2.length >= 3);

        console.log(`局部匹配检查: 位置1(${row1},${col1})=${matches1?.length || 0}个匹配, 位置2(${row2},${col2})=${matches2?.length || 0}个匹配`);

        // 方法2：检查整个网格（更可靠）
        const allMatches = this.core.matcher.findAllMatches();
        const globalMatches = allMatches && allMatches.matches.length > 0;

        console.log(`全局匹配检查: 发现${allMatches?.matches?.length || 0}个匹配方块`);

        const hasMatches = localMatches || globalMatches;
        console.log(`最终结果: ${hasMatches ? '有匹配' : '无匹配'}`);

        return hasMatches;
    }

    // 简单匹配处理（备用方案）- 修复连锁消除检测
    async handleSimpleMatches(row1, col1, row2, col2) {
        console.log('=== 开始简单匹配处理 ===');
        console.log(`交换位置: (${row1},${col1}) <-> (${row2},${col2})`);

        // 设置动画状态
        this.core.isAnimating = true;

        // 检查matcher是否可用
        if (!this.core.matcher) {
            console.error('匹配器不可用，无法处理匹配');
            this.core.isAnimating = false;
            return;
        }

        try {
            // 循环处理匹配和掉落，直到没有新的匹配
            let hasMoreMatches = true;
            let totalScore = 0;
            let roundCount = 0;

            while (hasMoreMatches) {
                roundCount++;
                console.log(`开始第${roundCount}轮简单匹配处理`);

                // 查找所有匹配
                const allMatches = this.core.matcher.findAllMatches();
                console.log(`第${roundCount}轮匹配查找结果:`, allMatches);

                if (allMatches && allMatches.matches.length > 0) {
                    console.log(`第${roundCount}轮发现${allMatches.matches.length}个匹配方块，${allMatches.matchGroups?.length || 0}个匹配组`);

                    // 处理匹配消除
                    const result = await this.core.matcher.processMatches(allMatches);
                    console.log(`第${roundCount}轮匹配处理完成:`, result);

                    // 累计分数
                    if (result.score > 0) {
                        totalScore += result.score;
                        console.log(`第${roundCount}轮得分: +${result.score}, 累计得分: ${totalScore}`);
                    }

                    // 处理掉落
                    if (result.removedPositions && result.removedPositions.length > 0) {
                        console.log(`第${roundCount}轮处理掉落: ${result.removedPositions.length}个位置`);
                        if (this.core.animator && this.core.animator.falling) {
                            // 等待掉落完成
                            await this.core.animator.falling.processFalling(result.removedPositions);
                            console.log(`第${roundCount}轮掉落处理完成`);
                        } else {
                            console.warn('掉落系统不可用');
                        }
                    } else {
                        console.log(`第${roundCount}轮没有需要掉落的位置`);
                    }
                } else {
                    hasMoreMatches = false;
                    console.log(`第${roundCount}轮无新匹配，结束处理`);
                }
            }

            // 更新总分数
            if (totalScore > 0) {
                this.core.updateScore(totalScore);
                console.log(`简单匹配处理完成，总轮数: ${roundCount}, 总得分: ${totalScore}`);
            }

            // 检查游戏状态
            this.core.checkGameStatus();

        } catch (error) {
            console.error('简单匹配处理出错:', error);
        } finally {
            // 重置动画状态
            this.core.isAnimating = false;
            console.log('简单匹配处理完成，动画状态已重置');
        }

        console.log('=== 简单匹配处理完成 ===');
    }

    // 交换方块 - 简化版本
    swapBlocks(row1, col1, row2, col2) {
        const block1 = this.core.grid[row1][col1];
        const block2 = this.core.grid[row2][col2];

        if (!block1 || !block2) {
            console.error('交换失败：方块不存在');
            return;
        }

        // 保存原始位置信息
        const pos1 = { x: block1.x, y: block1.y, row: row1, col: col1 };
        const pos2 = { x: block2.x, y: block2.y, row: row2, col: col2 };

        // 交换方块的类型和属性，但保持位置信息
        const tempType = block1.type;
        const tempSpecial = block1.special;
        const tempSpecialType = block1.specialType;
        const tempCategory = block1.category;
        const tempBlockType = block1.blockType;

        // 更新block1为block2的属性
        block1.type = block2.type;
        block1.special = block2.special;
        block1.specialType = block2.specialType;
        block1.category = block2.category;
        block1.blockType = block2.blockType;

        // 更新block2为block1的原属性
        block2.type = tempType;
        block2.special = tempSpecial;
        block2.specialType = tempSpecialType;
        block2.category = tempCategory;
        block2.blockType = tempBlockType;

        // 添加交换动画效果
        this.addSwapAnimation(row1, col1, row2, col2);

        console.log(`方块交换完成: (${row1},${col1})[${block1.type}] <-> (${row2},${col2})[${block2.type}]`);
    }
    
    // 添加交换动画
    addSwapAnimation(row1, col1, row2, col2) {
        // 简单的缩放动画
        const block1 = this.core.grid[row1][col1];
        const block2 = this.core.grid[row2][col2];
        
        if (block1 && block2) {
            block1.scale = 1.2;
            block2.scale = 1.2;
            
            setTimeout(() => {
                if (block1) block1.scale = 1;
                if (block2) block2.scale = 1;
            }, 200);
        }
    }
    
    // 处理匹配
    handleMatches(matches) {
        console.log(`发现 ${matches.length} 个匹配`);
        
        this.core.isAnimating = true;
        
        // 计算分数
        const totalScore = matches.length * 10;
        this.core.score += totalScore;
        // 更新全局静态参数
        GamePageCore.score += totalScore;
        // 计算进度值
        GamePageCore.progress = Math.min(100, Math.floor((GamePageCore.score / GamePageCore.targetScore) * 100));
        
        // 创建特效
        matches.forEach(match => {
            const block = this.core.grid[match.row][match.col];
            if (block) {
                // 粒子效果
                this.core.createParticles(
                    block.x + this.core.blockSize / 2,
                    block.y + this.core.blockSize / 2,
                    block.color,
                    8
                );
                
                // 闪烁效果
                this.core.createSparkles(
                    block.x + this.core.blockSize / 2,
                    block.y + this.core.blockSize / 2,
                    5
                );
            }
        });
        
        // 显示得分文字（使用逻辑像素）
        this.core.createFloatingText(
            this.core.logicalWidth / 2,
            this.core.logicalHeight / 2,
            `+${totalScore}`,
            '#FFD700'
        );
        
        // 移除匹配的方块
        setTimeout(() => {
            this.removeMatches(matches);
        }, 300);
    }
    
    // 移除匹配的方块
    removeMatches(matches) {
        matches.forEach(match => {
            if (this.core.grid[match.row] && this.core.grid[match.row][match.col]) {
                // 保存原始位置
                const originalX = this.core.grid[match.row][match.col].x;
                const originalY = this.core.grid[match.row][match.col].y;
                
                // 使用核心类的方法创建新方块
                const newBlock = this.core.createRandomBlock(
                    match.row, 
                    match.col, 
                    originalX - match.col * this.core.blockSize,
                    originalY - match.row * this.core.blockSize
                );
                
                if (newBlock) {
                    // 设置初始动画状态
                    newBlock.scale = 0.5;
                    newBlock.alpha = 0.8;
                    
                    this.core.grid[match.row][match.col] = newBlock;
                    
                    // 添加出现动画
                    let scale = 0.5;
                    const scaleInterval = setInterval(() => {
                        scale += 0.1;
                        if (newBlock) newBlock.scale = scale;
                        if (scale >= 1) {
                            clearInterval(scaleInterval);
                            if (newBlock) newBlock.scale = 1;
                        }
                    }, 50);
                }
            }
        });
        
        // 检查是否还有新的匹配
        setTimeout(() => {
            const newMatches = this.core.checkForMatches();
            if (newMatches.length > 0) {
                this.handleMatches(newMatches);
            } else {
                this.core.isAnimating = false;
                this.core.combo = 0; // 重置连击
            }
        }, 500);
    }
    
    // 处理暂停游戏
    handlePauseGame() {
        // 这里可以添加暂停游戏的逻辑
        console.log('游戏暂停功能待实现');
    }
    
    // 清理事件监听器
    destroy() {
        console.log('清理GamePageEvents资源');
        
        if (typeof tt !== 'undefined') {
            if (this.touchStartHandler) {
                tt.offTouchStart(this.touchStartHandler);
            }
            if (this.touchMoveHandler) {
                tt.offTouchMove(this.touchMoveHandler);
            }
            if (this.touchEndHandler) {
                tt.offTouchEnd(this.touchEndHandler);
            }
            console.log('抖音小游戏事件监听器已清理');
        }
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GamePageEvents };
} else {
    window.GamePageEvents = GamePageEvents;
}