/**
 * 游戏页面渲染器 - 简洁美化版本
 * 负责所有视觉效果的渲染，无浮动动画
 */

// 添加 roundRect 兼容性支持（小程序环境兼容）
function addRoundRectSupport(ctx) {
    if (ctx && !ctx.roundRect) {
        ctx.roundRect = function(x, y, width, height, radius) {
            if (typeof radius === 'undefined') {
                radius = 5;
            }
            if (typeof radius === 'number') {
                radius = {tl: radius, tr: radius, br: radius, bl: radius};
            } else {
                var defaultRadius = {tl: 0, tr: 0, br: 0, bl: 0};
                for (var side in defaultRadius) {
                    radius[side] = radius[side] || defaultRadius[side];
                }
            }
            this.moveTo(x + radius.tl, y);
            this.lineTo(x + width - radius.tr, y);
            this.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
            this.lineTo(x + width, y + height - radius.br);
            this.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
            this.lineTo(x + radius.bl, y + height);
            this.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
            this.lineTo(x, y + radius.tl);
            this.quadraticCurveTo(x, y, x + radius.tl, y);
            this.closePath();
            return this;
        };
    }
}

// 尝试为全局 CanvasRenderingContext2D 添加支持（如果存在）
if (typeof CanvasRenderingContext2D !== 'undefined' && CanvasRenderingContext2D.prototype && !CanvasRenderingContext2D.prototype.roundRect) {
    CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
        if (typeof radius === 'undefined') {
            radius = 5;
        }
        if (typeof radius === 'number') {
            radius = {tl: radius, tr: radius, br: radius, bl: radius};
        } else {
            var defaultRadius = {tl: 0, tr: 0, br: 0, bl: 0};
            for (var side in defaultRadius) {
                radius[side] = radius[side] || defaultRadius[side];
            }
        }
        this.moveTo(x + radius.tl, y);
        this.lineTo(x + width - radius.tr, y);
        this.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
        this.lineTo(x + width, y + height - radius.br);
        this.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
        this.lineTo(x + radius.bl, y + height);
        this.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
        this.lineTo(x, y + radius.tl);
        this.quadraticCurveTo(x, y, x + radius.tl, y);
        this.closePath();
        return this;
    };
}

class GamePageRenderer {
    constructor(core) {
        this.core = core;
        this.canvas = core.canvas;
        this.ctx = core.ctx;

        // 获取逻辑尺寸（用于高分辨率支持）
        this.width = core.gameManager.logicalWidth || core.canvas.width;
        this.height = core.gameManager.logicalHeight || core.canvas.height;

        // 为当前上下文添加 roundRect 支持
        addRoundRectSupport(this.ctx);

        // 动画时间
        this.animationTime = 0;

        // 根据关卡配置颜色主题
        this.initLevelColors();

        // 使用全局配置的颜色
        const config = typeof GAME_CONFIG !== 'undefined' ? GAME_CONFIG : {};
        this.colors = {
            background: this.levelColors,
            grid: config.COLORS ? {
                background: config.COLORS.GRID_BACKGROUND,
                border: config.COLORS.GRID_BORDER,
                shadow: config.COLORS.GRID_SHADOW
            } : {
                background: 'rgba(255, 255, 255, 0.8)',
                border: '#FFFFFF',
                shadow: 'rgba(0, 0, 0, 0.1)'
            },
            animals: config.COLORS ? config.COLORS.ANIMALS : {
                'cat': { bg: '#FF6B9D', glow: '#FF8FB3' },
                'dog': { bg: '#4ECDC4', glow: '#6ED5CD' },
                'elephant': { bg: '#45B7D1', glow: '#65C7E1' },
                'fox': { bg: '#96CEB4', glow: '#A6DEC4' },
                'frog': { bg: '#FFEAA7', glow: '#FFEFB7' },
                'monkey': { bg: '#DDA0DD', glow: '#E7B0E7' },
                'panda': { bg: '#98D8C8', glow: '#A8E8D8' },
                'rabbit': { bg: '#F7DC6F', glow: '#F9E67F' },
                'tiger': { bg: '#BB8FCE', glow: '#CB9FDE' }
            },
            special: config.COLORS ? config.COLORS.SPECIAL : {
                'rocket': { bg: '#FF4500', glow: '#FF6347' },
                'bomb': { bg: '#8B0000', glow: '#DC143C' }
            }
        };
        
        console.log('GamePageRenderer初始化完成');
    }

    // 安全获取全局游戏数据
    getGlobalGameData(key, defaultValue = 0) {
        try {
            if (typeof window !== 'undefined' && window[key] !== undefined) {
                return window[key];
            } else if (typeof globalThis !== 'undefined' && globalThis[key] !== undefined) {
                return globalThis[key];
            } else if (typeof global !== 'undefined' && global[key] !== undefined) {
                return global[key];
            }
        } catch (error) {
            console.warn(`获取全局数据 ${key} 失败:`, error);
        }

        // 尝试从 core 实例获取
        if (this.core && this.core.getGlobalGameData) {
            const value = this.core.getGlobalGameData(key);
            if (value !== undefined) {
                return value;
            }
        }

        return defaultValue;
    }

    // 根据关卡初始化颜色主题（使用全局配置）
    initLevelColors() {
        const level = this.core.level || 1;
        const config = typeof GAME_CONFIG !== 'undefined' ? GAME_CONFIG : {};

        // 使用全局配置的关卡主题颜色
        if (config.COLORS && config.COLORS.LEVEL_THEMES) {
            this.levelColors = config.COLORS.LEVEL_THEMES[level] || config.COLORS.LEVEL_THEMES[1];
        } else {
            // 回退到默认主题
            switch (level) {
                case 1:
                    this.levelColors = {
                        primary: '#E8F5E8',
                        secondary: '#B8E6B8',
                        accent: '#87CEEB',
                        gradient: ['#E8F5E8', '#B8E6B8', '#87CEEB', '#98FB98']
                    };
                    break;
                case 2:
                    this.levelColors = {
                        primary: '#F0E6FF',
                        secondary: '#DDA0DD',
                        accent: '#9370DB',
                        gradient: ['#F0E6FF', '#DDA0DD', '#9370DB', '#BA55D3']
                    };
                    break;
                case 3:
                default:
                    this.levelColors = {
                        primary: '#FFE5F1',
                        secondary: '#FFB6C1',
                        accent: '#FF69B4',
                        gradient: ['#FFE5F1', '#FFB6C1', '#FF69B4', '#FF1493']
                    };
                    break;
            }
        }

        console.log(`关卡${level}使用颜色主题:`, this.levelColors);
    }
    
    // 主渲染方法
    render() {
        this.animationTime += 0.016; // 60fps
        
        // 清空画布
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // 渲染背景
        this.renderBackground();
        
        // 渲染UI界面
        this.renderUI();
        
        // 渲染游戏网格
        this.renderGrid();

        // 渲染道具栏
        this.renderPropBar();

        // 渲染特效
        this.renderEffects();

        // 渲染游戏状态
        this.renderGameStatus();

        // 渲染退出确认对话框
        if (this.core.showExitDialog) {
            this.renderExitDialog();
        }

        // 渲染炸弹卡拖拽
        if (this.core.events && this.core.events.isDraggingBomb) {
            this.renderBombDrag();
        }

        // 渲染漩涡效果
        if (this.core.vortexEffect && this.core.vortexEffect.active) {
            this.renderVortexEffect();
        }

        // 渲染闪光效果
        if (this.core.flashEffect && this.core.flashEffect.active) {
            this.renderFlashEffect();
        }
    }
    
    // 渲染静态背景 - 简洁版本
    renderBackground() {
        // 使用简单的线性渐变背景
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);

        // 根据关卡使用不同的静态颜色
        const colors = this.levelColors.gradient;
        if (colors && colors.length >= 2) {
            gradient.addColorStop(0, colors[0]);
            gradient.addColorStop(1, colors[colors.length - 1]);
        } else {
            // 默认静态渐变
            gradient.addColorStop(0, '#F0F8FF');  // 爱丽丝蓝
            gradient.addColorStop(1, '#E6E6FA');  // 薰衣草
        }

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.width, this.height);
    }
    
    // 渲染静态装饰 - 已移除复杂装饰，保持简洁
    renderStaticDecorations() {
        // 静态背景不需要装饰元素
        // 保留方法以防其他地方调用，但不执行任何绘制
    }
    
    // 渲染UI界面 - 使用新的布局配置
    renderUI() {
        // 使用新的布局配置
        let statsBarConfig;
        if (typeof CONFIG_UTILS !== 'undefined') {
            statsBarConfig = CONFIG_UTILS.getStatsBarConfig(this.width, this.height);
        } else {
            // 回退到默认配置
            statsBarConfig = {
                x: this.width * 0.05,
                y: 70,
                width: this.width * 0.9,
                height: 120
            };
        }

        // 顶部信息栏背景
        const headerX = statsBarConfig.x;
        const headerY = statsBarConfig.y;
        const headerWidth = statsBarConfig.width;
        const headerHeight = statsBarConfig.height;

        const headerGradient = this.ctx.createLinearGradient(headerX, headerY, headerX, headerY + headerHeight);
        headerGradient.addColorStop(0, 'rgba(135, 206, 250, 0.95)'); // 天蓝色
        headerGradient.addColorStop(1, 'rgba(70, 130, 180, 0.8)'); // 钢蓝色

        // 统计栏背景（圆角）
        this.ctx.fillStyle = headerGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(headerX, headerY, headerWidth, headerHeight, 12);
        this.ctx.fill();

        // 添加装饰边框（圆角）
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.roundRect(headerX + 5, headerY + 5, headerWidth - 10, headerHeight - 10, 8);
        this.ctx.stroke();

        // 关卡名称
        this.ctx.fillStyle = '#FF1493';
        this.ctx.font = 'bold 28px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 3;
        this.ctx.strokeText(this.core.levelName, this.width / 2, headerY + 30);
        this.ctx.fillText(this.core.levelName, this.width / 2, headerY + 30);

        // 分4个模块显示：分数、目标、进度、连击
        const moduleWidth = (this.width - 60) / 4;
        const moduleY = headerY + 75;

        // 分数模块 - 使用全局变量，去掉白色描边
        this.ctx.fillStyle = '#4169E1';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`分数`, 30 + moduleWidth * 0.5, moduleY - 15);
        this.ctx.fillText(`${this.getGlobalGameData('gameScore', 0)}`, 30 + moduleWidth * 0.5, moduleY + 10);

        // 目标模块 - 使用全局变量，去掉白色描边
        this.ctx.fillStyle = '#32CD32';
        this.ctx.fillText(`目标`, 30 + moduleWidth * 1.5, moduleY - 15);
        this.ctx.fillText(`${this.getGlobalGameData('gameTargetScore', 0)}`, 30 + moduleWidth * 1.5, moduleY + 10);

        // 进度模块 - 使用全局变量，去掉白色描边
        const progressPercent = Math.round(this.getGlobalGameData('gameProgress', 0) * 100);
        this.ctx.fillStyle = '#FF69B4';
        this.ctx.fillText(`进度`, 30 + moduleWidth * 2.5, moduleY - 15);
        this.ctx.fillText(`${progressPercent}%`, 30 + moduleWidth * 2.5, moduleY + 10);

        // 连击模块 - 新增，显示最大连击数
        const maxCombo = this.core.maxCombo || 0;
        this.ctx.fillStyle = '#FF6347'; // 番茄红色
        this.ctx.fillText(`连击`, 30 + moduleWidth * 3.5, moduleY - 15);
        this.ctx.fillText(`${maxCombo}`, 30 + moduleWidth * 3.5, moduleY + 10);

        // 返回按钮（使用新的布局配置）
        let backButtonConfig;
        if (typeof CONFIG_UTILS !== 'undefined') {
            const layoutCalc = CONFIG_UTILS.calculateLayout(this.canvas.width, this.canvas.height);
            backButtonConfig = layoutCalc.backButton;
        } else {
            backButtonConfig = { x: 20, y: 30, width: 100, height: 32 };
        }
        this.renderBackButton(backButtonConfig.x, backButtonConfig.y, backButtonConfig.width, backButtonConfig.height, '返回');
    }

    // 移除了进度条渲染方法

    // 渲染道具栏（使用新的布局配置）
    renderPropBar() {
        // 使用新的布局配置
        let propsBarConfig;
        if (typeof CONFIG_UTILS !== 'undefined') {
            propsBarConfig = CONFIG_UTILS.getPropsBarConfig(this.width, this.height);
        } else {
            // 回退到默认配置
            const gridStartY = this.core.gridStartY || 280;
            const gridBottom = gridStartY + this.core.gridSizeY * this.core.blockSize;
            propsBarConfig = {
                x: this.width * 0.05,
                y: gridBottom + 10,
                width: this.width * 0.9,
                height: 80,
                buttonSize: 60,
                buttonSpacing: 20
            };
        }

        const barX = propsBarConfig.x;
        const propBarY = propsBarConfig.y;
        const barWidth = propsBarConfig.width;
        const barHeight = propsBarConfig.height;
        const propSize = propsBarConfig.buttonSize;

        // 道具布局计算（支持配置化）
        const layoutMode = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.LAYOUT_MODE) ?
                          GAME_CONFIG.LAYOUT.PROPS_BAR.LAYOUT_MODE : 'EQUAL_SPLIT';
        const propCount = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.PROP_COUNT) ?
                         GAME_CONFIG.LAYOUT.PROPS_BAR.PROP_COUNT : 3;

        let startX, propSpacing;

        if (layoutMode === 'EQUAL_SPLIT') {
            // 平分布局：道具栏宽度平分成N份
            const sectionWidth = barWidth / propCount;
            propSpacing = sectionWidth;
            startX = barX + sectionWidth / 2; // 每个区域的中心点

            //console.log(`道具栏平分布局: 总宽度${barWidth}px, ${propCount}个道具, 每份${sectionWidth.toFixed(1)}px`);
        } else {
            // 传统间距布局
            const buttonSpacing = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.BUTTON_SPACING) ?
                                 GAME_CONFIG.LAYOUT.PROPS_BAR.BUTTON_SPACING : 20;
            const totalPropsWidth = propCount * propSize + (propCount - 1) * buttonSpacing;
            startX = barX + (barWidth - totalPropsWidth) / 2;
            propSpacing = propSize + buttonSpacing;
        }

        // 美化的渐变背景（浅色调，添加圆角）
        const propGradient = this.ctx.createLinearGradient(barX, propBarY, barX, propBarY + barHeight);
        propGradient.addColorStop(0, 'rgba(135, 206, 250, 0.7)'); // 浅天蓝色
        propGradient.addColorStop(1, 'rgba(100, 149, 237, 0.6)'); // 浅蓝色

        this.ctx.fillStyle = propGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(barX, propBarY, barWidth, barHeight, 12);
        this.ctx.fill();

        // 美化的边框（圆角）
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.roundRect(barX, propBarY, barWidth, barHeight, 12);
        this.ctx.stroke();

        // 内层装饰边框（圆角）
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.6)'; // 金色
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.roundRect(barX + 3, propBarY + 3, barWidth - 6, barHeight - 6, 8);
        this.ctx.stroke();

        // 道具配置（使用配置系统）
        let props;
        if (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.PROPS) {
            props = [
                {
                    type: 'refresh',
                    name: GAME_CONFIG.PROPS.NAMES.refresh,
                    shortName: GAME_CONFIG.PROPS.SHORT_NAMES.refresh,
                    count: this.core.props.refresh,
                    color: GAME_CONFIG.PROPS.COLORS.refresh,
                    icon: GAME_CONFIG.PROPS.ICONS.refresh,
                    image: GAME_CONFIG.PROPS.IMAGES.refresh
                },
                {
                    type: 'bomb',
                    name: GAME_CONFIG.PROPS.NAMES.bomb,
                    shortName: GAME_CONFIG.PROPS.SHORT_NAMES.bomb,
                    count: this.core.props.bomb,
                    color: GAME_CONFIG.PROPS.COLORS.bomb,
                    icon: GAME_CONFIG.PROPS.ICONS.bomb,
                    image: GAME_CONFIG.PROPS.IMAGES.bomb
                },
                {
                    type: 'clear',
                    name: GAME_CONFIG.PROPS.NAMES.clear,
                    shortName: GAME_CONFIG.PROPS.SHORT_NAMES.clear,
                    count: this.core.props.clear,
                    color: GAME_CONFIG.PROPS.COLORS.clear,
                    icon: GAME_CONFIG.PROPS.ICONS.clear,
                    image: GAME_CONFIG.PROPS.IMAGES.clear
                },
                {
                    type: 'levelDown',
                    name: GAME_CONFIG.PROPS.NAMES.levelDown,
                    shortName: GAME_CONFIG.PROPS.SHORT_NAMES.levelDown,
                    count: this.core.props.levelDown,
                    color: GAME_CONFIG.PROPS.COLORS.levelDown,
                    icon: GAME_CONFIG.PROPS.ICONS.levelDown,
                    image: GAME_CONFIG.PROPS.IMAGES.levelDown
                }
            ];
        } else {
            // 回退到默认配置
            props = [
                { type: 'refresh', name: '刷新卡', shortName: '刷新', count: this.core.props.refresh, color: '#4CAF50', icon: '🔄' },
                { type: 'bomb', name: '炸弹卡', shortName: '炸弹', count: this.core.props.bomb, color: '#F44336', icon: '💣' },
                { type: 'clear', name: '清屏卡', shortName: '清屏', count: this.core.props.clear, color: '#2196F3', icon: '✨' },
                { type: 'levelDown', name: '降级卡', shortName: '降级', count: this.core.props.levelDown, color: '#FF9800', icon: '⬇️' }
            ];
        }

        // 自动计算道具卡尺寸和间距
        const availableHeight = barHeight;
        const textHeight = 16; // 预估文字高度
        const verticalPadding = 8; // 上下总间距
        const maxCardSize = availableHeight - textHeight - verticalPadding;

        // 道具卡大小：完全覆盖背景框，但要为文字留空间
        const cardSize = Math.min(maxCardSize, propSize);

        // 计算垂直居中的间距
        const totalContentHeight = cardSize + textHeight;
        const topMargin = (availableHeight - totalContentHeight) / 2;
        const textMarginTop = 4; // 卡片和文字之间的小间距

        // 根据卡片大小调整字体
        const countFontSize = Math.max(10, Math.min(14, cardSize * 0.25));
        const nameFontSize = Math.max(10, Math.min(12, cardSize * 0.2));

        props.forEach((prop, index) => {
            // 计算道具按钮的X坐标
            let x;
            if (layoutMode === 'EQUAL_SPLIT') {
                // 平分布局：每个道具在其区域内居中
                x = startX + index * propSpacing - cardSize / 2;
            } else {
                // 传统布局
                x = startX + index * propSpacing;
            }

            const y = propBarY + topMargin;  // 垂直居中

            // 道具卡背景（完全覆盖背景框）
            this.ctx.save();
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            this.ctx.shadowBlur = 5;
            this.ctx.shadowOffsetX = 2;
            this.ctx.shadowOffsetY = 2;

            // 根据数量决定按钮状态
            const isAvailable = prop.count > 0;

            // 获取圆角半径，根据卡片大小调整
            const buttonRadius = Math.min(8, cardSize * 0.15);

            // 道具卡背景（圆角）
            if (isAvailable) {
                // 可用状态：正常颜色
                this.ctx.globalAlpha = 1;
                this.ctx.fillStyle = prop.color;
            } else {
                // 不可用状态：灰色
                this.ctx.globalAlpha = 0.4;
                this.ctx.fillStyle = '#999999';
            }

            this.ctx.beginPath();
            this.ctx.roundRect(x, y, cardSize, cardSize, buttonRadius);
            this.ctx.fill();

            // 道具卡边框（圆角）
            this.ctx.strokeStyle = isAvailable ? '#FFFFFF' : '#666666';
            this.ctx.lineWidth = 2;
            this.ctx.beginPath();
            this.ctx.roundRect(x, y, cardSize, cardSize, buttonRadius);
            this.ctx.stroke();

            this.ctx.restore();

            // 道具图标（使用图片，完全覆盖卡片）
            const propImage = this.core.propImages[prop.type];
            if (propImage && propImage.complete) {
                this.ctx.save();
                // 不可用时图标变灰
                if (!isAvailable) {
                    this.ctx.globalAlpha = 0.4;
                    this.ctx.filter = 'grayscale(100%)';
                }

                // 图片完全覆盖卡片，留出边框空间
                const imageSize = cardSize - 4; // 减去边框宽度
                this.ctx.drawImage(
                    propImage,
                    x + 2,
                    y + 2,
                    imageSize,
                    imageSize
                );
                this.ctx.restore();
            } else {
                // 图片未加载时的备用方案
                this.ctx.fillStyle = isAvailable ? '#FFFFFF' : '#CCCCCC';
                this.ctx.font = `bold ${nameFontSize}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText(prop.name, x + cardSize/2, y + cardSize/2);
            }

            // 道具数量（显示在右上角，字体更小）
            this.ctx.font = `bold ${countFontSize}px Arial, "Microsoft YaHei"`;
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            // 数量背景圆圈，根据卡片大小调整
            const circleRadius = Math.max(8, cardSize * 0.15);
            const circleX = x + cardSize - circleRadius - 2;
            const circleY = y + circleRadius + 2;

            this.ctx.beginPath();
            this.ctx.arc(circleX, circleY, circleRadius, 0, Math.PI * 2);
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.fill();

            // 根据可用性设置边框和文字颜色
            if (isAvailable) {
                this.ctx.strokeStyle = '#FF0000';
                this.ctx.fillStyle = '#FF0000';
            } else {
                this.ctx.strokeStyle = '#999999';
                this.ctx.fillStyle = '#999999';
            }

            this.ctx.lineWidth = 1;
            this.ctx.stroke();

            // 数量文字
            this.ctx.fillText(prop.count.toString(), circleX, circleY);

            // 激活状态指示
            if (this.core.propUsing.isActive && this.core.propUsing.type === prop.type) {
                this.ctx.strokeStyle = '#FFD700';
                this.ctx.lineWidth = 4;
                this.ctx.strokeRect(x - 2, y - 2, cardSize + 4, cardSize + 4);

                // 添加闪烁效果
                this.ctx.save();
                this.ctx.globalAlpha = 0.5 + 0.5 * Math.sin(this.animationTime * 5);
                this.ctx.fillStyle = '#FFD700';
                this.ctx.fillRect(x, y, cardSize, cardSize);
                this.ctx.restore();
            }

            // 道具名称（在卡片下方，根据卡片大小调整字体）
            const textY = y + cardSize + textMarginTop;
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = `bold ${nameFontSize}px Arial, "Microsoft YaHei"`;
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'top';

            // 添加文字阴影效果
            this.ctx.save();
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.8)';
            this.ctx.shadowBlur = 2;
            this.ctx.shadowOffsetX = 1;
            this.ctx.shadowOffsetY = 1;
            this.ctx.fillText(prop.shortName, x + cardSize/2, textY);
            this.ctx.restore();
        });

        // 道具使用提示
        if (this.core.propUsing.isActive) {
            this.ctx.fillStyle = '#FF69B4';
            this.ctx.font = 'bold 18px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            let tipText = '';
            if (this.core.propUsing.type === 'bomb') {
                tipText = '请点击要爆炸的位置';
            }

            if (tipText) {
                this.ctx.strokeStyle = '#FFFFFF';
                this.ctx.lineWidth = 2;
                this.ctx.strokeText(tipText, this.canvas.width / 2, propBarY + propSize + 30);
                this.ctx.fillText(tipText, this.canvas.width / 2, propBarY + propSize + 30);
            }
        }
    }
    
    // 渲染按钮
    renderButton(x, y, width, height, text, color) {
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 8;
        this.ctx.shadowOffsetX = 3;
        this.ctx.shadowOffsetY = 3;
        
        // 按钮背景渐变
        const buttonGradient = this.ctx.createLinearGradient(x, y, x, y + height);
        buttonGradient.addColorStop(0, color);
        buttonGradient.addColorStop(0.5, this.lightenColor(color, 20));
        buttonGradient.addColorStop(1, this.darkenColor(color, 10));
        
        this.ctx.fillStyle = buttonGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(x, y, width, height, 15);
        this.ctx.fill();
        
        // 按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        
        // 按钮文字
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(text, x + width / 2, y + height / 2);
        
        this.ctx.restore();
    }
    
    // 渲染返回按钮（半透明白色背景，黑色字体）
    renderBackButton(x, y, width, height, text) {
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 半透明白色背景（透明度0.6）
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        this.ctx.beginPath();
        this.ctx.roundRect(x, y, width, height, 15);
        this.ctx.fill();
        
        // 按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();
        
        // 黑色文字（确保完全居中）
        this.ctx.fillStyle = '#333333';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 精确计算文本位置以确保完全居中
        const textX = Math.round(x + width / 2);
        const textY = Math.round(y + height / 2);
        this.ctx.fillText(text, textX, textY);
        
        this.ctx.restore();
    }
    

    
    // 渲染游戏网格
    renderGrid() {
        if (!this.core.grid) return;
        
        const startX = this.core.gridStartX;
        const startY = this.core.gridStartY;
        
        // 网格背景
        this.renderGridBackground(startX, startY);
        
        // 渲染网格中的方块（考虑间距）
        const spacing = typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.GRID ? GAME_CONFIG.GRID.SPACING : 2;
        // 安全检查网格是否已初始化
        if (!this.core.grid || !Array.isArray(this.core.grid)) {
            console.warn('网格未初始化，跳过渲染');
            return;
        }

        for (let row = 0; row < this.core.gridSizeY; row++) {
            // 安全检查行是否存在
            if (!this.core.grid[row] || !Array.isArray(this.core.grid[row])) {
                continue;
            }

            for (let col = 0; col < this.core.gridSizeX; col++) {
                const block = this.core.grid[row][col];
                if (block && !block.isFalling) {
                    let x = startX + col * (this.core.blockSize + spacing);
                    let y = startY + row * (this.core.blockSize + spacing);

                    // 处理刷新卡闪烁效果
                    if (block.isFlashing) {
                        const elapsed = Date.now() - block.flashStartTime;
                        if (elapsed < block.flashDuration) {
                            // 闪烁效果：快速变化透明度
                            const flashCycle = Math.sin(elapsed * 0.02) * 0.5 + 0.5; // 0-1之间变化
                            block.alpha = 0.3 + flashCycle * 0.7; // 0.3-1之间变化
                            block.scale = 0.9 + flashCycle * 0.1; // 0.9-1之间变化
                        } else {
                            // 闪烁结束
                            block.isFlashing = false;
                            block.alpha = 1;
                            block.scale = 1;
                        }
                    }

                    // 处理出现动画
                    if (block.isAppearing) {
                        const elapsed = Date.now() - block.appearStartTime;
                        if (elapsed > 0) {
                            const progress = Math.min(elapsed / 300, 1); // 300ms出现动画
                            block.scale = 0.5 + progress * 0.5; // 从0.5放大到1
                            block.alpha = 0.7 + progress * 0.3; // 从0.7变到1

                            if (progress >= 1) {
                                // 出现动画完成
                                block.isAppearing = false;
                                block.scale = 1;
                                block.alpha = 1;
                            }
                        } else {
                            // 还没开始出现
                            continue;
                        }
                    }

                    this.renderBlock(block, x, y);
                }
            }
        }
        
        // 渲染掉落中的方块
        if (this.core.animator && this.core.animator.falling) {
            this.core.animator.render(this);
        }

        // 渲染拖拽指示效果
        this.renderDragIndicator();
    }

    // 渲染拖拽指示效果
    renderDragIndicator() {
        if (!this.core.selectedBlock) return;

        const selectedRow = this.core.selectedBlock.row;
        const selectedCol = this.core.selectedBlock.col;
        const selectedBlock = this.core.grid[selectedRow] && this.core.grid[selectedRow][selectedCol];

        if (!selectedBlock || !selectedBlock.isSelected) return;

        const spacing = this.core.getSpacing();
        const blockSize = this.core.blockSize;

        // 计算选中方块的中心位置
        const selectedCenterX = this.core.gridStartX + selectedCol * (blockSize + spacing) + blockSize / 2;
        const selectedCenterY = this.core.gridStartY + selectedRow * (blockSize + spacing) + blockSize / 2;

        // 渲染可交换方向的指示箭头
        this.ctx.save();

        const directions = [
            { row: -1, col: 0, angle: -Math.PI / 2 }, // 上
            { row: 1, col: 0, angle: Math.PI / 2 },   // 下
            { row: 0, col: -1, angle: Math.PI },      // 左
            { row: 0, col: 1, angle: 0 }              // 右
        ];

        directions.forEach(dir => {
            const targetRow = selectedRow + dir.row;
            const targetCol = selectedCol + dir.col;

            // 检查目标位置是否有效
            if (targetRow >= 0 && targetRow < this.core.gridSizeY &&
                targetCol >= 0 && targetCol < this.core.gridSizeX &&
                this.core.grid[targetRow] && this.core.grid[targetRow][targetCol]) {

                // 计算箭头位置
                const arrowDistance = blockSize * 0.7;
                const arrowX = selectedCenterX + Math.cos(dir.angle) * arrowDistance;
                const arrowY = selectedCenterY + Math.sin(dir.angle) * arrowDistance;

                // 绘制脉冲箭头
                const pulseIntensity = 0.6 + 0.4 * Math.sin(Date.now() * 0.01);
                this.ctx.globalAlpha = pulseIntensity * 0.8;

                this.ctx.fillStyle = '#FFD700';
                this.ctx.strokeStyle = '#FFA500';
                this.ctx.lineWidth = 2;

                // 绘制箭头
                this.ctx.save();
                this.ctx.translate(arrowX, arrowY);
                this.ctx.rotate(dir.angle);

                this.ctx.beginPath();
                this.ctx.moveTo(8, 0);
                this.ctx.lineTo(-4, -6);
                this.ctx.lineTo(-4, 6);
                this.ctx.closePath();
                this.ctx.fill();
                this.ctx.stroke();

                this.ctx.restore();
            }
        });

        this.ctx.restore();
    }
    
    // 渲染网格背景（使用配置系统和逻辑尺寸）
    renderGridBackground(startX, startY) {
        // 使用配置系统计算网格背景尺寸（基于逻辑尺寸）
        let gridBackgroundWidth, gridBackgroundHeight, backgroundX, backgroundY;

        // 尝试获取 CONFIG_UTILS
        let configUtils = null;
        try {
            if (typeof CONFIG_UTILS !== 'undefined') {
                configUtils = CONFIG_UTILS;
            } else if (typeof window !== 'undefined' && window.CONFIG_UTILS) {
                configUtils = window.CONFIG_UTILS;
            } else if (typeof globalThis !== 'undefined' && globalThis.CONFIG_UTILS) {
                configUtils = globalThis.CONFIG_UTILS;
            } else if (typeof global !== 'undefined' && global.CONFIG_UTILS) {
                configUtils = global.CONFIG_UTILS;
            }
        } catch (error) {
            console.warn('获取 CONFIG_UTILS 时出错:', error);
        }

        if (configUtils) {
            // 使用逻辑尺寸进行布局计算
            const layoutCalc = configUtils.calculateLayout(this.width, this.height);
            gridBackgroundWidth = layoutCalc.gridBackgroundWidth;
            gridBackgroundHeight = layoutCalc.gridBackgroundHeight;
            backgroundX = layoutCalc.gridArea.x;
            backgroundY = layoutCalc.gridArea.y;

            console.log(`🎨 网格背景渲染（CONFIG_UTILS）:`);
            console.log(`   逻辑尺寸: ${this.width} x ${this.height}`);
            console.log(`   背景位置: (${backgroundX.toFixed(1)}, ${backgroundY.toFixed(1)})`);
            console.log(`   背景尺寸: ${gridBackgroundWidth.toFixed(1)} x ${gridBackgroundHeight.toFixed(1)}`);
        } else {
            // 回退到响应式计算（基于逻辑尺寸）
            console.log(`🎨 网格背景渲染（回退计算）:`);
            console.log(`   逻辑尺寸: ${this.width} x ${this.height}`);

            const config = this.core.constructor.getConfig();
            const layout = config.LAYOUT;
            const gridConfig = config.GRID;

            // 计算网格区域
            const gridAreaWidth = this.width * (layout.GRID_AREA.WIDTH_PERCENT || 0.85);
            const gridAreaHeight = this.core.gridSizeY * this.core.blockSize + (this.core.gridSizeY - 1) * gridConfig.SPACING + gridConfig.PADDING * 2;

            backgroundX = (this.width - gridAreaWidth) / 2;
            backgroundY = startY - gridConfig.PADDING;
            gridBackgroundWidth = gridAreaWidth;
            gridBackgroundHeight = gridAreaHeight;

            console.log(`   网格区域宽度: ${gridAreaWidth.toFixed(1)}px (${(layout.GRID_AREA.WIDTH_PERCENT || 0.85) * 100}%)`);
            console.log(`   背景位置: (${backgroundX.toFixed(1)}, ${backgroundY.toFixed(1)})`);
            console.log(`   背景尺寸: ${gridBackgroundWidth.toFixed(1)} x ${gridBackgroundHeight.toFixed(1)}`);
        }

        // 验证网格背景边界
        this.validateGridBackgroundBounds(backgroundX, backgroundY, gridBackgroundWidth, gridBackgroundHeight);

        // 半透明白色背景
        this.ctx.fillStyle = this.colors.grid.background;
        this.ctx.shadowColor = this.colors.grid.shadow;
        this.ctx.shadowBlur = 10;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 5;

        this.ctx.beginPath();
        this.ctx.roundRect(backgroundX, backgroundY, gridBackgroundWidth, gridBackgroundHeight, 15);
        this.ctx.fill();

        this.ctx.shadowColor = 'transparent';
        
        // 网格线（可选）
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.lineWidth = 1;
        
        // 获取间距配置
        const spacing = typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.GRID ? GAME_CONFIG.GRID.SPACING : 2;

        // 垂直线（在间距中央）
        for (let col = 1; col < this.core.gridSizeX; col++) {
            const x = startX + col * (this.core.blockSize + spacing) - spacing/2;
            this.ctx.beginPath();
            this.ctx.moveTo(x, startY);
            this.ctx.lineTo(x, startY + (this.core.gridSizeY * this.core.blockSize + (this.core.gridSizeY - 1) * spacing));
            this.ctx.stroke();
        }

        // 水平线（在间距中央）
        for (let row = 1; row < this.core.gridSizeY; row++) {
            const y = startY + row * (this.core.blockSize + spacing) - spacing/2;
            this.ctx.beginPath();
            this.ctx.moveTo(startX, y);
            this.ctx.lineTo(startX + (this.core.gridSizeX * this.core.blockSize + (this.core.gridSizeX - 1) * spacing), y);
            this.ctx.stroke();
        }
    }

    // 验证网格背景边界
    validateGridBackgroundBounds(backgroundX, backgroundY, backgroundWidth, backgroundHeight) {
        const isValid = backgroundX >= 0 && backgroundY >= 0 &&
                       (backgroundX + backgroundWidth) <= this.width &&
                       (backgroundY + backgroundHeight) <= this.height;

        if (!isValid) {
            console.warn(`⚠️ 网格背景超出屏幕边界!`);
            console.warn(`   逻辑屏幕: ${this.width} x ${this.height}`);
            console.warn(`   背景位置: (${backgroundX.toFixed(1)}, ${backgroundY.toFixed(1)})`);
            console.warn(`   背景尺寸: ${backgroundWidth.toFixed(1)} x ${backgroundHeight.toFixed(1)}`);
            console.warn(`   背景结束: (${(backgroundX + backgroundWidth).toFixed(1)}, ${(backgroundY + backgroundHeight).toFixed(1)})`);

            if (backgroundX < 0) console.warn(`   左边界超出: ${backgroundX.toFixed(1)}px`);
            if (backgroundY < 0) console.warn(`   上边界超出: ${backgroundY.toFixed(1)}px`);
            if (backgroundX + backgroundWidth > this.width) {
                console.warn(`   右边界超出: ${(backgroundX + backgroundWidth - this.width).toFixed(1)}px`);
            }
            if (backgroundY + backgroundHeight > this.height) {
                console.warn(`   下边界超出: ${(backgroundY + backgroundHeight - this.height).toFixed(1)}px`);
            }
        } else {
            console.log(`✅ 网格背景在屏幕范围内`);
        }

        return isValid;
    }

    // 渲染方块（高性能版本）
    renderBlock(block, x, y) {
        this.ctx.save();

        // 应用透明度和缩放
        this.ctx.globalAlpha = block.alpha || 1;

        let scaleOffset = block.scale || 1;
        let rotationOffset = block.rotation || 0;

        // 在低性能模式下跳过复杂阴影效果
        if (!this.core.lowPerformanceMode) {
            // 拖拽状态的简化阴影效果
            if (block.isDragging || block.isSelected) {
                this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'; // 减少阴影强度
                this.ctx.shadowOffsetX = 2; // 减少偏移
                this.ctx.shadowOffsetY = 3;
                this.ctx.shadowBlur = 6; // 减少模糊半径
            } else if (block.isTargetHighlighted) {
                this.ctx.shadowColor = 'rgba(0, 255, 127, 0.2)'; // 减少强度
                this.ctx.shadowOffsetX = 1;
                this.ctx.shadowOffsetY = 2;
                this.ctx.shadowBlur = 4; // 减少模糊
            }
        }

        // 漩涡动画效果
        if (block.isVortexing) {
            const elapsed = Date.now() - block.vortexStartTime;
            const totalProgress = Math.min(elapsed / block.vortexDuration, 1);

            if (totalProgress >= 1) {
                // 动画结束，恢复原位置
                block.isVortexing = false;
                x = block.originalX;
                y = block.originalY;
            } else {
                // 计算当前动画阶段
                let adjustedProgress = Math.max(0, (elapsed - block.vortexDelay) / (block.vortexDuration - block.vortexDelay));
                adjustedProgress = Math.min(adjustedProgress, 1);

                if (adjustedProgress > 0) {
                    if (adjustedProgress < 0.5) {
                        // 前半段：吸入漩涡中心
                        const inProgress = adjustedProgress * 2;
                        const spiralTurns = inProgress * 3; // 3圈螺旋
                        const currentRadius = block.vortexDistance * (1 - inProgress);
                        const currentAngle = block.vortexAngle + spiralTurns * Math.PI * 2;

                        x = block.vortexCenterX + Math.cos(currentAngle) * currentRadius - this.core.blockSize / 2;
                        y = block.vortexCenterY + Math.sin(currentAngle) * currentRadius - this.core.blockSize / 2;

                        // 缩放效果
                        scaleOffset *= (1 - inProgress * 0.8); // 缩小到0.2倍
                        rotationOffset += spiralTurns * Math.PI * 2;
                    } else {
                        // 后半段：从漩涡中心出来
                        const outProgress = (adjustedProgress - 0.5) * 2;
                        const spiralTurns = (1 - outProgress) * 2; // 2圈螺旋出来
                        const currentRadius = block.vortexDistance * outProgress;
                        const currentAngle = block.vortexAngle + spiralTurns * Math.PI * 2;

                        x = block.vortexCenterX + Math.cos(currentAngle) * currentRadius - this.core.blockSize / 2;
                        y = block.vortexCenterY + Math.sin(currentAngle) * currentRadius - this.core.blockSize / 2;

                        // 缩放效果
                        scaleOffset *= (0.2 + outProgress * 0.8); // 从0.2倍恢复到1倍
                        rotationOffset += spiralTurns * Math.PI * 2;
                    }
                }
            }
        }

        if (block.isSelected) {
            scaleOffset = 1.1 + Math.sin(this.animationTime * 10) * 0.05; // 更明显的缩放和脉动
        }
        
        this.ctx.translate(x + this.core.blockSize / 2, y + this.core.blockSize / 2);
        this.ctx.scale(scaleOffset, scaleOffset);
        this.ctx.rotate(rotationOffset);
        
        // 方块阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 方块背景渐变 - 支持特殊方块
        let blockColor;
        if (block.blockType === 'special') {
            blockColor = this.colors.special[block.type] || this.colors.special['rocket'];
        } else {
            blockColor = this.colors.animals[block.type] || this.colors.animals['cat'];
        }

        const blockGradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, this.core.blockSize / 2);
        blockGradient.addColorStop(0, blockColor.glow);
        blockGradient.addColorStop(0.7, blockColor.bg);
        blockGradient.addColorStop(1, this.darkenColor(blockColor.bg, 15));
        
        this.ctx.fillStyle = blockGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(-this.core.blockSize / 2, -this.core.blockSize / 2, 
                         this.core.blockSize, this.core.blockSize, 8);
        this.ctx.fill();
        
        // 方块边框和特效
        this.ctx.shadowColor = 'transparent';

        if (block.isSelected) {
            // 选中状态：增强的金色发光边框 + 脉冲效果
            const pulseIntensity = 0.7 + 0.3 * Math.sin(Date.now() * 0.008); // 脉冲效果
            const glowSize = 12 * pulseIntensity;

            // 外层发光
            this.ctx.strokeStyle = '#FFD700';
            this.ctx.lineWidth = 5;
            this.ctx.shadowColor = '#FFD700';
            this.ctx.shadowBlur = glowSize;
            this.ctx.globalAlpha = pulseIntensity;
            this.ctx.stroke();

            // 中层边框
            this.ctx.shadowColor = 'transparent';
            this.ctx.globalAlpha = 1;
            this.ctx.strokeStyle = '#FFA500';
            this.ctx.lineWidth = 3;
            this.ctx.stroke();

            // 内层白色边框
            this.ctx.strokeStyle = '#FFFFFF';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();

            // 添加选中方块的背景高亮
            this.ctx.fillStyle = 'rgba(255, 215, 0, 0.15)';
            this.ctx.fill();

        } else if (block.isTargetHighlighted) {
            // 目标高亮状态：增强的绿色发光边框 + 呼吸效果
            const breatheIntensity = 0.6 + 0.4 * Math.sin(Date.now() * 0.01); // 呼吸效果
            const glowSize = 10 * breatheIntensity;

            // 外层发光
            this.ctx.strokeStyle = '#00FF7F';
            this.ctx.lineWidth = 4;
            this.ctx.shadowColor = '#00FF7F';
            this.ctx.shadowBlur = glowSize;
            this.ctx.globalAlpha = breatheIntensity;
            this.ctx.stroke();

            // 中层边框
            this.ctx.shadowColor = 'transparent';
            this.ctx.globalAlpha = 1;
            this.ctx.strokeStyle = '#32CD32';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();

            // 内层白色边框
            this.ctx.strokeStyle = '#FFFFFF';
            this.ctx.lineWidth = 1;
            this.ctx.stroke();

            // 添加目标方块的背景高亮
            this.ctx.fillStyle = 'rgba(0, 255, 127, 0.12)';
            this.ctx.fill();

        } else {
            // 普通状态：柔和边框
            this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
        }
        
        // 内部高光
        const highlightGradient = this.ctx.createLinearGradient(
            -this.core.blockSize / 2, -this.core.blockSize / 2,
            this.core.blockSize / 4, this.core.blockSize / 4
        );
        highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
        highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        
        this.ctx.fillStyle = highlightGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(-this.core.blockSize / 2, -this.core.blockSize / 2, 
                         this.core.blockSize, this.core.blockSize, 8);
        this.ctx.fill();
        
        // 方块内容渲染
        this.ctx.shadowColor = 'transparent';

        if (block.blockType === 'special') {
            // 渲染特殊方块图标
            this.renderSpecialBlockIcon(block);
        } else {
            // 渲染普通萌宠
            const animalImage = this.core.animalImages[block.type];
            if (animalImage && animalImage.complete) {
                // 绘制萌宠图片
                const imageSize = this.core.blockSize * 0.7;
                this.ctx.drawImage(
                    animalImage,
                    -imageSize / 2,
                    -imageSize / 2,
                    imageSize,
                    imageSize
                );
            } else {
                // 图片未加载时的备用方案 - 显示文字
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${this.core.blockSize * 0.3}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';

                // 显示萌宠名称的中文
                const animalNames = {
                    'cat': '猫',
                    'dog': '狗',
                    'elephant': '象',
                    'fox': '狐',
                    'frog': '蛙',
                    'monkey': '猴',
                    'panda': '熊',
                    'rabbit': '兔',
                    'tiger': '虎'
                };

                this.ctx.fillText(animalNames[block.type] || block.type, 0, 0);
            }
        }
        
        this.ctx.restore();
    }

    // 渲染特殊方块图标
    renderSpecialBlockIcon(block) {
        if (block.type === 'rocket_horizontal' || block.type === 'rocket_vertical') {
            // 尝试使用火箭图片（优先使用具体类型的图片）
            const rocketImage = this.core.propImages[block.type] || this.core.propImages['rocket'];
            if (rocketImage && rocketImage.complete) {
                const imageSize = this.core.blockSize * 0.7;

                // 所有火箭图片方向保持一致，不进行旋转
                this.ctx.drawImage(
                    rocketImage,
                    -imageSize / 2,
                    -imageSize / 2,
                    imageSize,
                    imageSize
                );
            } else {
                // 图片未加载时的备用方案：所有火箭都显示向上箭头
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${this.core.blockSize * 0.3}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText('↑', 0, 0); // 统一显示向上箭头
            }

            // 添加闪烁效果
            this.ctx.save();
            this.ctx.globalAlpha = 0.5 + 0.5 * Math.sin(this.animationTime * 6);
            this.ctx.fillStyle = '#FFD700';
            this.ctx.beginPath();
            this.ctx.arc(0, 0, this.core.blockSize * 0.4, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();

        } else if (block.type === 'bomb_extra') {
            // 尝试使用炸弹图片
            const bombImage = this.core.propImages['bomb_extra'];
            if (bombImage && bombImage.complete) {
                const imageSize = this.core.blockSize * 0.7;
                this.ctx.drawImage(
                    bombImage,
                    -imageSize / 2,
                    -imageSize / 2,
                    imageSize,
                    imageSize
                );
            } else {
                // 图片未加载时的备用方案
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${this.core.blockSize * 0.3}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText('炸弹', 0, 0);
            }

            // 添加危险闪烁效果
            this.ctx.save();
            this.ctx.globalAlpha = 0.3 + 0.3 * Math.sin(this.animationTime * 8);
            this.ctx.fillStyle = '#FF0000';
            this.ctx.beginPath();
            this.ctx.arc(0, 0, this.core.blockSize * 0.45, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        }
    }
    
    // 渲染特效
    renderEffects() {
        // 渲染粒子效果
        this.renderParticles();
        
        // 渲染闪烁效果
        this.renderSparkles();
        
        // 渲染浮动文字
        this.renderFloatingTexts();
    }
    
    // 渲染粒子效果（平衡版本）
    renderParticles() {
        if (this.core.particles.length === 0) return;

        // 超快速模式：完全跳过粒子渲染（仅在FPS<20时）
        if (this.core.ultraFastMode) {
            return;
        }

        this.ctx.save();

        // 快速消除模式：使用简化但有效果的渲染
        if (this.core.fastEliminationMode) {
            // 按颜色分组渲染，保持视觉效果
            const particlesByColor = {};
            this.core.particles.forEach(particle => {
                if (particle.life > 0.2) {
                    if (!particlesByColor[particle.color]) {
                        particlesByColor[particle.color] = [];
                    }
                    particlesByColor[particle.color].push(particle);
                }
            });

            Object.entries(particlesByColor).forEach(([color, particles]) => {
                this.ctx.fillStyle = color;
                this.ctx.beginPath();

                particles.forEach(particle => {
                    this.ctx.globalAlpha = particle.life;
                    this.ctx.moveTo(particle.x + particle.size, particle.y);
                    this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                });

                this.ctx.fill();
            });
        } else {
            // 标准模式：恢复完整的粒子渲染
            const particlesByColor = {};
            this.core.particles.forEach(particle => {
                if (particle.life > 0.1) {
                    if (!particlesByColor[particle.color]) {
                        particlesByColor[particle.color] = [];
                    }
                    particlesByColor[particle.color].push(particle);
                }
            });

            Object.entries(particlesByColor).forEach(([color, particles]) => {
                this.ctx.fillStyle = color;
                this.ctx.beginPath();

                particles.forEach(particle => {
                    this.ctx.globalAlpha = particle.life;
                    this.ctx.moveTo(particle.x + particle.size, particle.y);
                    this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                });

                this.ctx.fill();
            });
        }

        this.ctx.restore();
    }
    
    // 渲染闪烁效果（平衡版本）
    renderSparkles() {
        if (this.core.sparkles.length === 0) return;

        // 超快速模式：完全跳过闪烁渲染（仅在FPS<20时）
        if (this.core.ultraFastMode) {
            return;
        }

        this.ctx.save();
        this.ctx.fillStyle = '#FFD700';

        this.core.sparkles.forEach(sparkle => {
            if (sparkle.life > 0.1) {
                this.ctx.save();

                // 根据模式选择渲染复杂度
                if (this.core.fastEliminationMode) {
                    // 快速模式：简化但保留效果
                    this.ctx.globalAlpha = sparkle.life * 0.9;
                    this.ctx.translate(sparkle.x, sparkle.y);

                    // 简化的十字形
                    const size = sparkle.size * 0.8;
                    this.ctx.fillRect(-size * 0.5, -size * 0.1, size, size * 0.2);
                    this.ctx.fillRect(-size * 0.1, -size * 0.5, size * 0.2, size);
                } else {
                    // 标准模式：恢复完整的闪烁效果
                    const alpha = sparkle.life * (0.7 + 0.3 * Math.sin(sparkle.twinkle * 0.3));
                    this.ctx.globalAlpha = alpha;

                    this.ctx.translate(sparkle.x, sparkle.y);
                    this.ctx.rotate(sparkle.twinkle * 0.3);

                    // 完整的星形
                    const size = sparkle.size;
                    this.ctx.fillRect(-size * 0.5, -size * 0.1, size, size * 0.2);
                    this.ctx.fillRect(-size * 0.1, -size * 0.5, size * 0.2, size);
                }

                this.ctx.restore();
            }
        });

        this.ctx.restore();
    }
    
    // 渲染浮动文字（优化分数文字响应速度）
    renderFloatingTexts() {
        // 分离分数文字和其他文字，优先渲染分数文字
        const scoreTexts = this.core.floatingTexts.filter(text => text.isScoreText);
        const otherTexts = this.core.floatingTexts.filter(text => !text.isScoreText);

        // 先渲染分数文字（更高优先级）
        scoreTexts.forEach(text => {
            this.ctx.save();

            if (text.isCombo) {
                this.renderComboText(text);
            } else {
                // 分数文字使用简化渲染，提升性能
                this.ctx.globalAlpha = text.life;
                this.ctx.fillStyle = text.color;
                this.ctx.font = `bold ${32 * text.scale}px Arial`; // 更大更醒目
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';

                // 简化描边效果
                this.ctx.strokeStyle = '#000000';
                this.ctx.lineWidth = 2;
                this.ctx.strokeText(text.text, text.x, text.y);
                this.ctx.fillText(text.text, text.x, text.y);
            }

            this.ctx.restore();
        });

        // 再渲染其他文字
        otherTexts.forEach(text => {
            this.ctx.save();

            if (text.isCombo) {
                this.renderComboText(text);
            } else {
                // 普通浮动文字
                this.ctx.globalAlpha = text.life;
                this.ctx.fillStyle = text.color;
                this.ctx.font = `bold ${28 * text.scale}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';

                // 标准描边效果
                this.ctx.strokeStyle = '#FFFFFF';
                this.ctx.lineWidth = 3;
                this.ctx.strokeText(text.text, text.x, text.y);
                this.ctx.fillText(text.text, text.x, text.y);
            }

            this.ctx.restore();
        });
    }

    // 渲染连击文字特效（极简高性能版）
    renderComboText(text) {
        // 透明度
        this.ctx.globalAlpha = text.life;

        // 根据是否为简单模式选择渲染方式
        if (text.isSimple) {
            // 极简模式：无特效，纯文字
            this.ctx.fillStyle = text.color;
            this.ctx.font = `bold ${24 * text.scale}px Arial`; // 更小的字体
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            // 只有简单描边
            this.ctx.strokeStyle = '#000000';
            this.ctx.lineWidth = 2;
            this.ctx.strokeText(text.text, text.x, text.y);
            this.ctx.fillText(text.text, text.x, text.y);
        } else {
            // 标准模式：简化的脉动效果
            if (!text.pulsePhase) text.pulsePhase = 0;
            text.pulsePhase += 0.08; // 进一步减少更新频率
            const pulseScale = 1 + Math.sin(text.pulsePhase) * 0.05; // 大幅减少脉动幅度
            const finalScale = text.scale * pulseScale;

            // 极简阴影效果
            this.ctx.shadowColor = text.color;
            this.ctx.shadowBlur = 4; // 进一步减少模糊
            this.ctx.shadowOffsetX = 0;
            this.ctx.shadowOffsetY = 1;

            // 文字样式
            this.ctx.fillStyle = text.color;
            this.ctx.font = `bold ${26 * finalScale}px Arial`; // 减少字体大小
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            // 简化描边
            this.ctx.strokeStyle = '#000000';
            this.ctx.lineWidth = 2; // 进一步减少描边宽度
            this.ctx.strokeText(text.text, text.x, text.y);
            this.ctx.fillText(text.text, text.x, text.y);

            // 清除阴影
            this.ctx.shadowColor = 'transparent';
            this.ctx.shadowBlur = 0;
        }
    }
    
    // 渲染游戏状态
    renderGameStatus() {
        if (this.core.showExitDialog) {
            this.renderExitDialog();
        } else if (this.core.isLevelComplete) {
            this.renderVictoryScreen();
        } else if (this.core.isGameOver) {
            this.renderGameOverScreen();
        }
    }
    
    // 渲染退出确认弹框
    renderExitDialog() {
        // 半透明遮罩（增加透明度）
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;

        // 弹框背景
        const dialogWidth = 300;
        const dialogHeight = 180;
        const dialogX = centerX - dialogWidth / 2;
        const dialogY = centerY - dialogHeight / 2;

        // 弹框阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.4)';
        this.ctx.shadowBlur = 20;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 8;

        // 弹框背景渐变（添加底透明度）
        const dialogGradient = this.ctx.createLinearGradient(dialogX, dialogY, dialogX, dialogY + dialogHeight);
        dialogGradient.addColorStop(0, 'rgba(255, 255, 255, 0.95)');
        dialogGradient.addColorStop(1, 'rgba(248, 249, 250, 0.9)');

        this.ctx.fillStyle = dialogGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(dialogX, dialogY, dialogWidth, dialogHeight, 20);
        this.ctx.fill();

        // 弹框边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = 'rgba(255, 105, 180, 0.8)';
        this.ctx.lineWidth = 3;
        this.ctx.stroke();
        
        // 标题
        this.ctx.fillStyle = '#FF1493';
        this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('确认退出', centerX, centerY - 40);
        
        // 提示文字
        this.ctx.fillStyle = '#666666';
        this.ctx.font = '18px Arial, "Microsoft YaHei"';
        this.ctx.fillText('确定要退出当前游戏吗？', centerX, centerY - 10);
        this.ctx.fillText('游戏进度将会丢失', centerX, centerY + 10);
        
        // 继续游戏按钮
        this.renderButton(centerX - 120, centerY + 20, 100, 40, '继续游戏', '#4CAF50');
        
        // 返回主页按钮
        this.renderButton(centerX + 20, centerY + 20, 100, 40, '返回主页', '#FF6B6B');
    }
    
    // 渲染通关弹框
    renderVictoryScreen() {
        // 半透明遮罩
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;

        // 弹框尺寸
        const dialogWidth = 350;
        const dialogHeight = 250;
        const dialogX = centerX - dialogWidth / 2;
        const dialogY = centerY - dialogHeight / 2;

        // 弹框阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 15;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 5;

        // 弹框背景渐变
        const dialogGradient = this.ctx.createLinearGradient(dialogX, dialogY, dialogX, dialogY + dialogHeight);
        dialogGradient.addColorStop(0, '#FFFFFF');
        dialogGradient.addColorStop(1, '#F8F9FA');

        this.ctx.fillStyle = dialogGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(dialogX, dialogY, dialogWidth, dialogHeight, 15);
        this.ctx.fill();

        // 弹框边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = '#4CAF50';
        this.ctx.lineWidth = 3;
        this.ctx.stroke();

        // 标题
        this.ctx.fillStyle = '#4CAF50';
        this.ctx.font = 'bold 28px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('通关成功！', centerX, centerY - 60);

        // 分数统计
        this.ctx.fillStyle = '#333333';
        this.ctx.font = '20px Arial, "Microsoft YaHei"';
        this.ctx.fillText(`最终分数: ${this.core.score}`, centerX, centerY - 20);

        // 倒计时显示
        const countdown = this.core.levelCompleteCountdown || 0;
        this.ctx.fillStyle = '#666666';
        this.ctx.font = '16px Arial, "Microsoft YaHei"';
        this.ctx.fillText(`${countdown}秒后自动进入下一关`, centerX, centerY + 20);

        // 确定按钮
        const buttonWidth = 120;
        const buttonHeight = 40;
        const buttonX = centerX - buttonWidth / 2;
        const buttonY = centerY + 50;

        // 按钮背景
        this.ctx.fillStyle = '#4CAF50';
        this.ctx.beginPath();
        this.ctx.roundRect(buttonX, buttonY, buttonWidth, buttonHeight, 8);
        this.ctx.fill();

        // 按钮边框
        this.ctx.strokeStyle = '#45A049';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();

        // 按钮文字
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 18px Arial, "Microsoft YaHei"';
        this.ctx.fillText('确定', centerX, buttonY + buttonHeight / 2);

        // 存储按钮位置供事件处理使用
        this.levelCompleteButton = {
            x: buttonX,
            y: buttonY,
            width: buttonWidth,
            height: buttonHeight
        };
    }
    
    // 渲染游戏结束界面
    renderGameOverScreen() {
        // 半透明遮罩
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        // 游戏结束文字
        this.ctx.fillStyle = '#FF6B6B';
        this.ctx.font = 'bold 42px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('游戏结束', centerX, centerY - 50);
        
        // 分数统计
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = '20px Arial, "Microsoft YaHei"';
        this.ctx.fillText(`最终分数: ${this.core.score}`, centerX, centerY);
        
        // 提示文字
        this.ctx.font = '16px Arial, "Microsoft YaHei"';
        this.ctx.fillStyle = '#CCCCCC';
        this.ctx.fillText('点击屏幕重新开始', centerX, centerY + 50);
    }



    // 渲染炸弹卡拖拽 - 增强版
    renderBombDrag() {
        const events = this.core.events;
        if (!events || !events.isDraggingBomb) return;

        const x = events.bombDragCurrentX;
        const y = events.bombDragCurrentY;
        const size = 60; // 增大尺寸

        // 检查是否在网格内
        const gridPos = events.getGridPosition(x, y);
        const isInGrid = gridPos.row !== -1 && gridPos.col !== -1;

        // 绘制拖拽轨迹（更明显）
        this.ctx.save();
        this.ctx.strokeStyle = isInGrid ? 'rgba(76, 175, 80, 0.8)' : 'rgba(244, 67, 54, 0.8)';
        this.ctx.lineWidth = 4;
        this.ctx.setLineDash([8, 4]);
        this.ctx.beginPath();
        this.ctx.moveTo(events.bombDragStartX, events.bombDragStartY);
        this.ctx.lineTo(x, y);
        this.ctx.stroke();
        this.ctx.restore();

        // 如果在网格内，高亮目标位置
        if (isInGrid) {
            const spacing = this.core.getSpacing();
            const blockWithSpacing = this.core.blockSize + spacing;
            const targetX = this.core.gridStartX + gridPos.col * blockWithSpacing + this.core.blockSize / 2;
            const targetY = this.core.gridStartY + gridPos.row * blockWithSpacing + this.core.blockSize / 2;

            // 绘制目标高亮圈
            this.ctx.save();
            this.ctx.strokeStyle = '#4CAF50';
            this.ctx.lineWidth = 3;
            this.ctx.globalAlpha = 0.8 + 0.2 * Math.sin(this.animationTime * 8);
            this.ctx.beginPath();
            this.ctx.arc(targetX, targetY, this.core.blockSize / 2 + 5, 0, Math.PI * 2);
            this.ctx.stroke();
            this.ctx.restore();
        }

        // 炸弹图标（增强效果）
        this.ctx.save();
        this.ctx.globalAlpha = 0.9;

        // 发光效果
        this.ctx.shadowColor = isInGrid ? '#4CAF50' : '#F44336';
        this.ctx.shadowBlur = 15;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;

        // 炸弹背景（圆形）
        this.ctx.fillStyle = isInGrid ? '#4CAF50' : '#F44336';
        this.ctx.beginPath();
        this.ctx.arc(x, y, size/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 炸弹图片
        const bombImage = this.core.propImages['bomb_extra'] || this.core.propImages['bomb'];
        if (bombImage && bombImage.complete) {
            const imageSize = size * 0.7;
            this.ctx.drawImage(
                bombImage,
                x - imageSize/2,
                y - imageSize/2,
                imageSize,
                imageSize
            );
        } else {
            // 备用文字
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText('💣', x, y);
        }

        this.ctx.restore();
    }

    // 渲染漩涡效果
    renderVortexEffect() {
        const vortex = this.core.vortexEffect;
        if (!vortex || !vortex.active) return;

        const elapsed = Date.now() - vortex.startTime;
        const progress = Math.min(elapsed / vortex.duration, 1);

        if (progress >= 1) {
            // 动画结束
            this.core.vortexEffect.active = false;
            return;
        }

        this.ctx.save();

        // 绘制漩涡中心
        const centerX = vortex.centerX;
        const centerY = vortex.centerY;

        // 漩涡的多层螺旋线
        for (let layer = 0; layer < 5; layer++) {
            this.ctx.beginPath();
            this.ctx.strokeStyle = `rgba(138, 43, 226, ${0.8 - layer * 0.15})`;
            this.ctx.lineWidth = 3 - layer * 0.4;

            const spiralRadius = vortex.maxRadius * (0.8 - layer * 0.15);
            const spiralTurns = 4 + layer;

            for (let i = 0; i <= 100; i++) {
                const t = i / 100;
                const angle = t * spiralTurns * Math.PI * 2 + progress * Math.PI * 4;
                const radius = spiralRadius * (1 - t) * (0.5 + 0.5 * Math.sin(progress * Math.PI * 6));

                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;

                if (i === 0) {
                    this.ctx.moveTo(x, y);
                } else {
                    this.ctx.lineTo(x, y);
                }
            }

            this.ctx.stroke();
        }

        // 中心发光点
        const glowRadius = 20 + Math.sin(progress * Math.PI * 8) * 10;
        const gradient = this.ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, glowRadius);
        gradient.addColorStop(0, 'rgba(138, 43, 226, 0.8)');
        gradient.addColorStop(0.5, 'rgba(75, 0, 130, 0.4)');
        gradient.addColorStop(1, 'rgba(75, 0, 130, 0)');

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, glowRadius, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.restore();
    }

    // 渲染闪光效果
    renderFlashEffect() {
        const flash = this.core.flashEffect;
        if (!flash || !flash.active) return;

        const elapsed = Date.now() - flash.startTime;
        const progress = Math.min(elapsed / flash.duration, 1);

        if (progress >= 1) {
            // 动画结束
            this.core.flashEffect.active = false;
            return;
        }

        // 计算闪光强度（先增强后减弱）
        let alpha;
        if (progress < 0.3) {
            alpha = (progress / 0.3) * flash.intensity;
        } else {
            alpha = ((1 - progress) / 0.7) * flash.intensity;
        }

        // 绘制全屏闪光
        this.ctx.save();
        this.ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.restore();
    }

    // 获取按钮信息（供事件处理使用）
    getButtons() {
        // 使用新的布局配置
        let backButtonConfig;
        if (typeof CONFIG_UTILS !== 'undefined') {
            const layoutCalc = CONFIG_UTILS.calculateLayout(this.canvas.width, this.canvas.height);
            backButtonConfig = layoutCalc.backButton;
        } else {
            backButtonConfig = { x: 20, y: 30, width: 100, height: 32 };
        }

        const buttons = {
            back: {
                id: 'back',
                x: backButtonConfig.x,
                y: backButtonConfig.y,
                width: backButtonConfig.width,
                height: backButtonConfig.height,
                text: '返回'
            }
        };
        
        // 如果显示退出确认弹框，添加弹框按钮
        if (this.core.showExitDialog) {
            buttons.continueGame = {
                id: 'continueGame',
                x: this.canvas.width / 2 - 120,
                y: this.canvas.height / 2 + 20,
                width: 100,
                height: 40,
                text: '继续游戏'
            };
            buttons.exitGame = {
                id: 'exitGame',
                x: this.canvas.width / 2 + 20,
                y: this.canvas.height / 2 + 20,
                width: 100,
                height: 40,
                text: '返回主页'
            };
        }
        
        return buttons;
    }
    
    // 检查点击是否在按钮内
    isPointInButton(x, y, button) {
        return x >= button.x && 
               x <= button.x + button.width && 
               y >= button.y && 
               y <= button.y + button.height;
    }
    
    // 颜色工具函数
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageRenderer;
} else {
    window.GamePageRenderer = GamePageRenderer;
}