// 背景工具类 - 负责背景效果和动画
class BackgroundUtils {
    // 创建星星背景效果
    static createStars(count, canvasWidth, canvasHeight) {
        const stars = [];
        
        for (let i = 0; i < count; i++) {
            stars.push({
                x: Math.random() * canvasWidth,
                y: Math.random() * canvasHeight,
                size: Math.random() * 3 + 1,
                speed: Math.random() * 0.5 + 0.1,
                opacity: Math.random() * 0.8 + 0.2,
                twinkle: Math.random() * Math.PI * 2
            });
        }
        
        return stars;
    }
    
    // 更新星星位置
    static updateStars(stars, canvasWidth, canvasHeight) {
        if (!stars || !Array.isArray(stars)) return;

        // 安全获取画布尺寸
        const width = canvasWidth || (typeof window !== 'undefined' ? window.innerWidth : 800);
        const height = canvasHeight || (typeof window !== 'undefined' ? window.innerHeight : 600);

        stars.forEach(star => {
            star.y += star.speed;
            star.twinkle += 0.02;

            // 重置超出屏幕的星星
            if (star.y > height + 10) {
                star.y = -10;
                star.x = Math.random() * width;
            }
        });
    }
    
    // 渲染星星
    static renderStars(ctx, stars) {
        if (!stars || !Array.isArray(stars)) return;
        
        ctx.save();
        
        stars.forEach(star => {
            const alpha = star.opacity * (0.5 + 0.5 * Math.sin(star.twinkle));
            ctx.globalAlpha = alpha;
            ctx.fillStyle = '#FFFFFF';
            
            ctx.beginPath();
            ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
            ctx.fill();
        });
        
        ctx.restore();
    }
    
    // 创建渐变背景
    static createGradientBackground(ctx, width, height, colors) {
        const gradient = ctx.createLinearGradient(0, 0, 0, height);
        
        colors.forEach((color, index) => {
            gradient.addColorStop(index / (colors.length - 1), color);
        });
        
        return gradient;
    }
    
    // 绘制渐变背景
    static renderGradientBackground(ctx, width, height, colors) {
        const gradient = this.createGradientBackground(ctx, width, height, colors);
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
    }
    
    // 创建浮动粒子效果
    static createFloatingParticles(count, canvasWidth, canvasHeight) {
        const particles = [];
        
        for (let i = 0; i < count; i++) {
            particles.push({
                x: Math.random() * canvasWidth,
                y: Math.random() * canvasHeight,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                size: Math.random() * 4 + 2,
                color: `hsl(${Math.random() * 360}, 70%, 80%)`,
                life: 1.0,
                decay: Math.random() * 0.01 + 0.005
            });
        }
        
        return particles;
    }
    
    // 更新浮动粒子
    static updateFloatingParticles(particles, canvasWidth, canvasHeight) {
        if (!particles || !Array.isArray(particles)) return [];
        
        return particles.filter(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= particle.decay;
            
            // 边界反弹
            if (particle.x <= 0 || particle.x >= canvasWidth) {
                particle.vx *= -1;
            }
            if (particle.y <= 0 || particle.y >= canvasHeight) {
                particle.vy *= -1;
            }
            
            // 保持在边界内
            particle.x = Math.max(0, Math.min(canvasWidth, particle.x));
            particle.y = Math.max(0, Math.min(canvasHeight, particle.y));
            
            return particle.life > 0;
        });
    }
    
    // 渲染浮动粒子
    static renderFloatingParticles(ctx, particles) {
        if (!particles || !Array.isArray(particles)) return;
        
        ctx.save();
        
        particles.forEach(particle => {
            ctx.globalAlpha = particle.life;
            ctx.fillStyle = particle.color;
            
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();
        });
        
        ctx.restore();
    }
    
    // 创建波浪效果
    static createWaveEffect(canvasWidth, canvasHeight) {
        return {
            amplitude: 20,
            frequency: 0.02,
            speed: 0.05,
            offset: 0,
            y: canvasHeight * 0.8
        };
    }
    
    // 更新波浪效果
    static updateWaveEffect(wave) {
        if (!wave) return;
        
        wave.offset += wave.speed;
    }
    
    // 渲染波浪效果
    static renderWaveEffect(ctx, wave, canvasWidth, canvasHeight) {
        if (!wave) return;
        
        ctx.save();
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        
        ctx.beginPath();
        ctx.moveTo(0, canvasHeight);
        
        for (let x = 0; x <= canvasWidth; x += 5) {
            const y = wave.y + Math.sin((x * wave.frequency) + wave.offset) * wave.amplitude;
            ctx.lineTo(x, y);
        }
        
        ctx.lineTo(canvasWidth, canvasHeight);
        ctx.closePath();
        ctx.fill();
        
        ctx.restore();
    }
    
    // 创建云朵效果
    static createClouds(count, canvasWidth, canvasHeight) {
        const clouds = [];
        
        for (let i = 0; i < count; i++) {
            clouds.push({
                x: Math.random() * canvasWidth,
                y: Math.random() * canvasHeight * 0.5,
                width: Math.random() * 100 + 50,
                height: Math.random() * 40 + 20,
                speed: Math.random() * 0.5 + 0.1,
                opacity: Math.random() * 0.3 + 0.1
            });
        }
        
        return clouds;
    }
    
    // 更新云朵位置
    static updateClouds(clouds, canvasWidth) {
        if (!clouds || !Array.isArray(clouds)) return;
        
        clouds.forEach(cloud => {
            cloud.x += cloud.speed;
            
            // 重置超出屏幕的云朵
            if (cloud.x > canvasWidth + cloud.width) {
                cloud.x = -cloud.width;
                cloud.y = Math.random() * window.innerHeight * 0.5;
            }
        });
    }
    
    // 渲染云朵
    static renderClouds(ctx, clouds) {
        if (!clouds || !Array.isArray(clouds)) return;
        
        ctx.save();
        
        clouds.forEach(cloud => {
            ctx.globalAlpha = cloud.opacity;
            ctx.fillStyle = '#FFFFFF';
            
            // 绘制椭圆形云朵
            ctx.beginPath();
            ctx.ellipse(cloud.x, cloud.y, cloud.width / 2, cloud.height / 2, 0, 0, Math.PI * 2);
            ctx.fill();
            
            // 添加一些小圆形来增加云朵的层次感
            for (let i = 0; i < 3; i++) {
                const offsetX = (Math.random() - 0.5) * cloud.width * 0.5;
                const offsetY = (Math.random() - 0.5) * cloud.height * 0.5;
                const radius = Math.random() * 15 + 10;
                
                ctx.beginPath();
                ctx.arc(cloud.x + offsetX, cloud.y + offsetY, radius, 0, Math.PI * 2);
                ctx.fill();
            }
        });
        
        ctx.restore();
    }
    
    // 创建彩虹效果
    static createRainbow(canvasWidth, canvasHeight) {
        return {
            centerX: canvasWidth / 2,
            centerY: canvasHeight * 1.2,
            radius: canvasWidth * 0.8,
            colors: [
                'rgba(255, 0, 0, 0.3)',
                'rgba(255, 165, 0, 0.3)',
                'rgba(255, 255, 0, 0.3)',
                'rgba(0, 255, 0, 0.3)',
                'rgba(0, 0, 255, 0.3)',
                'rgba(75, 0, 130, 0.3)',
                'rgba(238, 130, 238, 0.3)'
            ]
        };
    }
    
    // 渲染彩虹效果
    static renderRainbow(ctx, rainbow) {
        if (!rainbow) return;
        
        ctx.save();
        
        rainbow.colors.forEach((color, index) => {
            const radius = rainbow.radius - (index * 15);
            
            ctx.strokeStyle = color;
            ctx.lineWidth = 12;
            ctx.beginPath();
            ctx.arc(rainbow.centerX, rainbow.centerY, radius, Math.PI, 0);
            ctx.stroke();
        });
        
        ctx.restore();
    }
    
    // 创建完整的背景场景
    static createBackgroundScene(canvasWidth, canvasHeight) {
        return {
            stars: this.createStars(20, canvasWidth, canvasHeight),
            clouds: this.createClouds(5, canvasWidth, canvasHeight),
            particles: this.createFloatingParticles(15, canvasWidth, canvasHeight),
            wave: this.createWaveEffect(canvasWidth, canvasHeight),
            rainbow: this.createRainbow(canvasWidth, canvasHeight)
        };
    }
    
    // 更新完整的背景场景
    static updateBackgroundScene(scene, canvasWidth, canvasHeight) {
        if (!scene) return;
        
        this.updateStars(scene.stars);
        this.updateClouds(scene.clouds, canvasWidth);
        scene.particles = this.updateFloatingParticles(scene.particles, canvasWidth, canvasHeight);
        this.updateWaveEffect(scene.wave);
    }
    
    // 渲染完整的背景场景
    static renderBackgroundScene(ctx, scene, canvasWidth, canvasHeight) {
        if (!scene) return;
        
        // 渲染渐变背景
        this.renderGradientBackground(ctx, canvasWidth, canvasHeight, [
            '#87CEEB', '#98FB98', '#DDA0DD'
        ]);
        
        // 渲染彩虹（在最底层）
        this.renderRainbow(ctx, scene.rainbow);
        
        // 渲染云朵
        this.renderClouds(ctx, scene.clouds);
        
        // 渲染星星
        this.renderStars(ctx, scene.stars);
        
        // 渲染浮动粒子
        this.renderFloatingParticles(ctx, scene.particles);
        
        // 渲染波浪效果
        this.renderWaveEffect(ctx, scene.wave, canvasWidth, canvasHeight);
    }
    
    // 绘制完整背景 - 为排行榜页面提供的统一接口
    static drawCompleteBackground(ctx, canvasWidth, canvasHeight, stars, animationTime) {
        // 绘制渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(0.5, '#764ba2');
        gradient.addColorStop(1, '#667eea');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);
        
        // 绘制星星效果
        if (stars && Array.isArray(stars)) {
            this.renderStars(ctx, stars);
        }
        
        // 添加一些装饰性的圆点
        ctx.save();
        ctx.globalAlpha = 0.3;
        
        for (let i = 0; i < 10; i++) {
            const x = (canvasWidth / 10) * i + Math.sin(animationTime + i) * 20;
            const y = canvasHeight * 0.2 + Math.cos(animationTime + i * 0.5) * 30;
            const radius = 3 + Math.sin(animationTime * 2 + i) * 2;
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fill();
        }
        
        ctx.restore();
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BackgroundUtils;
} else {
    window.BackgroundUtils = BackgroundUtils;
}
