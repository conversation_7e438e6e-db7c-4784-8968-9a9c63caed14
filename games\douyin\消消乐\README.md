# 萌宠消消乐游戏

基于抖音原生开发的消消乐游戏，严格按照游戏界面设计文档实现。

## 游戏特性

### 核心玩法
- **8×10网格系统**：经典消消乐布局
- **9种萌宠类型**：猫、狗、狐狸、青蛙、狮子、猴子、熊猫、兔子、老虎
- **3/4/5连消机制**：不同连消长度对应不同分数
- **特殊方块系统**：
  - 4连消生成火箭（消除整行/列）
  - 5连消生成炸弹（3×3爆炸）
- **连击系统**：2秒内连续消除可获得倍率加成

### 关卡设计
1. **第一关：萌宠新手村** - 目标1000分，5种萌宠
2. **第二关：萌宠总动员** - 目标4000分，7种萌宠  
3. **第三关：萌宠修罗场** - 目标8000分，9种萌宠

### 道具系统
- **刷新卡**：随机洗牌网格（×3）
- **炸弹卡**：5×5范围爆炸（×2）
- **清屏卡**：清除所有萌宠（×1）
- **降级卡**：减少萌宠种类（×1）

### 界面布局
- **顶部统计栏**（15%）：关卡信息、分数、进度条、连击显示
- **中部游戏区域**（70%）：8×10网格，萌宠交互区域
- **底部道具栏**（15%）：四种道具卡片

## 技术实现

### 核心架构
- 游戏状态管理（加载/菜单/游戏中/关卡完成/游戏结束）
- 资源管理系统（图片/音效动态加载）
- 网格系统（防初始3连消算法）
- 消除检测算法（水平/垂直匹配）

### 交互系统
- 触摸拖拽交换
- 点击选择操作
- 道具使用机制
- 震动反馈

### 性能优化
- 60FPS帧率控制
- 资源动态加载
- 对象池管理
- 异步处理

### 音效系统
- 背景音乐
- 消除音效
- 连击特效音
- 胜利/失败音效

## 文件结构

```
games/douyin/消消乐/
├── game.js              # 主游戏逻辑
├── config.js            # 游戏配置
├── game.json            # 项目配置
├── project.config.json  # 抖音小游戏配置
├── 游戏界面设计文档.md    # 设计文档
├── audios/              # 音效资源
├── images/              # 图片资源
│   ├── animal/          # 萌宠图片
│   ├── button/          # 按钮图片
│   ├── extra/           # 特殊方块图片
│   ├── icon/            # 图标
│   └── prop/            # 道具图片
└── README.md            # 说明文档
```

## 游戏操作

### 基础操作
1. **开始游戏**：点击主界面"开始游戏"按钮
2. **萌宠交换**：拖拽相邻萌宠进行交换
3. **使用道具**：点击底部道具卡片使用
4. **炸弹道具**：选择炸弹后点击目标位置

### 游戏目标
- 达到关卡目标分数即可通关
- 无可用移动时可使用复活机制（每局限2次）
- 通过三个关卡完成游戏

## 开发说明

### 运行环境
- 抖音开发者工具
- 支持Canvas 2D渲染
- 支持触摸事件

### 配置要求
- 包体大小：≤15MB
- 帧率：≥50FPS
- 分辨率：自适应屏幕尺寸

### 扩展功能
- 社交分享集成
- 好友排行榜
- 直播互动功能
- UGC内容生成

## 更新日志

### v1.0.0
- 完整实现游戏界面设计文档要求
- 支持三个关卡
- 完整的道具系统
- 音效和动画系统
- 性能优化和错误处理

---

基于抖音原生开发特性，轻量化资源设计，高流畅性体验，适合快速迭代与多端适配。
