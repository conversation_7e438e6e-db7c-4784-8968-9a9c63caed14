// 游戏配置文件 - 统一管理所有可控参数
var GameConfig = {
  // 游戏基础配置
  GAME: {
    VERSION: 'v1.0.0',
    TITLE: '萌宠爱消消',
    TARGET_FPS: 50,
    MAX_PACKAGE_SIZE: 15 * 1024 * 1024, // 15MB
  },

  // 网格配置
  GRID: {
    ROWS: 10,
    COLS: 8,
    CELL_SIZE: 64, // 基础格子大小，会根据屏幕动态调整
    PADDING: 10,
    BORDER_RADIUS: 8,
  },

  // 屏幕适配配置
  SCREEN: {
    DESIGN_WIDTH: 750, // 设计稿宽度
    DESIGN_HEIGHT: 1334, // 设计稿高度
    MIN_SCALE: 0.5,
    MAX_SCALE: 2.0,
    SAFE_AREA_TOP: 0.15, // 顶部安全区域占屏幕高度比例
    SAFE_AREA_BOTTOM: 0.1, // 底部安全区域占屏幕高度比例
  },

  // 关卡配置
  LEVELS: [
    {
      id: 1,
      name: '萌宠新手村',
      targetScore: 1000,
      animalTypes: 5,
      timeLimit: 180, // 3分钟
      description: '新手入门关卡'
    },
    {
      id: 2,
      name: '萌宠总动员',
      targetScore: 4000,
      animalTypes: 7,
      timeLimit: 240, // 4分钟
      description: '中级挑战关卡'
    },
    {
      id: 3,
      name: '萌宠修罗场',
      targetScore: 8000,
      animalTypes: 9,
      timeLimit: 300, // 5分钟
      description: '高级挑战关卡'
    }
  ],

  // 动物类型配置
  ANIMALS: [
    { id: 0, name: 'cat', image: 'images/animal/cat.png', color: '#FF6B8B' },
    { id: 1, name: 'dog', image: 'images/animal/dog.png', color: '#4ECDC4' },
    { id: 2, name: 'fox', image: 'images/animal/fox.png', color: '#45B7D1' },
    { id: 3, name: 'frog', image: 'images/animal/frog.png', color: '#96CEB4' },
    { id: 4, name: 'lion', image: 'images/animal/lion.png', color: '#FFEAA7' },
    { id: 5, name: 'monkey', image: 'images/animal/monkey.png', color: '#DDA0DD' },
    { id: 6, name: 'panda', image: 'images/animal/panda.png', color: '#98D8C8' },
    { id: 7, name: 'rabbit', image: 'images/animal/rabbit.png', color: '#F7DC6F' },
    { id: 8, name: 'tiger', image: 'images/animal/tiger.png', color: '#BB8FCE' }
  ],

  // 分数配置
  SCORE: {
    MATCH_3: 30,
    MATCH_4: 40,
    MATCH_5: 50,
    COMBO_MULTIPLIER: [1.0, 1.5, 2.0, 2.5, 3.0], // 连击倍率
    SPECIAL_BOMB: 500,
    SPECIAL_CLEAR: 800,
    COMBO_TIMEOUT: 2000, // 连击超时时间(ms)
  },

  // 道具配置
  PROPS: [
    {
      id: 'refresh',
      name: '刷新卡',
      image: 'images/prop/refresh_card.png',
      description: '打乱所有格子位置',
      type: 'click'
    },
    {
      id: 'bomb',
      name: '炸弹卡',
      image: 'images/prop/bomb_card.png',
      description: '5×5范围爆炸消除',
      type: 'drag',
      score: 500,
      audio: 'audios/bomb.mp3'
    },
    {
      id: 'clear',
      name: '清屏卡',
      image: 'images/prop/clear_card.png',
      description: '全屏消除',
      type: 'click',
      score: 800
    },
    {
      id: 'levelDown',
      name: '降级卡',
      image: 'images/prop/level_down_card.png',
      description: '减少一种萌宠类型',
      type: 'click'
    }
  ],

  // 音效配置
  AUDIO: {
    BACKGROUND: 'audios/background.mp3',
    EFFECTS: {
      NORMAL_MATCH: 'audios/so.mp3',
      COMBO_3: 'audios/wa.mp3',
      COMBO_5: 'audios/good.mp3',
      ROCKET: 'audios/shua.mp3',
      BOMB: 'audios/bomb.mp3',
      WIN: 'audios/win.mp3',
      LOSE: 'audios/lose.mp3',
      CAT: 'audios/cat.mp3'
    },
    DEFAULT_VOLUME: {
      BACKGROUND: 0.3,
      EFFECTS: 0.7
    }
  },

  // 动画配置
  ANIMATION: {
    BUTTON_HOVER_SCALE: 1.05,
    BUTTON_PRESS_SCALE: 0.95,
    TITLE_FLOAT_DURATION: 2000,
    PARTICLE_SPAWN_RATE: 4, // 每秒粒子数
    ELIMINATION_DURATION: 300,
    COMBO_DISPLAY_DURATION: 1000,
    PAGE_TRANSITION_DURATION: 500,
    HEART_FALL_SPEED: 100, // 像素/秒
  },

  // 颜色主题配置
  COLORS: {
    PRIMARY: '#FFEFD5', // 米黄色背景
    SECONDARY: '#FF6B8B', // 粉色主色调
    TEXT_PRIMARY: '#333333',
    TEXT_SECONDARY: '#888888',
    BUTTON_NORMAL: '#FFFFFF',
    BUTTON_HOVER: '#F0F0F0',
    COMBO_NORMAL: '#00FF00', // 绿色
    COMBO_HIGH: '#FF0000', // 红色
    OVERLAY: 'rgba(0, 0, 0, 0.5)',
    TRANSPARENT_WHITE: 'rgba(255, 255, 255, 0.8)',
    TRANSPARENT_GRAY: 'rgba(128, 128, 128, 0.8)'
  },

  // 字体配置
  FONTS: {
    TITLE: '48px Arial, sans-serif',
    SUBTITLE: '24px Arial, sans-serif',
    BUTTON: '18px Arial, sans-serif',
    SCORE: '20px Arial, sans-serif',
    VERSION: '14px Arial, sans-serif'
  },

  // 按钮配置
  BUTTONS: {
    WIDTH: 120,
    HEIGHT: 50,
    BORDER_RADIUS: 15,
    SPACING: 20,
    SHADOW: '0 4px 8px rgba(0, 0, 0, 0.2)'
  },

  // 性能配置
  PERFORMANCE: {
    MAX_PARTICLES: 100,
    PARTICLE_POOL_SIZE: 200,
    TEXTURE_ATLAS_SIZE: 128,
    LOW_END_THRESHOLD: 1024, // 低端机内存阈值(MB)
    ASYNC_LOAD_DELAY: 16, // 异步加载延迟(ms)
    GC_INTERVAL: 30000, // 垃圾回收间隔(ms)
  },

  // 存储键名配置
  STORAGE_KEYS: {
    HIGH_SCORE: 'xiaoxiaole_high_score',
    BEST_COMBO: 'xiaoxiaole_best_combo',
    BEST_TIME: 'xiaoxiaole_best_time',
    CURRENT_LEVEL: 'xiaoxiaole_current_level',
    SETTINGS: 'xiaoxiaole_settings',
    RANK_DATA: 'xiaoxiaole_rank_data'
  },

  // 默认设置
  DEFAULT_SETTINGS: {
    backgroundVolume: 30,
    effectVolume: 70,
    isMuted: false,
    enableParticles: true,
    enableVibration: true
  }
};

// 导出配置对象到全局作用域
// 抖音小游戏环境 - 确保全局可访问
(function() {
  // 尝试多种方式确保 GameConfig 在全局作用域中可用
  try {
    // 方式1: 直接赋值到 this
    this.GameConfig = GameConfig;

    // 方式2: 如果有 global 对象
    if (typeof global !== 'undefined') {
      global.GameConfig = GameConfig;
    }

    // 方式3: 如果有 window 对象
    if (typeof window !== 'undefined') {
      window.GameConfig = GameConfig;
    }

    // 方式4: 如果有 globalThis 对象
    if (typeof globalThis !== 'undefined') {
      globalThis.GameConfig = GameConfig;
    }

    // 调试输出
    console.log('GameConfig export attempts completed');
    console.log('GameConfig type check:', typeof GameConfig);
    console.log('this.GameConfig type check:', typeof this.GameConfig);

  } catch (error) {
    console.error('GameConfig export error:', error);
  }
})();

// 最后的验证
setTimeout(() => {
  console.log('GameConfig final check:', typeof GameConfig !== 'undefined');
}, 10);
