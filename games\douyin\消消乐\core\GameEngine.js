// 游戏引擎核心类
var GameEngine = class GameEngine {
  constructor() {
    this.systemInfo = tt.getSystemInfoSync();
    this.canvas = tt.createCanvas();
    this.ctx = this.canvas.getContext('2d');
    this.isRunning = false;
    this.lastTime = 0;
    this.deltaTime = 0;
    this.fps = 0;
    this.frameCount = 0;
    this.fpsUpdateTime = 0;
    
    // 屏幕适配
    this.scale = 1;
    this.offsetX = 0;
    this.offsetY = 0;
    
    // 初始化画布
    this.initCanvas();
    this.calculateScreenAdaptation();
    
    // 绑定事件
    this.bindEvents();
  }

  // 初始化画布
  initCanvas() {
    this.canvas.width = this.systemInfo.windowWidth;
    this.canvas.height = this.systemInfo.windowHeight;
    
    // 设置画布样式
    this.ctx.imageSmoothingEnabled = true;
    this.ctx.imageSmoothingQuality = 'high';
  }

  // 计算屏幕适配
  calculateScreenAdaptation() {
    const { windowWidth, windowHeight } = this.systemInfo;
    const { DESIGN_WIDTH, DESIGN_HEIGHT, MIN_SCALE, MAX_SCALE } = GameConfig.SCREEN;
    
    // 计算缩放比例
    const scaleX = windowWidth / DESIGN_WIDTH;
    const scaleY = windowHeight / DESIGN_HEIGHT;
    this.scale = Math.min(scaleX, scaleY);
    
    // 限制缩放范围
    this.scale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, this.scale));
    
    // 计算偏移量（居中显示）
    this.offsetX = (windowWidth - DESIGN_WIDTH * this.scale) / 2;
    this.offsetY = (windowHeight - DESIGN_HEIGHT * this.scale) / 2;
    
    console.log(`Screen adaptation: scale=${this.scale}, offset=(${this.offsetX}, ${this.offsetY})`);
  }

  // 屏幕坐标转换为设计坐标
  screenToDesign(x, y) {
    return {
      x: (x - this.offsetX) / this.scale,
      y: (y - this.offsetY) / this.scale
    };
  }

  // 设计坐标转换为屏幕坐标
  designToScreen(x, y) {
    return {
      x: x * this.scale + this.offsetX,
      y: y * this.scale + this.offsetY
    };
  }

  // 绑定事件
  bindEvents() {
    // 触摸事件
    this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this));
    this.canvas.addEventListener('touchend', this.handleTouchEnd.bind(this));
    
    // 鼠标事件（开发调试用）
    this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this));
  }

  // 触摸开始事件
  handleTouchStart(event) {
    event.preventDefault();
    const touch = event.touches[0];
    const designPos = this.screenToDesign(touch.clientX, touch.clientY);
    this.onInputStart(designPos.x, designPos.y);
  }

  // 触摸移动事件
  handleTouchMove(event) {
    event.preventDefault();
    const touch = event.touches[0];
    const designPos = this.screenToDesign(touch.clientX, touch.clientY);
    this.onInputMove(designPos.x, designPos.y);
  }

  // 触摸结束事件
  handleTouchEnd(event) {
    event.preventDefault();
    this.onInputEnd();
  }

  // 鼠标按下事件
  handleMouseDown(event) {
    const designPos = this.screenToDesign(event.clientX, event.clientY);
    this.onInputStart(designPos.x, designPos.y);
  }

  // 鼠标移动事件
  handleMouseMove(event) {
    const designPos = this.screenToDesign(event.clientX, event.clientY);
    this.onInputMove(designPos.x, designPos.y);
  }

  // 鼠标抬起事件
  handleMouseUp(event) {
    this.onInputEnd();
  }

  // 输入开始（由子类重写）
  onInputStart(x, y) {
    // 由具体页面实现
  }

  // 输入移动（由子类重写）
  onInputMove(x, y) {
    // 由具体页面实现
  }

  // 输入结束（由子类重写）
  onInputEnd() {
    // 由具体页面实现
  }

  // 开始游戏循环
  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.lastTime = performance.now();
    this.gameLoop();
  }

  // 停止游戏循环
  stop() {
    this.isRunning = false;
  }

  // 游戏主循环
  gameLoop() {
    if (!this.isRunning) return;

    const currentTime = performance.now();
    this.deltaTime = currentTime - this.lastTime;
    this.lastTime = currentTime;

    // 计算FPS
    this.frameCount++;
    this.fpsUpdateTime += this.deltaTime;
    if (this.fpsUpdateTime >= 1000) {
      this.fps = this.frameCount;
      this.frameCount = 0;
      this.fpsUpdateTime = 0;
    }

    // 更新和渲染
    this.update(this.deltaTime);
    this.render();

    // 请求下一帧
    requestAnimationFrame(() => this.gameLoop());
  }

  // 更新逻辑（由子类重写）
  update(deltaTime) {
    // 由具体页面实现
  }

  // 渲染逻辑（由子类重写）
  render() {
    // 清空画布
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // 设置变换矩阵
    this.ctx.save();
    this.ctx.translate(this.offsetX, this.offsetY);
    this.ctx.scale(this.scale, this.scale);
    
    // 由具体页面实现渲染逻辑
    this.renderContent();
    
    this.ctx.restore();
    
    // 渲染调试信息
    if (this.showDebugInfo) {
      this.renderDebugInfo();
    }
  }

  // 渲染内容（由子类重写）
  renderContent() {
    // 由具体页面实现
  }

  // 渲染调试信息
  renderDebugInfo() {
    this.ctx.save();
    this.ctx.fillStyle = '#000000';
    this.ctx.font = '16px Arial';
    this.ctx.fillText(`FPS: ${this.fps}`, 10, 30);
    this.ctx.fillText(`Scale: ${this.scale.toFixed(2)}`, 10, 50);
    this.ctx.fillText(`Offset: (${this.offsetX.toFixed(0)}, ${this.offsetY.toFixed(0)})`, 10, 70);
    this.ctx.restore();
  }

  // 销毁引擎
  destroy() {
    this.stop();
    
    // 移除事件监听器
    this.canvas.removeEventListener('touchstart', this.handleTouchStart);
    this.canvas.removeEventListener('touchmove', this.handleTouchMove);
    this.canvas.removeEventListener('touchend', this.handleTouchEnd);
    this.canvas.removeEventListener('mousedown', this.handleMouseDown);
    this.canvas.removeEventListener('mousemove', this.handleMouseMove);
    this.canvas.removeEventListener('mouseup', this.handleMouseUp);
    
    console.log('GameEngine destroyed');
  }

  // 获取画布尺寸
  getCanvasSize() {
    return {
      width: this.canvas.width,
      height: this.canvas.height
    };
  }

  // 获取设计尺寸
  getDesignSize() {
    return {
      width: GameConfig.SCREEN.DESIGN_WIDTH,
      height: GameConfig.SCREEN.DESIGN_HEIGHT
    };
  }
}

// 导出类到全局作用域
if (typeof global !== 'undefined') {
  global.GameEngine = GameEngine;
}
if (typeof window !== 'undefined') {
  window.GameEngine = GameEngine;
}
// 抖音小游戏环境
if (typeof tt !== 'undefined') {
  this.GameEngine = GameEngine;
}
