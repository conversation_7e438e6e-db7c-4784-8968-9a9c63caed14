// 页面管理器 - 负责页面切换和生命周期管理
var PageManager = class PageManager {
  constructor(gameEngine) {
    this.gameEngine = gameEngine;
    this.currentPage = null;
    this.pages = new Map();
    this.isTransitioning = false;
    this.transitionProgress = 0;
    this.transitionDuration = GameConfig.ANIMATION.PAGE_TRANSITION_DURATION;
    
    // 注册所有页面类
    this.registerPages();
  }

  // 注册页面类
  registerPages() {
    // 页面类将在各自的文件中定义
    this.pageClasses = {
      'home': null,      // HomePage
      'game': null,      // GamePage
      'rank': null,      // RankPage
      'setting': null    // SettingPage
    };
  }

  // 设置页面类
  setPageClass(pageName, pageClass) {
    this.pageClasses[pageName] = pageClass;
  }

  // 切换到指定页面
  async navigateTo(pageName, params = {}) {
    if (this.isTransitioning) {
      console.warn('Page transition in progress, ignoring navigation request');
      return;
    }

    console.log(`Navigating to page: ${pageName}`);
    
    // 检查页面类是否存在
    const PageClass = this.pageClasses[pageName];
    if (!PageClass) {
      console.error(`Page class not found: ${pageName}`);
      return;
    }

    // 开始转场动画
    this.isTransitioning = true;
    this.transitionProgress = 0;

    // 如果有当前页面，先执行退出动画
    if (this.currentPage) {
      await this.transitionOut();
      this.destroyCurrentPage();
    }

    // 创建新页面
    const newPage = new PageClass(this.gameEngine, this);
    this.pages.set(pageName, newPage);
    this.currentPage = newPage;

    // 初始化新页面
    await newPage.init(params);

    // 执行进入动画
    await this.transitionIn();

    this.isTransitioning = false;
    console.log(`Navigation completed: ${pageName}`);
  }

  // 退出转场动画
  async transitionOut() {
    return new Promise((resolve) => {
      const startTime = performance.now();
      
      const animate = (currentTime) => {
        const elapsed = currentTime - startTime;
        this.transitionProgress = Math.min(elapsed / this.transitionDuration, 1);
        
        if (this.transitionProgress >= 1) {
          resolve();
        } else {
          requestAnimationFrame(animate);
        }
      };
      
      requestAnimationFrame(animate);
    });
  }

  // 进入转场动画
  async transitionIn() {
    return new Promise((resolve) => {
      const startTime = performance.now();
      
      const animate = (currentTime) => {
        const elapsed = currentTime - startTime;
        this.transitionProgress = 1 - Math.min(elapsed / this.transitionDuration, 1);
        
        if (this.transitionProgress <= 0) {
          this.transitionProgress = 0;
          resolve();
        } else {
          requestAnimationFrame(animate);
        }
      };
      
      requestAnimationFrame(animate);
    });
  }

  // 销毁当前页面
  destroyCurrentPage() {
    if (this.currentPage) {
      console.log('Destroying current page');
      
      // 调用页面的销毁方法
      if (typeof this.currentPage.destroy === 'function') {
        this.currentPage.destroy();
      }
      
      // 清理页面引用
      this.currentPage = null;
      
      // 强制垃圾回收（如果支持）
      if (typeof tt.triggerGC === 'function') {
        tt.triggerGC();
      }
    }
  }

  // 返回上一页
  goBack() {
    // 简单实现：根据当前页面决定返回目标
    if (!this.currentPage) return;
    
    const currentPageName = this.getCurrentPageName();
    
    switch (currentPageName) {
      case 'game':
      case 'rank':
      case 'setting':
        this.navigateTo('home');
        break;
      default:
        console.log('No back navigation defined for current page');
    }
  }

  // 获取当前页面名称
  getCurrentPageName() {
    for (const [name, page] of this.pages.entries()) {
      if (page === this.currentPage) {
        return name;
      }
    }
    return null;
  }

  // 更新当前页面
  update(deltaTime) {
    if (this.currentPage && typeof this.currentPage.update === 'function') {
      this.currentPage.update(deltaTime);
    }
  }

  // 渲染当前页面和转场效果
  render() {
    const ctx = this.gameEngine.ctx;
    
    // 渲染当前页面
    if (this.currentPage && typeof this.currentPage.render === 'function') {
      this.currentPage.render();
    }
    
    // 渲染转场效果
    if (this.isTransitioning && this.transitionProgress > 0) {
      this.renderTransition();
    }
  }

  // 渲染转场效果（爱心扩散效果）
  renderTransition() {
    const ctx = this.gameEngine.ctx;
    const { width, height } = this.gameEngine.getDesignSize();
    
    ctx.save();
    
    // 创建径向渐变
    const centerX = width / 2;
    const centerY = height / 2;
    const maxRadius = Math.sqrt(width * width + height * height) / 2;
    const currentRadius = maxRadius * this.transitionProgress;
    
    // 绘制遮罩
    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = GameConfig.COLORS.SECONDARY;
    ctx.fillRect(0, 0, width, height);
    
    // 创建爱心形状的遮罩
    ctx.globalCompositeOperation = 'destination-out';
    this.drawHeart(ctx, centerX, centerY, currentRadius);
    
    ctx.restore();
  }

  // 绘制爱心形状
  drawHeart(ctx, x, y, size) {
    ctx.save();
    ctx.translate(x, y);
    ctx.scale(size / 50, size / 50); // 标准化尺寸
    
    ctx.beginPath();
    ctx.moveTo(0, -15);
    ctx.bezierCurveTo(-25, -35, -55, -15, -25, 5);
    ctx.bezierCurveTo(-25, 5, 0, 25, 0, 25);
    ctx.bezierCurveTo(0, 25, 25, 5, 25, 5);
    ctx.bezierCurveTo(55, -15, 25, -35, 0, -15);
    ctx.closePath();
    ctx.fill();
    
    ctx.restore();
  }

  // 处理输入事件
  onInputStart(x, y) {
    if (this.isTransitioning) return;
    
    if (this.currentPage && typeof this.currentPage.onInputStart === 'function') {
      this.currentPage.onInputStart(x, y);
    }
  }

  onInputMove(x, y) {
    if (this.isTransitioning) return;
    
    if (this.currentPage && typeof this.currentPage.onInputMove === 'function') {
      this.currentPage.onInputMove(x, y);
    }
  }

  onInputEnd() {
    if (this.isTransitioning) return;
    
    if (this.currentPage && typeof this.currentPage.onInputEnd === 'function') {
      this.currentPage.onInputEnd();
    }
  }

  // 销毁页面管理器
  destroy() {
    console.log('Destroying PageManager');
    
    // 销毁当前页面
    this.destroyCurrentPage();
    
    // 清理所有页面
    for (const [name, page] of this.pages.entries()) {
      if (page && typeof page.destroy === 'function') {
        page.destroy();
      }
    }
    
    this.pages.clear();
    this.pageClasses = {};
    this.gameEngine = null;
  }

  // 获取页面实例
  getPage(pageName) {
    return this.pages.get(pageName);
  }

  // 检查是否正在转场
  isInTransition() {
    return this.isTransitioning;
  }
}

// 导出类到全局作用域
// 抖音小游戏环境 - 直接赋值到全局
PageManager = PageManager;

// 其他环境兼容
if (typeof global !== 'undefined') {
  global.PageManager = PageManager;
}
if (typeof window !== 'undefined') {
  window.PageManager = PageManager;
}

// 调试输出
console.log('PageManager loaded:', typeof PageManager !== 'undefined');
