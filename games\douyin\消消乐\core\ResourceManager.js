// 资源管理器 - 负责图片、音频等资源的加载和管理
class ResourceManager {
  constructor() {
    this.images = new Map();
    this.audios = new Map();
    this.loadedResources = new Set();
    this.loadingPromises = new Map();
    this.isLowEndDevice = Utils.Device.isLowEndDevice();
    
    console.log(`ResourceManager initialized, low-end device: ${this.isLowEndDevice}`);
  }

  // 预加载基础资源
  async preloadBasicResources() {
    console.log('Preloading basic resources...');
    
    const basicImages = [
      'images/title.png',
      'images/button/start.png',
      'images/button/ranking.png',
      'images/icon/icon.png'
    ];

    const basicAudios = [
      GameConfig.AUDIO.BACKGROUND
    ];

    try {
      await Promise.all([
        this.loadImages(basicImages),
        this.loadAudios(basicAudios)
      ]);
      console.log('Basic resources loaded successfully');
    } catch (error) {
      console.error('Failed to load basic resources:', error);
      throw error;
    }
  }

  // 加载单个图片
  async loadImage(src) {
    if (this.images.has(src)) {
      return this.images.get(src);
    }

    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src);
    }

    const loadPromise = new Promise(async (resolve, reject) => {
      try {
        console.log(`Loading image: ${src}`);
        
        // 低端设备延迟加载
        if (this.isLowEndDevice) {
          await Utils.Time.delay(GameConfig.PERFORMANCE.ASYNC_LOAD_DELAY);
        }

        const image = await Utils.Image.load(src);
        this.images.set(src, image);
        this.loadedResources.add(src);
        
        console.log(`Image loaded: ${src}`);
        resolve(image);
      } catch (error) {
        console.error(`Failed to load image: ${src}`, error);
        reject(error);
      } finally {
        this.loadingPromises.delete(src);
      }
    });

    this.loadingPromises.set(src, loadPromise);
    return loadPromise;
  }

  // 批量加载图片
  async loadImages(sources) {
    const promises = sources.map(src => this.loadImage(src));
    return Promise.all(promises);
  }

  // 加载单个音频
  async loadAudio(src, options = {}) {
    if (this.audios.has(src)) {
      return this.audios.get(src);
    }

    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src);
    }

    const loadPromise = new Promise((resolve, reject) => {
      try {
        console.log(`Loading audio: ${src}`);
        
        const audio = Utils.Audio.create(src, options);
        
        // 音频加载完成回调
        audio.onCanplay(() => {
          this.audios.set(src, audio);
          this.loadedResources.add(src);
          console.log(`Audio loaded: ${src}`);
          resolve(audio);
        });

        // 音频加载错误回调
        audio.onError((error) => {
          console.error(`Failed to load audio: ${src}`, error);
          reject(error);
        });

      } catch (error) {
        console.error(`Failed to create audio: ${src}`, error);
        reject(error);
      } finally {
        this.loadingPromises.delete(src);
      }
    });

    this.loadingPromises.set(src, loadPromise);
    return loadPromise;
  }

  // 批量加载音频
  async loadAudios(sources) {
    const promises = sources.map(src => this.loadAudio(src));
    return Promise.all(promises);
  }

  // 按关卡加载动物图片
  async loadAnimalsByLevel(level) {
    const levelConfig = GameConfig.LEVELS[level - 1];
    if (!levelConfig) {
      throw new Error(`Invalid level: ${level}`);
    }

    const animalSources = GameConfig.ANIMALS
      .slice(0, levelConfig.animalTypes)
      .map(animal => animal.image);

    console.log(`Loading animals for level ${level}:`, animalSources);
    return this.loadImages(animalSources);
  }

  // 加载道具图片
  async loadPropImages() {
    const propSources = GameConfig.PROPS.map(prop => prop.image);
    console.log('Loading prop images:', propSources);
    return this.loadImages(propSources);
  }

  // 加载游戏音效
  async loadGameAudios() {
    const audioSources = Object.values(GameConfig.AUDIO.EFFECTS);
    console.log('Loading game audios:', audioSources);
    return this.loadAudios(audioSources);
  }

  // 获取图片
  getImage(src) {
    const image = this.images.get(src);
    if (!image) {
      console.warn(`Image not loaded: ${src}`);
    }
    return image;
  }

  // 获取音频
  getAudio(src) {
    const audio = this.audios.get(src);
    if (!audio) {
      console.warn(`Audio not loaded: ${src}`);
    }
    return audio;
  }

  // 播放音效
  playEffect(src, volume = null) {
    const settings = Utils.Storage.get(GameConfig.STORAGE_KEYS.SETTINGS, GameConfig.DEFAULT_SETTINGS);
    
    // 检查是否静音
    if (settings.isMuted) {
      return null;
    }

    // 计算音量
    const effectVolume = volume !== null ? volume : settings.effectVolume / 100;
    
    try {
      return Utils.Audio.playEffect(src, effectVolume);
    } catch (error) {
      console.error(`Failed to play effect: ${src}`, error);
      return null;
    }
  }

  // 播放背景音乐
  playBackgroundMusic() {
    const settings = Utils.Storage.get(GameConfig.STORAGE_KEYS.SETTINGS, GameConfig.DEFAULT_SETTINGS);
    
    if (settings.isMuted) {
      return null;
    }

    const bgAudio = this.getAudio(GameConfig.AUDIO.BACKGROUND);
    if (bgAudio) {
      bgAudio.volume = settings.backgroundVolume / 100;
      bgAudio.loop = true;
      bgAudio.play();
      return bgAudio;
    }
    
    return null;
  }

  // 停止背景音乐
  stopBackgroundMusic() {
    const bgAudio = this.getAudio(GameConfig.AUDIO.BACKGROUND);
    if (bgAudio) {
      bgAudio.stop();
    }
  }

  // 卸载资源
  unloadResource(src) {
    if (this.images.has(src)) {
      this.images.delete(src);
      this.loadedResources.delete(src);
      console.log(`Image unloaded: ${src}`);
    }

    if (this.audios.has(src)) {
      const audio = this.audios.get(src);
      if (audio && typeof audio.destroy === 'function') {
        audio.destroy();
      }
      this.audios.delete(src);
      this.loadedResources.delete(src);
      console.log(`Audio unloaded: ${src}`);
    }
  }

  // 卸载非活跃资源
  unloadInactiveResources(activeResources = []) {
    const activeSet = new Set(activeResources);
    
    // 卸载非活跃图片
    for (const [src, image] of this.images.entries()) {
      if (!activeSet.has(src)) {
        this.unloadResource(src);
      }
    }

    // 卸载非活跃音频（保留背景音乐）
    for (const [src, audio] of this.audios.entries()) {
      if (!activeSet.has(src) && src !== GameConfig.AUDIO.BACKGROUND) {
        this.unloadResource(src);
      }
    }

    // 触发垃圾回收
    this.triggerGC();
  }

  // 触发垃圾回收
  triggerGC() {
    if (typeof tt.triggerGC === 'function') {
      tt.triggerGC();
      console.log('Garbage collection triggered');
    }
  }

  // 获取资源加载状态
  getLoadingStatus() {
    return {
      totalImages: this.images.size,
      totalAudios: this.audios.size,
      totalResources: this.loadedResources.size,
      isLoading: this.loadingPromises.size > 0
    };
  }

  // 检查资源是否已加载
  isResourceLoaded(src) {
    return this.loadedResources.has(src);
  }

  // 销毁资源管理器
  destroy() {
    console.log('Destroying ResourceManager');
    
    // 停止所有音频
    for (const [src, audio] of this.audios.entries()) {
      if (audio && typeof audio.destroy === 'function') {
        audio.destroy();
      }
    }

    // 清理所有资源
    this.images.clear();
    this.audios.clear();
    this.loadedResources.clear();
    this.loadingPromises.clear();

    // 触发垃圾回收
    this.triggerGC();
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ResourceManager;
} else if (typeof window !== 'undefined') {
  window.ResourceManager = ResourceManager;
}
