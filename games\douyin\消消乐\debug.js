// 调试文件 - 用于测试所有类是否正确加载
console.log('=== 调试模式启动 ===');

// 延迟执行，确保所有脚本都已加载
setTimeout(() => {
  console.log('开始检查类加载状态...');
  
  const requiredClasses = [
    'GameConfig',
    'Utils',
    'GameEngine', 
    'PageManager',
    'ResourceManager',
    'HomePage',
    'GamePage',
    'RankPage', 
    'SettingPage',
    'ParticleSystem',
    'AudioManager'
  ];
  
  let allLoaded = true;
  
  for (const className of requiredClasses) {
    try {
      // 尝试直接访问
      if (typeof eval(className) !== 'undefined') {
        console.log(`✅ ${className} 已加载`);
      } else if (typeof this[className] !== 'undefined') {
        console.log(`✅ ${className} 已加载 (this)`);
      } else if (typeof window !== 'undefined' && typeof window[className] !== 'undefined') {
        console.log(`✅ ${className} 已加载 (window)`);
      } else if (typeof global !== 'undefined' && typeof global[className] !== 'undefined') {
        console.log(`✅ ${className} 已加载 (global)`);
      } else {
        console.error(`❌ ${className} 未加载`);
        allLoaded = false;
      }
    } catch (error) {
      console.error(`❌ ${className} 检查失败:`, error);
      allLoaded = false;
    }
  }
  
  if (allLoaded) {
    console.log('🎉 所有类都已正确加载！');
    
    // 测试基本功能
    try {
      console.log('测试基本功能...');
      
      // 测试配置
      console.log(`游戏版本: ${GameConfig.GAME.VERSION}`);
      console.log(`网格尺寸: ${GameConfig.GRID.ROWS}x${GameConfig.GRID.COLS}`);
      
      // 测试工具类
      const randomNum = Utils.Math.randomInt(1, 10);
      console.log(`随机数测试: ${randomNum}`);
      
      console.log('✅ 基本功能测试通过');
      
    } catch (error) {
      console.error('❌ 基本功能测试失败:', error);
    }
    
  } else {
    console.error('⚠️ 部分类未加载，请检查脚本配置');
  }
  
}, 500);

// 导出调试函数
if (typeof this !== 'undefined') {
  this.debugGame = () => {
    console.log('=== 游戏状态调试 ===');
    
    if (typeof gameInstance !== 'undefined' && gameInstance) {
      console.log('游戏实例状态:', {
        isInitialized: gameInstance.isInitialized,
        currentPage: gameInstance.pageManager ? gameInstance.pageManager.getCurrentPageName() : 'none',
        fps: gameInstance.performanceMonitor ? gameInstance.performanceMonitor.currentFPS : 'unknown'
      });
    } else {
      console.log('游戏实例未创建');
    }
  };
}
