// 音效管理器类
var AudioManager = class AudioManager {
  constructor() {
    this.audioInstances = new Map();
    this.backgroundMusic = null;
    this.settings = {
      backgroundVolume: 0.3,
      effectVolume: 0.7,
      isMuted: false
    };
    this.isInitialized = false;
    
    // 音效队列（用于避免同时播放太多音效）
    this.effectQueue = [];
    this.maxConcurrentEffects = 5;
    this.currentEffects = [];
  }

  // 初始化音效管理器
  async init() {
    try {
      console.log('Initializing AudioManager...');
      
      // 加载设置
      this.loadSettings();
      
      // 预加载背景音乐
      await this.preloadBackgroundMusic();
      
      this.isInitialized = true;
      console.log('AudioManager initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize AudioManager:', error);
      throw error;
    }
  }

  // 加载设置
  loadSettings() {
    const savedSettings = Utils.Storage.get(GameConfig.STORAGE_KEYS.SETTINGS, GameConfig.DEFAULT_SETTINGS);
    this.settings = {
      backgroundVolume: savedSettings.backgroundVolume / 100,
      effectVolume: savedSettings.effectVolume / 100,
      isMuted: savedSettings.isMuted
    };
    
    console.log('Audio settings loaded:', this.settings);
  }

  // 预加载背景音乐
  async preloadBackgroundMusic() {
    try {
      const bgMusic = tt.createInnerAudioContext();
      bgMusic.src = GameConfig.AUDIO.BACKGROUND;
      bgMusic.loop = true;
      bgMusic.volume = this.settings.isMuted ? 0 : this.settings.backgroundVolume;
      
      // 等待音频可以播放
      await new Promise((resolve, reject) => {
        bgMusic.onCanplay(() => {
          console.log('Background music loaded');
          resolve();
        });
        
        bgMusic.onError((error) => {
          console.error('Failed to load background music:', error);
          reject(error);
        });
        
        // 超时处理
        setTimeout(() => {
          reject(new Error('Background music load timeout'));
        }, 5000);
      });
      
      this.backgroundMusic = bgMusic;
      
    } catch (error) {
      console.warn('Background music not available:', error);
      // 继续初始化，不阻塞游戏
    }
  }

  // 播放背景音乐
  playBackgroundMusic() {
    if (!this.backgroundMusic || this.settings.isMuted) {
      return;
    }
    
    try {
      this.backgroundMusic.volume = this.settings.backgroundVolume;
      this.backgroundMusic.play();
      console.log('Background music started');
    } catch (error) {
      console.error('Failed to play background music:', error);
    }
  }

  // 停止背景音乐
  stopBackgroundMusic() {
    if (this.backgroundMusic) {
      try {
        this.backgroundMusic.stop();
        console.log('Background music stopped');
      } catch (error) {
        console.error('Failed to stop background music:', error);
      }
    }
  }

  // 暂停背景音乐
  pauseBackgroundMusic() {
    if (this.backgroundMusic) {
      try {
        this.backgroundMusic.pause();
        console.log('Background music paused');
      } catch (error) {
        console.error('Failed to pause background music:', error);
      }
    }
  }

  // 恢复背景音乐
  resumeBackgroundMusic() {
    if (this.backgroundMusic && !this.settings.isMuted) {
      try {
        this.backgroundMusic.play();
        console.log('Background music resumed');
      } catch (error) {
        console.error('Failed to resume background music:', error);
      }
    }
  }

  // 播放音效
  playEffect(src, options = {}) {
    if (this.settings.isMuted) {
      return null;
    }

    // 检查并发音效数量
    if (this.currentEffects.length >= this.maxConcurrentEffects) {
      console.warn('Too many concurrent effects, skipping:', src);
      return null;
    }

    try {
      const audio = tt.createInnerAudioContext();
      audio.src = src;
      audio.volume = (options.volume !== undefined ? options.volume : this.settings.effectVolume);
      audio.loop = options.loop || false;
      
      // 添加到当前音效列表
      this.currentEffects.push(audio);
      
      // 播放完成后清理
      audio.onEnded(() => {
        this.removeFromCurrentEffects(audio);
        audio.destroy();
      });
      
      // 错误处理
      audio.onError((error) => {
        console.error(`Failed to play effect: ${src}`, error);
        this.removeFromCurrentEffects(audio);
        audio.destroy();
      });
      
      // 播放音效
      audio.play();
      
      console.log(`Playing effect: ${src}`);
      return audio;
      
    } catch (error) {
      console.error(`Failed to create effect: ${src}`, error);
      return null;
    }
  }

  // 从当前音效列表中移除
  removeFromCurrentEffects(audio) {
    const index = this.currentEffects.indexOf(audio);
    if (index > -1) {
      this.currentEffects.splice(index, 1);
    }
  }

  // 播放游戏音效（带防重复播放）
  playGameEffect(effectName, options = {}) {
    const src = GameConfig.AUDIO.EFFECTS[effectName];
    if (!src) {
      console.warn(`Effect not found: ${effectName}`);
      return null;
    }

    // 防止短时间内重复播放同一音效
    const now = Date.now();
    const lastPlayTime = this.audioInstances.get(effectName) || 0;
    const minInterval = options.minInterval || 100; // 最小间隔100ms

    if (now - lastPlayTime < minInterval) {
      return null;
    }

    this.audioInstances.set(effectName, now);
    return this.playEffect(src, options);
  }

  // 播放匹配音效
  playMatchEffect(matchCount) {
    let effectName;
    
    switch (matchCount) {
      case 3:
        effectName = 'COMBO_3';
        break;
      case 4:
        effectName = 'NORMAL_MATCH';
        break;
      case 5:
      default:
        effectName = 'COMBO_5';
        break;
    }
    
    return this.playGameEffect(effectName);
  }

  // 播放连击音效
  playComboEffect(comboCount) {
    if (comboCount >= 5) {
      return this.playGameEffect('COMBO_5');
    } else if (comboCount >= 3) {
      return this.playGameEffect('COMBO_3');
    }
    return null;
  }

  // 播放道具音效
  playPropEffect(propType) {
    switch (propType) {
      case 'bomb':
        return this.playGameEffect('BOMB');
      case 'rocket':
        return this.playGameEffect('ROCKET');
      default:
        return this.playGameEffect('NORMAL_MATCH');
    }
  }

  // 播放UI音效
  playUIEffect() {
    return this.playGameEffect('CAT', { volume: 0.5 });
  }

  // 播放胜利音效
  playWinEffect() {
    return this.playGameEffect('WIN');
  }

  // 播放失败音效
  playLoseEffect() {
    return this.playGameEffect('LOSE');
  }

  // 更新设置
  updateSettings(newSettings) {
    const oldMuted = this.settings.isMuted;
    
    this.settings = {
      backgroundVolume: newSettings.backgroundVolume / 100,
      effectVolume: newSettings.effectVolume / 100,
      isMuted: newSettings.isMuted
    };
    
    // 更新背景音乐音量
    if (this.backgroundMusic) {
      if (this.settings.isMuted) {
        this.pauseBackgroundMusic();
      } else {
        this.backgroundMusic.volume = this.settings.backgroundVolume;
        if (oldMuted && !this.settings.isMuted) {
          this.resumeBackgroundMusic();
        }
      }
    }
    
    console.log('Audio settings updated:', this.settings);
  }

  // 设置静音
  setMuted(muted) {
    this.settings.isMuted = muted;
    
    if (muted) {
      this.pauseBackgroundMusic();
      // 停止所有当前音效
      this.stopAllEffects();
    } else {
      this.resumeBackgroundMusic();
    }
  }

  // 停止所有音效
  stopAllEffects() {
    for (const audio of this.currentEffects) {
      try {
        audio.stop();
        audio.destroy();
      } catch (error) {
        console.error('Failed to stop effect:', error);
      }
    }
    this.currentEffects = [];
  }

  // 设置背景音量
  setBackgroundVolume(volume) {
    this.settings.backgroundVolume = volume;
    if (this.backgroundMusic && !this.settings.isMuted) {
      this.backgroundMusic.volume = volume;
    }
  }

  // 设置音效音量
  setEffectVolume(volume) {
    this.settings.effectVolume = volume;
  }

  // 获取音效统计信息
  getStats() {
    return {
      currentEffects: this.currentEffects.length,
      maxConcurrentEffects: this.maxConcurrentEffects,
      backgroundMusicLoaded: !!this.backgroundMusic,
      settings: { ...this.settings }
    };
  }

  // 销毁音效管理器
  destroy() {
    console.log('Destroying AudioManager...');
    
    // 停止背景音乐
    this.stopBackgroundMusic();
    if (this.backgroundMusic) {
      this.backgroundMusic.destroy();
      this.backgroundMusic = null;
    }
    
    // 停止所有音效
    this.stopAllEffects();
    
    // 清理数据
    this.audioInstances.clear();
    this.effectQueue = [];
    this.isInitialized = false;
    
    console.log('AudioManager destroyed');
  }
}

// 导出类到全局作用域
if (typeof global !== 'undefined') {
  global.AudioManager = AudioManager;
}
if (typeof window !== 'undefined') {
  window.AudioManager = AudioManager;
}
// 抖音小游戏环境
if (typeof tt !== 'undefined') {
  this.AudioManager = AudioManager;
}
