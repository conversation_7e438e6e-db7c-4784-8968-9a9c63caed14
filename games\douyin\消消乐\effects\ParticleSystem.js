// 粒子系统类
var ParticleSystem = class ParticleSystem {
  constructor() {
    this.particles = [];
    this.particlePool = [];
    this.maxParticles = GameConfig.PERFORMANCE.MAX_PARTICLES;
    this.isEnabled = true;
    
    // 预创建粒子池
    this.initParticlePool();
  }

  // 初始化粒子池
  initParticlePool() {
    for (let i = 0; i < GameConfig.PERFORMANCE.PARTICLE_POOL_SIZE; i++) {
      this.particlePool.push(this.createParticle());
    }
    console.log(`Particle pool initialized with ${this.particlePool.length} particles`);
  }

  // 创建粒子对象
  createParticle() {
    return {
      x: 0,
      y: 0,
      vx: 0,
      vy: 0,
      life: 0,
      maxLife: 0,
      size: 0,
      color: '#FFFFFF',
      alpha: 1,
      rotation: 0,
      rotationSpeed: 0,
      type: 'default',
      isActive: false,
      gravity: 0,
      friction: 1,
      scale: 1,
      scaleSpeed: 0
    };
  }

  // 从池中获取粒子
  getParticleFromPool() {
    if (this.particlePool.length > 0) {
      return this.particlePool.pop();
    }
    return this.createParticle();
  }

  // 将粒子返回池中
  returnParticleToPool(particle) {
    particle.isActive = false;
    if (this.particlePool.length < GameConfig.PERFORMANCE.PARTICLE_POOL_SIZE) {
      this.particlePool.push(particle);
    }
  }

  // 发射粒子
  emit(config) {
    if (!this.isEnabled || this.particles.length >= this.maxParticles) {
      return;
    }

    const particle = this.getParticleFromPool();
    
    // 设置粒子属性
    particle.x = config.x || 0;
    particle.y = config.y || 0;
    particle.vx = config.vx || Utils.Math.randomFloat(-50, 50);
    particle.vy = config.vy || Utils.Math.randomFloat(-100, -50);
    particle.life = config.life || Utils.Math.randomFloat(1000, 3000);
    particle.maxLife = particle.life;
    particle.size = config.size || Utils.Math.randomFloat(4, 12);
    particle.color = config.color || Utils.Array.randomElement(['#FF6B8B', '#4ECDC4', '#45B7D1', '#96CEB4']);
    particle.alpha = 1;
    particle.rotation = config.rotation || Utils.Math.randomFloat(0, Math.PI * 2);
    particle.rotationSpeed = config.rotationSpeed || Utils.Math.randomFloat(-0.01, 0.01);
    particle.type = config.type || 'default';
    particle.gravity = config.gravity || 50;
    particle.friction = config.friction || 0.98;
    particle.scale = 1;
    particle.scaleSpeed = config.scaleSpeed || 0;
    particle.isActive = true;

    this.particles.push(particle);
  }

  // 批量发射粒子
  emitBurst(config) {
    const count = config.count || 10;
    for (let i = 0; i < count; i++) {
      const particleConfig = { ...config };
      
      // 添加随机偏移
      if (config.spread) {
        particleConfig.x += Utils.Math.randomFloat(-config.spread, config.spread);
        particleConfig.y += Utils.Math.randomFloat(-config.spread, config.spread);
      }
      
      this.emit(particleConfig);
    }
  }

  // 创建爱心粒子效果
  createHeartEffect(x, y, count = 5) {
    this.emitBurst({
      x: x,
      y: y,
      count: count,
      type: 'heart',
      life: Utils.Math.randomFloat(2000, 4000),
      size: Utils.Math.randomFloat(8, 16),
      vx: Utils.Math.randomFloat(-30, 30),
      vy: Utils.Math.randomFloat(-80, -40),
      gravity: 30,
      spread: 20
    });
  }

  // 创建爆炸效果
  createExplosionEffect(x, y, count = 15) {
    this.emitBurst({
      x: x,
      y: y,
      count: count,
      type: 'explosion',
      life: Utils.Math.randomFloat(500, 1500),
      size: Utils.Math.randomFloat(6, 14),
      vx: Utils.Math.randomFloat(-100, 100),
      vy: Utils.Math.randomFloat(-100, 100),
      gravity: 0,
      friction: 0.95,
      scaleSpeed: -0.002,
      spread: 10
    });
  }

  // 创建连击效果
  createComboEffect(x, y, combo) {
    const count = Math.min(combo * 2, 20);
    const colors = combo >= 5 ? ['#FF0000', '#FF4444'] : ['#00FF00', '#44FF44'];
    
    this.emitBurst({
      x: x,
      y: y,
      count: count,
      type: 'combo',
      life: Utils.Math.randomFloat(1000, 2000),
      size: Utils.Math.randomFloat(4, 8),
      color: Utils.Array.randomElement(colors),
      vx: Utils.Math.randomFloat(-60, 60),
      vy: Utils.Math.randomFloat(-120, -60),
      gravity: 40,
      spread: 30
    });
  }

  // 创建星星效果
  createStarEffect(x, y, count = 8) {
    this.emitBurst({
      x: x,
      y: y,
      count: count,
      type: 'star',
      life: Utils.Math.randomFloat(1500, 2500),
      size: Utils.Math.randomFloat(6, 12),
      color: '#FFFF00',
      vx: Utils.Math.randomFloat(-40, 40),
      vy: Utils.Math.randomFloat(-60, -20),
      gravity: 20,
      spread: 15
    });
  }

  // 更新粒子系统
  update(deltaTime) {
    if (!this.isEnabled) return;

    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];
      
      // 更新位置
      particle.x += particle.vx * deltaTime / 1000;
      particle.y += particle.vy * deltaTime / 1000;
      
      // 应用重力
      particle.vy += particle.gravity * deltaTime / 1000;
      
      // 应用摩擦力
      particle.vx *= particle.friction;
      particle.vy *= particle.friction;
      
      // 更新旋转
      particle.rotation += particle.rotationSpeed * deltaTime;
      
      // 更新缩放
      particle.scale += particle.scaleSpeed * deltaTime;
      particle.scale = Math.max(0, particle.scale);
      
      // 更新生命周期
      particle.life -= deltaTime;
      particle.alpha = Math.max(0, particle.life / particle.maxLife);
      
      // 移除死亡粒子
      if (particle.life <= 0 || particle.scale <= 0) {
        this.particles.splice(i, 1);
        this.returnParticleToPool(particle);
      }
    }
  }

  // 渲染粒子系统
  render(ctx) {
    if (!this.isEnabled || this.particles.length === 0) return;

    ctx.save();
    
    for (const particle of this.particles) {
      if (!particle.isActive) continue;
      
      ctx.save();
      ctx.globalAlpha = particle.alpha;
      ctx.translate(particle.x, particle.y);
      ctx.rotate(particle.rotation);
      ctx.scale(particle.scale, particle.scale);
      
      // 根据粒子类型渲染
      switch (particle.type) {
        case 'heart':
          this.renderHeart(ctx, particle);
          break;
        case 'star':
          this.renderStar(ctx, particle);
          break;
        case 'explosion':
        case 'combo':
        default:
          this.renderCircle(ctx, particle);
          break;
      }
      
      ctx.restore();
    }
    
    ctx.restore();
  }

  // 渲染圆形粒子
  renderCircle(ctx, particle) {
    ctx.fillStyle = particle.color;
    ctx.beginPath();
    ctx.arc(0, 0, particle.size / 2, 0, Math.PI * 2);
    ctx.fill();
  }

  // 渲染爱心粒子
  renderHeart(ctx, particle) {
    const size = particle.size / 20;
    ctx.fillStyle = particle.color;
    ctx.scale(size, size);
    
    ctx.beginPath();
    ctx.moveTo(0, -8);
    ctx.bezierCurveTo(-12, -18, -25, -8, -12, 2);
    ctx.bezierCurveTo(-12, 2, 0, 12, 0, 12);
    ctx.bezierCurveTo(0, 12, 12, 2, 12, 2);
    ctx.bezierCurveTo(25, -8, 12, -18, 0, -8);
    ctx.closePath();
    ctx.fill();
  }

  // 渲染星星粒子
  renderStar(ctx, particle) {
    const size = particle.size / 2;
    ctx.fillStyle = particle.color;
    
    ctx.beginPath();
    for (let i = 0; i < 5; i++) {
      const angle = (i * Math.PI * 2) / 5 - Math.PI / 2;
      const x = Math.cos(angle) * size;
      const y = Math.sin(angle) * size;
      
      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
      
      // 内角
      const innerAngle = angle + Math.PI / 5;
      const innerX = Math.cos(innerAngle) * size * 0.4;
      const innerY = Math.sin(innerAngle) * size * 0.4;
      ctx.lineTo(innerX, innerY);
    }
    ctx.closePath();
    ctx.fill();
  }

  // 清空所有粒子
  clear() {
    for (const particle of this.particles) {
      this.returnParticleToPool(particle);
    }
    this.particles = [];
  }

  // 设置启用状态
  setEnabled(enabled) {
    this.isEnabled = enabled;
    if (!enabled) {
      this.clear();
    }
  }

  // 设置最大粒子数
  setMaxParticles(max) {
    this.maxParticles = max;
    
    // 如果当前粒子数超过限制，移除多余的
    while (this.particles.length > this.maxParticles) {
      const particle = this.particles.pop();
      this.returnParticleToPool(particle);
    }
  }

  // 获取粒子统计信息
  getStats() {
    return {
      activeParticles: this.particles.length,
      poolSize: this.particlePool.length,
      maxParticles: this.maxParticles,
      isEnabled: this.isEnabled
    };
  }

  // 销毁粒子系统
  destroy() {
    this.clear();
    this.particlePool = [];
    this.isEnabled = false;
    console.log('ParticleSystem destroyed');
  }
}

// 导出类到全局作用域
if (typeof global !== 'undefined') {
  global.ParticleSystem = ParticleSystem;
}
if (typeof window !== 'undefined') {
  window.ParticleSystem = ParticleSystem;
}
// 抖音小游戏环境
if (typeof tt !== 'undefined') {
  this.ParticleSystem = ParticleSystem;
}
