// 最终启动脚本 - 确保游戏能够成功启动
console.log('=== 最终启动脚本开始 ===');

// 延迟执行，确保所有脚本都已加载
setTimeout(function() {
  console.log('开始最终启动检查...');
  
  // 检查核心组件
  var coreComponents = {
    tt: typeof tt !== 'undefined',
    GameConfig: typeof GameConfig !== 'undefined',
    Utils: typeof Utils !== 'undefined',
    GameEngine: typeof GameEngine !== 'undefined',
    PageManager: typeof PageManager !== 'undefined',
    HomePage: typeof HomePage !== 'undefined'
  };
  
  console.log('核心组件检查结果:');
  var allReady = true;
  for (var component in coreComponents) {
    var status = coreComponents[component] ? '✅' : '❌';
    console.log('  ' + component + ': ' + status);
    if (!coreComponents[component]) {
      allReady = false;
    }
  }
  
  if (allReady) {
    console.log('🎉 所有核心组件就绪，启动游戏！');
    
    // 验证配置
    try {
      console.log('配置验证:');
      console.log('  游戏版本:', GameConfig.GAME.VERSION);
      console.log('  网格尺寸:', GameConfig.GRID.ROWS + 'x' + GameConfig.GRID.COLS);
      
      // 测试工具类
      var testNum = Utils.Math.randomInt(1, 10);
      console.log('  工具类测试:', testNum);
      
      // 获取系统信息
      var systemInfo = Utils.Device.getSystemInfo();
      console.log('  屏幕尺寸:', systemInfo.windowWidth + 'x' + systemInfo.windowHeight);
      
    } catch (e) {
      console.error('配置验证失败:', e);
    }
    
    // 启动游戏
    if (typeof initGame === 'function') {
      console.log('调用游戏初始化函数...');
      initGame().catch(function(error) {
        console.error('游戏初始化失败:', error);
        
        // 尝试手动创建游戏实例
        console.log('尝试手动创建游戏实例...');
        try {
          var gameInstance = new Game();
          gameInstance.init().then(function() {
            console.log('✅ 手动游戏实例创建成功');
          }).catch(function(e) {
            console.error('❌ 手动游戏实例创建失败:', e);
          });
        } catch (e) {
          console.error('❌ 无法创建游戏实例:', e);
        }
      });
    } else {
      console.error('❌ initGame 函数不存在');
    }
    
  } else {
    console.error('❌ 部分核心组件未就绪，无法启动游戏');
    
    // 尝试修复缺失的组件
    console.log('尝试修复缺失的组件...');
    
    // 获取全局对象
    function getGlobalObject() {
      try {
        return globalThis;
      } catch (e) {
        try {
          return window;
        } catch (e) {
          try {
            return global;
          } catch (e) {
            try {
              return Function('return this')();
            } catch (e) {
              return {};
            }
          }
        }
      }
    }
    
    var globalObj = getGlobalObject();
    
    // 尝试从全局对象获取缺失的组件
    if (!coreComponents.GameConfig && globalObj.GameConfig) {
      GameConfig = globalObj.GameConfig;
      console.log('✅ 修复了 GameConfig');
    }
    
    if (!coreComponents.Utils && globalObj.Utils) {
      Utils = globalObj.Utils;
      console.log('✅ 修复了 Utils');
    }
    
    if (!coreComponents.GameEngine && globalObj.GameEngine) {
      GameEngine = globalObj.GameEngine;
      console.log('✅ 修复了 GameEngine');
    }
    
    // 如果还是缺少 GameConfig，创建最小配置
    if (typeof GameConfig === 'undefined') {
      console.log('创建最小 GameConfig...');
      GameConfig = {
        GAME: { VERSION: 'v1.0.0', TITLE: '萌宠爱消消', TARGET_FPS: 50 },
        GRID: { ROWS: 10, COLS: 8, CELL_SIZE: 64, PADDING: 10 },
        SCREEN: { DESIGN_WIDTH: 750, DESIGN_HEIGHT: 1334, SAFE_AREA_TOP: 0.15, SAFE_AREA_BOTTOM: 0.1 },
        PERFORMANCE: { GC_INTERVAL: 30000 },
        COLORS: { PRIMARY: '#FFEFD5', SECONDARY: '#FF6B8B', TEXT_PRIMARY: '#333333' },
        FONTS: { TITLE: '48px Arial', BUTTON: '18px Arial' }
      };
      console.log('✅ 最小 GameConfig 创建完成');
    }
    
    // 重新检查
    setTimeout(function() {
      var recheck = {
        GameConfig: typeof GameConfig !== 'undefined',
        Utils: typeof Utils !== 'undefined',
        GameEngine: typeof GameEngine !== 'undefined'
      };
      
      var recheckReady = true;
      for (var component in recheck) {
        if (!recheck[component]) {
          recheckReady = false;
        }
      }
      
      if (recheckReady) {
        console.log('✅ 修复成功，重新尝试启动游戏');
        if (typeof initGame === 'function') {
          initGame();
        }
      } else {
        console.error('❌ 修复失败，无法启动游戏');
      }
    }, 100);
  }
  
}, 500);

// 创建手动启动函数
function manualStartGame() {
  console.log('手动启动游戏...');
  
  // 确保基本组件可用
  if (typeof GameConfig === 'undefined') {
    console.log('创建基本 GameConfig...');
    GameConfig = {
      GAME: { VERSION: 'v1.0.0', TARGET_FPS: 50 },
      GRID: { ROWS: 10, COLS: 8 },
      PERFORMANCE: { GC_INTERVAL: 30000 }
    };
  }
  
  if (typeof Game !== 'undefined') {
    try {
      var gameInstance = new Game();
      gameInstance.init().then(function() {
        console.log('✅ 手动启动成功');
      }).catch(function(e) {
        console.error('❌ 手动启动失败:', e);
      });
    } catch (e) {
      console.error('❌ 无法创建游戏实例:', e);
    }
  } else {
    console.error('❌ Game 类不可用');
  }
}

// 导出手动启动函数
try {
  var globalObj = (function() {
    try {
      return globalThis;
    } catch (e) {
      try {
        return window;
      } catch (e) {
        try {
          return global;
        } catch (e) {
          return {};
        }
      }
    }
  })();
  
  if (globalObj) {
    globalObj.manualStartGame = manualStartGame;
  }
  manualStartGame = manualStartGame;
} catch (e) {
  console.log('无法导出手动启动函数:', e);
}

console.log('=== 最终启动脚本加载完成 ===');
