// 最终测试 - 验证所有组件加载和游戏启动
console.log('=== 最终测试开始 ===');

// 立即检查基础环境
console.log('1. 基础环境检查:');
console.log('   抖音API:', typeof tt !== 'undefined' ? '✅' : '❌');
console.log('   Canvas API:', typeof tt !== 'undefined' && typeof tt.createCanvas === 'function' ? '✅' : '❌');

// 延迟检查脚本加载
setTimeout(function() {
  console.log('\n2. 配置文件检查:');
  
  // 检查 GameConfig
  var configLoaded = false;
  var configSource = '';
  
  if (typeof GameConfig !== 'undefined') {
    configLoaded = true;
    configSource = 'direct';
  } else if (typeof this.GameConfig !== 'undefined') {
    configLoaded = true;
    configSource = 'this';
    GameConfig = this.GameConfig; // 复制到直接引用
  } else if (typeof window !== 'undefined' && typeof window.GameConfig !== 'undefined') {
    configLoaded = true;
    configSource = 'window';
    GameConfig = window.GameConfig;
  }
  
  if (configLoaded) {
    console.log('   ✅ GameConfig 已加载 (来源: ' + configSource + ')');
    console.log('      版本:', GameConfig.GAME.VERSION);
    console.log('      网格:', GameConfig.GRID.ROWS + 'x' + GameConfig.GRID.COLS);
  } else {
    console.log('   ❌ GameConfig 未加载');
  }
  
  // 检查 Utils
  var utilsLoaded = false;
  if (typeof Utils !== 'undefined') {
    utilsLoaded = true;
  } else if (typeof this.Utils !== 'undefined') {
    utilsLoaded = true;
    Utils = this.Utils;
  }
  
  if (utilsLoaded) {
    console.log('   ✅ Utils 已加载');
    try {
      var testNum = Utils.Math.randomInt(1, 5);
      console.log('      随机数测试:', testNum);
    } catch (e) {
      console.log('      工具测试失败:', e.message);
    }
  } else {
    console.log('   ❌ Utils 未加载');
  }
  
  console.log('\n3. 核心类检查:');
  
  // 检查核心类
  var coreClasses = {
    GameEngine: typeof GameEngine !== 'undefined',
    PageManager: typeof PageManager !== 'undefined',
    ResourceManager: typeof ResourceManager !== 'undefined'
  };
  
  for (var className in coreClasses) {
    if (coreClasses[className]) {
      console.log('   ✅ ' + className + ' 已加载');
    } else {
      console.log('   ❌ ' + className + ' 未加载');
    }
  }
  
  console.log('\n4. 页面类检查:');
  
  var pageClasses = {
    HomePage: typeof HomePage !== 'undefined',
    GamePage: typeof GamePage !== 'undefined',
    RankPage: typeof RankPage !== 'undefined',
    SettingPage: typeof SettingPage !== 'undefined'
  };
  
  for (var className in pageClasses) {
    if (pageClasses[className]) {
      console.log('   ✅ ' + className + ' 已加载');
    } else {
      console.log('   ❌ ' + className + ' 未加载');
    }
  }
  
  console.log('\n5. 特效类检查:');
  
  var effectClasses = {
    ParticleSystem: typeof ParticleSystem !== 'undefined',
    AudioManager: typeof AudioManager !== 'undefined'
  };
  
  for (var className in effectClasses) {
    if (effectClasses[className]) {
      console.log('   ✅ ' + className + ' 已加载');
    } else {
      console.log('   ❌ ' + className + ' 未加载');
    }
  }
  
  // 统计加载情况
  var totalClasses = 0;
  var loadedClasses = 0;
  
  var allClasses = {};
  for (var key in coreClasses) allClasses[key] = coreClasses[key];
  for (var key in pageClasses) allClasses[key] = pageClasses[key];
  for (var key in effectClasses) allClasses[key] = effectClasses[key];
  
  for (var className in allClasses) {
    totalClasses++;
    if (allClasses[className]) loadedClasses++;
  }
  
  console.log('\n6. 加载统计:');
  console.log('   配置文件:', configLoaded ? '✅' : '❌');
  console.log('   工具类:', utilsLoaded ? '✅' : '❌');
  console.log('   游戏类:', loadedClasses + '/' + totalClasses);
  
  var readyToStart = configLoaded && utilsLoaded && loadedClasses >= totalClasses * 0.8;
  
  if (readyToStart) {
    console.log('\n🎉 所有组件加载完成，游戏可以启动！');
  } else {
    console.log('\n⚠️ 部分组件未加载，可能影响游戏运行');
  }
  
  console.log('\n=== 最终测试完成 ===');
  
}, 100);

// 创建手动测试函数
if (typeof this !== 'undefined') {
  this.runFinalTest = function() {
    console.log('手动运行最终测试...');
    
    // 立即检查当前状态
    var status = {
      tt: typeof tt !== 'undefined',
      GameConfig: typeof GameConfig !== 'undefined',
      Utils: typeof Utils !== 'undefined',
      GameEngine: typeof GameEngine !== 'undefined',
      PageManager: typeof PageManager !== 'undefined',
      HomePage: typeof HomePage !== 'undefined'
    };
    
    console.log('当前状态:', status);
    
    // 尝试修复缺失的引用
    if (!status.GameConfig && typeof this.GameConfig !== 'undefined') {
      GameConfig = this.GameConfig;
      console.log('✅ 修复了 GameConfig 引用');
    }
    
    if (!status.Utils && typeof this.Utils !== 'undefined') {
      Utils = this.Utils;
      console.log('✅ 修复了 Utils 引用');
    }
    
    // 重新检查
    var fixedStatus = {
      GameConfig: typeof GameConfig !== 'undefined',
      Utils: typeof Utils !== 'undefined'
    };
    
    console.log('修复后状态:', fixedStatus);
    
    return status;
  };
  
  // 创建强制启动函数
  this.forceStartGame = function() {
    console.log('强制启动游戏...');
    
    // 确保基本配置可用
    if (typeof GameConfig === 'undefined') {
      if (typeof this.GameConfig !== 'undefined') {
        GameConfig = this.GameConfig;
      } else {
        console.log('创建最小配置...');
        GameConfig = {
          GAME: { VERSION: 'v1.0.0', TARGET_FPS: 50 },
          GRID: { ROWS: 10, COLS: 8 },
          PERFORMANCE: { GC_INTERVAL: 30000 }
        };
      }
    }
    
    // 尝试启动游戏
    if (typeof initGame === 'function') {
      initGame();
    } else {
      console.log('❌ initGame 函数不可用');
    }
  };
}
