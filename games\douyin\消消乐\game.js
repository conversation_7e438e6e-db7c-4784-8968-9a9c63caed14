// 萌宠爱消消 - 抖音小游戏
console.log('萌宠爱消消游戏启动中...');
console.log('开发文档: https://developer.open-douyin.com/docs/resource/zh-CN/mini-game/guide/minigame/introduction');

// 所有脚本文件已通过 game.json 的 scripts 配置按顺序加载
// 在抖音小游戏环境中，脚本会自动按照 game.json 中的顺序加载到全局作用域

// 全局游戏实例
let gameInstance = null;

// 主游戏类
var Game = class Game {
  constructor() {
    this.gameEngine = null;
    this.pageManager = null;
    this.isInitialized = false;
    this.performanceMonitor = null;

    console.log('Game instance created');
  }

  // 初始化游戏
  async init() {
    try {
      console.log('Initializing game...');

      // 初始化性能监控
      this.initPerformanceMonitor();

      // 创建游戏引擎
      this.gameEngine = new GameEngine();

      // 创建页面管理器
      this.pageManager = new PageManager(this.gameEngine);

      // 注册页面类
      this.registerPages();

      // 设置引擎事件处理
      this.setupEngineEvents();

      // 启动游戏循环
      this.gameEngine.start();

      // 导航到主页
      await this.pageManager.navigateTo('home');

      this.isInitialized = true;
      console.log('Game initialized successfully');

    } catch (error) {
      console.error('Failed to initialize game:', error);
      this.showErrorMessage('游戏初始化失败，请重新启动');
    }
  }

  // 初始化性能监控
  initPerformanceMonitor() {
    // 检查 GameConfig 是否可用
    if (typeof GameConfig === 'undefined') {
      console.error('GameConfig is not available in initPerformanceMonitor');
      // 使用默认值
      const defaultGCInterval = 30000;

      this.performanceMonitor = {
        lastGCTime: Date.now(),
        frameCount: 0,
        lastFPSCheck: Date.now(),
        currentFPS: 0
      };

      // 定期垃圾回收
      setInterval(() => {
        this.performGarbageCollection();
      }, defaultGCInterval);

      return;
    }

    this.performanceMonitor = {
      lastGCTime: Date.now(),
      frameCount: 0,
      lastFPSCheck: Date.now(),
      currentFPS: 0
    };

    // 定期垃圾回收
    setInterval(() => {
      this.performGarbageCollection();
    }, GameConfig.PERFORMANCE.GC_INTERVAL);
  }

  // 注册页面类
  registerPages() {
    this.pageManager.setPageClass('home', HomePage);
    this.pageManager.setPageClass('game', GamePage);
    this.pageManager.setPageClass('rank', RankPage);
    this.pageManager.setPageClass('setting', SettingPage);

    console.log('Pages registered');
  }

  // 设置引擎事件处理
  setupEngineEvents() {
    // 重写引擎的更新和渲染方法
    this.gameEngine.update = (deltaTime) => {
      this.update(deltaTime);
    };

    this.gameEngine.renderContent = () => {
      this.render();
    };

    // 重写输入事件处理
    this.gameEngine.onInputStart = (x, y) => {
      this.pageManager.onInputStart(x, y);
    };

    this.gameEngine.onInputMove = (x, y) => {
      this.pageManager.onInputMove(x, y);
    };

    this.gameEngine.onInputEnd = () => {
      this.pageManager.onInputEnd();
    };
  }

  // 更新游戏逻辑
  update(deltaTime) {
    if (!this.isInitialized) return;

    // 更新性能监控
    this.updatePerformanceMonitor(deltaTime);

    // 更新页面管理器
    this.pageManager.update(deltaTime);

    // 检查内存使用
    this.checkMemoryUsage();
  }

  // 渲染游戏
  render() {
    if (!this.isInitialized) return;

    // 渲染页面管理器
    this.pageManager.render();

    // 渲染性能信息（调试模式）
    if (this.gameEngine.showDebugInfo) {
      this.renderPerformanceInfo();
    }
  }

  // 更新性能监控
  updatePerformanceMonitor(deltaTime) {
    this.performanceMonitor.frameCount++;

    const now = Date.now();
    if (now - this.performanceMonitor.lastFPSCheck >= 1000) {
      this.performanceMonitor.currentFPS = this.performanceMonitor.frameCount;
      this.performanceMonitor.frameCount = 0;
      this.performanceMonitor.lastFPSCheck = now;

      // 检查FPS是否低于目标
      if (this.performanceMonitor.currentFPS < GameConfig.GAME.TARGET_FPS * 0.8) {
        console.warn(`Low FPS detected: ${this.performanceMonitor.currentFPS}`);
        this.optimizePerformance();
      }
    }
  }

  // 检查内存使用
  checkMemoryUsage() {
    // 简单的内存检查（基于时间间隔）
    const now = Date.now();
    if (now - this.performanceMonitor.lastGCTime > GameConfig.PERFORMANCE.GC_INTERVAL) {
      this.performGarbageCollection();
      this.performanceMonitor.lastGCTime = now;
    }
  }

  // 执行垃圾回收
  performGarbageCollection() {
    if (typeof tt.triggerGC === 'function') {
      tt.triggerGC();
      console.log('Garbage collection triggered');
    }
  }

  // 优化性能
  optimizePerformance() {
    console.log('Optimizing performance...');

    // 减少粒子数量
    if (GameConfig.PERFORMANCE.MAX_PARTICLES > 50) {
      GameConfig.PERFORMANCE.MAX_PARTICLES = Math.max(50, GameConfig.PERFORMANCE.MAX_PARTICLES * 0.8);
    }

    // 降低动画质量
    if (GameConfig.ANIMATION.PARTICLE_SPAWN_RATE > 2) {
      GameConfig.ANIMATION.PARTICLE_SPAWN_RATE = Math.max(2, GameConfig.ANIMATION.PARTICLE_SPAWN_RATE * 0.8);
    }
  }

  // 渲染性能信息
  renderPerformanceInfo() {
    const ctx = this.gameEngine.ctx;
    const { width } = this.gameEngine.getCanvasSize();

    ctx.save();
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(width - 150, 10, 140, 80);

    ctx.fillStyle = '#FFFFFF';
    ctx.font = '12px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(`FPS: ${this.performanceMonitor.currentFPS}`, width - 145, 30);
    ctx.fillText(`Target: ${GameConfig.GAME.TARGET_FPS}`, width - 145, 45);
    ctx.fillText(`Particles: ${GameConfig.PERFORMANCE.MAX_PARTICLES}`, width - 145, 60);
    ctx.fillText(`Memory: ${this.getMemoryUsage()}MB`, width - 145, 75);
    ctx.restore();
  }

  // 获取内存使用情况
  getMemoryUsage() {
    // 简单估算，实际应用中可以使用更精确的方法
    if (typeof performance !== 'undefined' && performance.memory) {
      return Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
    }
    return 'N/A';
  }

  // 显示错误消息
  showErrorMessage(message) {
    const ctx = this.gameEngine ? this.gameEngine.ctx : null;
    if (!ctx) {
      console.error(message);
      return;
    }

    const { width, height } = this.gameEngine.getCanvasSize();

    ctx.save();
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(0, 0, width, height);

    ctx.fillStyle = '#FFFFFF';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(message, width / 2, height / 2);
    ctx.restore();
  }

  // 销毁游戏
  destroy() {
    console.log('Destroying game...');

    if (this.pageManager) {
      this.pageManager.destroy();
      this.pageManager = null;
    }

    if (this.gameEngine) {
      this.gameEngine.destroy();
      this.gameEngine = null;
    }

    this.performanceMonitor = null;
    this.isInitialized = false;

    console.log('Game destroyed');
  }
}

// 检查所有依赖是否已加载
function checkAllDependencies() {
  const requiredClasses = [
    'GameConfig', 'Utils', 'GameEngine', 'PageManager', 'ResourceManager',
    'HomePage', 'GamePage', 'RankPage', 'SettingPage',
    'ParticleSystem', 'AudioManager'
  ];

  const missing = [];
  for (const className of requiredClasses) {
    if (typeof eval(className) === 'undefined') {
      missing.push(className);
    }
  }

  if (missing.length > 0) {
    console.error('Missing dependencies:', missing);
    return false;
  }

  console.log('All dependencies loaded successfully');
  return true;
}

// 异步初始化游戏
async function initGame() {
  try {
    // 检查必要的API
    if (typeof tt === 'undefined') {
      throw new Error('抖音小游戏API不可用');
    }

    console.log('开始初始化游戏...');

    // 等待一小段时间确保所有脚本都已加载
    await new Promise(resolve => setTimeout(resolve, 100));

    // 检查依赖
    if (!checkAllDependencies()) {
      throw new Error('缺少必要的依赖文件，请检查脚本加载顺序');
    }

    // 创建游戏实例
    gameInstance = new Game();

    // 初始化游戏
    await gameInstance.init();

    console.log('游戏初始化完成！');

  } catch (error) {
    console.error('Game initialization failed:', error);
    showErrorScreen(error.message);
  }
}

// 显示错误屏幕
function showErrorScreen(message) {
  try {
    const systemInfo = tt.getSystemInfoSync();
    const canvas = tt.createCanvas();
    const ctx = canvas.getContext('2d');
    canvas.width = systemInfo.windowWidth;
    canvas.height = systemInfo.windowHeight;

    // 绘制错误背景
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#FF6B6B');
    gradient.addColorStop(1, '#FF8E8E');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制错误信息
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('游戏启动失败', canvas.width / 2, canvas.height / 2 - 40);

    ctx.font = '16px Arial';
    ctx.fillText(message, canvas.width / 2, canvas.height / 2);
    ctx.fillText('请重新启动游戏', canvas.width / 2, canvas.height / 2 + 40);

  } catch (err) {
    console.error('Failed to show error screen:', err);
  }
}

// 处理页面隐藏/显示
tt.onHide(() => {
  console.log('Game hidden');
  if (gameInstance && gameInstance.gameEngine) {
    gameInstance.gameEngine.stop();
  }
});

tt.onShow(() => {
  console.log('Game shown');
  if (gameInstance && gameInstance.gameEngine) {
    gameInstance.gameEngine.start();
  }
});

// 处理内存警告
tt.onMemoryWarning(() => {
  console.warn('Memory warning received');
  if (gameInstance) {
    gameInstance.performGarbageCollection();
    gameInstance.optimizePerformance();
  }
});

// 启动游戏
initGame();
