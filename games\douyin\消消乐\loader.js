// 脚本加载器 - 确保所有依赖按正确顺序加载
class ScriptLoader {
  constructor() {
    this.loadedScripts = new Set();
    this.loadingPromises = new Map();
  }

  // 加载单个脚本
  async loadScript(src) {
    if (this.loadedScripts.has(src)) {
      return Promise.resolve();
    }

    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src);
    }

    const promise = new Promise((resolve, reject) => {
      try {
        // 在抖音小游戏环境中，我们需要使用require或直接执行脚本内容
        // 这里我们假设脚本已经通过其他方式加载
        console.log(`Loading script: ${src}`);
        
        // 模拟异步加载
        setTimeout(() => {
          this.loadedScripts.add(src);
          console.log(`Script loaded: ${src}`);
          resolve();
        }, 10);
        
      } catch (error) {
        console.error(`Failed to load script: ${src}`, error);
        reject(error);
      }
    });

    this.loadingPromises.set(src, promise);
    return promise;
  }

  // 批量加载脚本
  async loadScripts(scripts) {
    for (const script of scripts) {
      await this.loadScript(script);
    }
  }

  // 检查脚本是否已加载
  isLoaded(src) {
    return this.loadedScripts.has(src);
  }

  // 检查所有必需的类是否可用
  checkDependencies() {
    const requiredClasses = [
      'GameConfig',
      'Utils', 
      'GameEngine',
      'PageManager',
      'ResourceManager',
      'HomePage',
      'GamePage', 
      'RankPage',
      'SettingPage'
    ];

    const missing = [];
    for (const className of requiredClasses) {
      if (typeof window[className] === 'undefined' && typeof global[className] === 'undefined') {
        missing.push(className);
      }
    }

    if (missing.length > 0) {
      console.error('Missing dependencies:', missing);
      return false;
    }

    console.log('All dependencies loaded successfully');
    return true;
  }
}

// 全局脚本加载器实例
const scriptLoader = new ScriptLoader();

// 导出加载器
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ScriptLoader;
} else if (typeof window !== 'undefined') {
  window.ScriptLoader = ScriptLoader;
  window.scriptLoader = scriptLoader;
}
