// 最小化测试 - 验证基本环境
console.log('=== 最小化测试开始 ===');

// 立即检查基本环境
console.log('抖音API可用:', typeof tt !== 'undefined');
console.log('Canvas API可用:', typeof tt !== 'undefined' && typeof tt.createCanvas === 'function');

// 延迟检查脚本加载
setTimeout(() => {
  console.log('\n--- 脚本加载检查 ---');
  
  // 检查配置
  if (typeof GameConfig !== 'undefined') {
    console.log('✅ GameConfig 已加载');
    try {
      console.log('   版本:', GameConfig.GAME.VERSION);
      console.log('   网格:', GameConfig.GRID.ROWS + 'x' + GameConfig.GRID.COLS);
    } catch (e) {
      console.log('   配置访问错误:', e.message);
    }
  } else {
    console.log('❌ GameConfig 未加载');
  }
  
  // 检查工具类
  if (typeof Utils !== 'undefined') {
    console.log('✅ Utils 已加载');
    try {
      const testNum = Utils.Math.randomInt(1, 5);
      console.log('   随机数测试:', testNum);
    } catch (e) {
      console.log('   工具类访问错误:', e.message);
    }
  } else {
    console.log('❌ Utils 未加载');
  }
  
  // 检查游戏引擎
  if (typeof GameEngine !== 'undefined') {
    console.log('✅ GameEngine 已加载');
  } else {
    console.log('❌ GameEngine 未加载');
  }
  
  // 检查页面管理器
  if (typeof PageManager !== 'undefined') {
    console.log('✅ PageManager 已加载');
  } else {
    console.log('❌ PageManager 未加载');
  }
  
  // 检查主页面
  if (typeof HomePage !== 'undefined') {
    console.log('✅ HomePage 已加载');
  } else {
    console.log('❌ HomePage 未加载');
  }
  
  console.log('\n--- 环境信息 ---');
  if (typeof tt !== 'undefined') {
    try {
      const systemInfo = tt.getSystemInfoSync();
      console.log('设备信息:');
      console.log('   屏幕尺寸:', systemInfo.windowWidth + 'x' + systemInfo.windowHeight);
      console.log('   设备型号:', systemInfo.model);
      console.log('   系统版本:', systemInfo.system);
    } catch (e) {
      console.log('获取系统信息失败:', e.message);
    }
  }
  
  console.log('\n=== 最小化测试完成 ===');
  
}, 100);

// 创建一个简单的画布测试
setTimeout(() => {
  console.log('\n--- 画布测试 ---');
  
  if (typeof tt !== 'undefined' && typeof tt.createCanvas === 'function') {
    try {
      const canvas = tt.createCanvas();
      const ctx = canvas.getContext('2d');
      
      if (canvas && ctx) {
        console.log('✅ 画布创建成功');
        
        // 设置画布尺寸
        const systemInfo = tt.getSystemInfoSync();
        canvas.width = systemInfo.windowWidth;
        canvas.height = systemInfo.windowHeight;
        
        // 绘制测试
        ctx.fillStyle = '#FF6B8B';
        ctx.fillRect(0, 0, 100, 100);
        
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '20px Arial';
        ctx.fillText('测试成功', 20, 50);
        
        console.log('✅ 基本绘制测试通过');
        
        // 如果所有基本组件都加载了，尝试创建游戏实例
        if (typeof GameConfig !== 'undefined' && 
            typeof Utils !== 'undefined' && 
            typeof GameEngine !== 'undefined') {
          
          console.log('\n--- 游戏引擎测试 ---');
          try {
            // 这里不创建完整的游戏实例，只测试基本功能
            console.log('✅ 所有基本组件已加载，可以尝试启动游戏');
          } catch (e) {
            console.log('❌ 游戏引擎测试失败:', e.message);
          }
        } else {
          console.log('❌ 基本组件未完全加载，无法启动游戏');
        }
        
      } else {
        console.log('❌ 画布上下文获取失败');
      }
    } catch (e) {
      console.log('❌ 画布测试失败:', e.message);
    }
  } else {
    console.log('❌ 画布API不可用');
  }
  
}, 300);

// 导出测试函数
if (typeof this !== 'undefined') {
  this.runMinimalTest = () => {
    console.log('手动运行最小化测试...');
    
    const results = {
      tt: typeof tt !== 'undefined',
      GameConfig: typeof GameConfig !== 'undefined',
      Utils: typeof Utils !== 'undefined',
      GameEngine: typeof GameEngine !== 'undefined',
      PageManager: typeof PageManager !== 'undefined',
      HomePage: typeof HomePage !== 'undefined'
    };
    
    console.log('组件加载状态:', results);
    
    const loadedCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`加载进度: ${loadedCount}/${totalCount}`);
    
    if (loadedCount === totalCount) {
      console.log('🎉 所有组件已加载，可以启动游戏！');
    } else {
      console.log('⚠️ 部分组件未加载，请检查脚本配置');
    }
    
    return results;
  };
}
