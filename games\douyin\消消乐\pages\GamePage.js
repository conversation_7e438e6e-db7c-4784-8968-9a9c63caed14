// 游戏页面类
var GamePage = class GamePage {
  constructor(gameEngine, pageManager) {
    this.gameEngine = gameEngine;
    this.pageManager = pageManager;
    this.resourceManager = null;
    
    // 游戏状态
    this.currentLevel = 1;
    this.score = 0;
    this.timeRemaining = 0;
    this.combo = 0;
    this.comboTimer = 0;
    this.isGameRunning = false;
    this.isGameOver = false;
    
    // 网格数据
    this.grid = [];
    this.gridX = 0;
    this.gridY = 0;
    this.cellSize = 0;
    
    // 选择状态
    this.selectedCell = null;
    this.dragStartCell = null;
    this.isDragging = false;
    
    // 动画状态
    this.eliminationAnimations = [];
    this.fallingAnimations = [];
    this.comboDisplay = null;
    
    // 道具状态
    this.selectedProp = null;
    this.props = [];
    
    // UI元素
    this.backButton = null;
    this.propButtons = [];
    
    // 页面状态
    this.isInitialized = false;
  }

  // 初始化页面
  async init(params = {}) {
    console.log('Initializing GamePage');
    
    try {
      // 获取关卡参数
      this.currentLevel = params.level || 1;
      
      // 创建资源管理器
      this.resourceManager = new ResourceManager();
      
      // 加载游戏资源
      await this.loadGameResources();
      
      // 初始化游戏
      this.initGame();
      
      // 初始化UI
      this.initUI();
      
      // 开始游戏
      this.startGame();
      
      this.isInitialized = true;
      console.log('GamePage initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize GamePage:', error);
      throw error;
    }
  }

  // 加载游戏资源
  async loadGameResources() {
    console.log(`Loading resources for level ${this.currentLevel}`);
    
    await Promise.all([
      this.resourceManager.loadAnimalsByLevel(this.currentLevel),
      this.resourceManager.loadPropImages(),
      this.resourceManager.loadGameAudios(),
      this.resourceManager.loadImages([
        'images/icon/current_score.png',
        'images/icon/total_score.png',
        'images/icon/progress.png',
        'images/icon/rebirth.png'
      ])
    ]);
  }

  // 初始化游戏
  initGame() {
    const levelConfig = GameConfig.LEVELS[this.currentLevel - 1];
    if (!levelConfig) {
      throw new Error(`Invalid level: ${this.currentLevel}`);
    }
    
    // 设置游戏参数
    this.timeRemaining = levelConfig.timeLimit;
    this.score = 0;
    this.combo = 0;
    this.comboTimer = 0;
    
    // 计算网格布局
    this.calculateGridLayout();
    
    // 初始化网格
    this.initGrid();
    
    // 初始化道具
    this.initProps();
    
    console.log(`Game initialized for level ${this.currentLevel}`);
  }

  // 计算网格布局
  calculateGridLayout() {
    const { width, height } = this.gameEngine.getDesignSize();
    const safeAreaTop = height * GameConfig.SCREEN.SAFE_AREA_TOP;
    const safeAreaBottom = height * GameConfig.SCREEN.SAFE_AREA_BOTTOM;
    
    // 计算可用区域
    const availableHeight = height - safeAreaTop - safeAreaBottom - 120 - 80; // 减去统计栏和道具栏
    const availableWidth = width * 0.9; // 留出边距
    
    // 计算格子大小
    const cellWidth = availableWidth / GameConfig.GRID.COLS;
    const cellHeight = availableHeight / GameConfig.GRID.ROWS;
    this.cellSize = Math.min(cellWidth, cellHeight) - GameConfig.GRID.PADDING;
    
    // 计算网格位置（居中）
    const gridWidth = this.cellSize * GameConfig.GRID.COLS + GameConfig.GRID.PADDING * (GameConfig.GRID.COLS - 1);
    const gridHeight = this.cellSize * GameConfig.GRID.ROWS + GameConfig.GRID.PADDING * (GameConfig.GRID.ROWS - 1);
    
    this.gridX = (width - gridWidth) / 2;
    this.gridY = safeAreaTop + 120 + (availableHeight - gridHeight) / 2;
    
    console.log(`Grid layout: size=${this.cellSize}, pos=(${this.gridX}, ${this.gridY})`);
  }

  // 初始化网格
  initGrid() {
    this.grid = Utils.Array.create2D(GameConfig.GRID.ROWS, GameConfig.GRID.COLS);
    
    // 填充网格，确保无初始3连消
    this.fillGridSafely();
    
    console.log('Grid initialized');
  }

  // 安全填充网格（避免初始3连消）
  fillGridSafely() {
    const levelConfig = GameConfig.LEVELS[this.currentLevel - 1];
    const animalTypes = levelConfig.animalTypes;
    
    for (let row = 0; row < GameConfig.GRID.ROWS; row++) {
      for (let col = 0; col < GameConfig.GRID.COLS; col++) {
        let animalType;
        let attempts = 0;
        
        do {
          animalType = Utils.Math.randomInt(0, animalTypes - 1);
          attempts++;
        } while (this.wouldCreateMatch(row, col, animalType) && attempts < 50);
        
        this.grid[row][col] = {
          type: animalType,
          x: col,
          y: row,
          isEliminating: false,
          isFalling: false,
          fallDistance: 0
        };
      }
    }
  }

  // 检查是否会创建匹配
  wouldCreateMatch(row, col, type) {
    // 检查水平方向
    let horizontalCount = 1;
    
    // 向左检查
    for (let c = col - 1; c >= 0 && this.grid[row][c] && this.grid[row][c].type === type; c--) {
      horizontalCount++;
    }
    
    // 向右检查
    for (let c = col + 1; c < GameConfig.GRID.COLS && this.grid[row][c] && this.grid[row][c].type === type; c++) {
      horizontalCount++;
    }
    
    if (horizontalCount >= 3) return true;
    
    // 检查垂直方向
    let verticalCount = 1;
    
    // 向上检查
    for (let r = row - 1; r >= 0 && this.grid[r][col] && this.grid[r][col].type === type; r--) {
      verticalCount++;
    }
    
    // 向下检查
    for (let r = row + 1; r < GameConfig.GRID.ROWS && this.grid[r][col] && this.grid[r][col].type === type; r++) {
      verticalCount++;
    }
    
    return verticalCount >= 3;
  }

  // 初始化道具
  initProps() {
    this.props = GameConfig.PROPS.map(propConfig => ({
      ...propConfig,
      count: 3, // 每种道具初始3个
      isSelected: false
    }));
  }

  // 初始化UI
  initUI() {
    const { width, height } = this.gameEngine.getDesignSize();
    const safeAreaTop = height * GameConfig.SCREEN.SAFE_AREA_TOP;
    
    // 返回按钮
    this.backButton = {
      x: 20,
      y: safeAreaTop,
      width: 60,
      height: 40,
      text: '返回'
    };
    
    // 道具按钮
    this.initPropButtons();
  }

  // 初始化道具按钮
  initPropButtons() {
    const { width, height } = this.gameEngine.getDesignSize();
    const propY = height - 100;
    const propSpacing = width / (this.props.length + 1);
    
    this.propButtons = this.props.map((prop, index) => ({
      ...prop,
      x: propSpacing * (index + 1) - 30,
      y: propY,
      width: 60,
      height: 60
    }));
  }

  // 开始游戏
  startGame() {
    this.isGameRunning = true;
    this.isGameOver = false;
    
    // 播放背景音乐
    this.resourceManager.playBackgroundMusic();
    
    console.log('Game started');
  }

  // 更新逻辑
  update(deltaTime) {
    if (!this.isInitialized || !this.isGameRunning) return;
    
    // 更新计时器
    this.updateTimer(deltaTime);
    
    // 更新连击计时器
    this.updateCombo(deltaTime);
    
    // 更新动画
    this.updateAnimations(deltaTime);
    
    // 检查游戏结束条件
    this.checkGameEnd();
  }

  // 更新计时器
  updateTimer(deltaTime) {
    this.timeRemaining -= deltaTime / 1000;
    if (this.timeRemaining <= 0) {
      this.timeRemaining = 0;
      this.endGame(false);
    }
  }

  // 更新连击
  updateCombo(deltaTime) {
    if (this.comboTimer > 0) {
      this.comboTimer -= deltaTime;
      if (this.comboTimer <= 0) {
        this.combo = 0;
      }
    }
    
    // 更新连击显示
    if (this.comboDisplay) {
      this.comboDisplay.life -= deltaTime;
      if (this.comboDisplay.life <= 0) {
        this.comboDisplay = null;
      }
    }
  }

  // 更新动画
  updateAnimations(deltaTime) {
    // 更新消除动画
    for (let i = this.eliminationAnimations.length - 1; i >= 0; i--) {
      const anim = this.eliminationAnimations[i];
      anim.progress += deltaTime / GameConfig.ANIMATION.ELIMINATION_DURATION;
      
      if (anim.progress >= 1) {
        this.eliminationAnimations.splice(i, 1);
      }
    }
    
    // 更新下落动画
    for (let i = this.fallingAnimations.length - 1; i >= 0; i--) {
      const anim = this.fallingAnimations[i];
      anim.progress += deltaTime / 300; // 300ms下落时间
      
      if (anim.progress >= 1) {
        this.fallingAnimations.splice(i, 1);
      }
    }
  }

  // 检查游戏结束
  checkGameEnd() {
    const levelConfig = GameConfig.LEVELS[this.currentLevel - 1];
    
    // 检查胜利条件
    if (this.score >= levelConfig.targetScore) {
      this.endGame(true);
      return;
    }
    
    // 检查失败条件（时间用完）
    if (this.timeRemaining <= 0) {
      this.endGame(false);
      return;
    }
    
    // 检查是否无解
    if (!this.hasValidMoves()) {
      this.showNoMovesDialog();
    }
  }

  // 检查是否有有效移动
  hasValidMoves() {
    for (let row = 0; row < GameConfig.GRID.ROWS; row++) {
      for (let col = 0; col < GameConfig.GRID.COLS; col++) {
        // 检查与右侧交换
        if (col < GameConfig.GRID.COLS - 1) {
          if (this.wouldCreateMatchAfterSwap(row, col, row, col + 1)) {
            return true;
          }
        }
        
        // 检查与下方交换
        if (row < GameConfig.GRID.ROWS - 1) {
          if (this.wouldCreateMatchAfterSwap(row, col, row + 1, col)) {
            return true;
          }
        }
      }
    }
    return false;
  }

  // 检查交换后是否会产生匹配
  wouldCreateMatchAfterSwap(row1, col1, row2, col2) {
    // 临时交换
    const temp = this.grid[row1][col1].type;
    this.grid[row1][col1].type = this.grid[row2][col2].type;
    this.grid[row2][col2].type = temp;
    
    // 检查是否产生匹配
    const hasMatch = this.findMatches().length > 0;
    
    // 恢复交换
    this.grid[row2][col2].type = this.grid[row1][col1].type;
    this.grid[row1][col1].type = temp;
    
    return hasMatch;
  }

  // 查找匹配
  findMatches() {
    const matches = [];
    const visited = Utils.Array.create2D(GameConfig.GRID.ROWS, GameConfig.GRID.COLS, false);
    
    for (let row = 0; row < GameConfig.GRID.ROWS; row++) {
      for (let col = 0; col < GameConfig.GRID.COLS; col++) {
        if (!visited[row][col] && this.grid[row][col]) {
          const match = this.findMatchGroup(row, col, visited);
          if (match.length >= 3) {
            matches.push(match);
          }
        }
      }
    }
    
    return matches;
  }

  // 查找匹配组
  findMatchGroup(startRow, startCol, visited) {
    const type = this.grid[startRow][startCol].type;
    const group = [];
    const stack = [{row: startRow, col: startCol}];
    
    while (stack.length > 0) {
      const {row, col} = stack.pop();
      
      if (row < 0 || row >= GameConfig.GRID.ROWS || 
          col < 0 || col >= GameConfig.GRID.COLS ||
          visited[row][col] || 
          !this.grid[row][col] || 
          this.grid[row][col].type !== type) {
        continue;
      }
      
      visited[row][col] = true;
      group.push({row, col});
      
      // 添加相邻格子
      stack.push({row: row - 1, col});
      stack.push({row: row + 1, col});
      stack.push({row, col: col - 1});
      stack.push({row, col: col + 1});
    }
    
    return group;
  }

  // 显示无移动对话框
  showNoMovesDialog() {
    // 简单实现：自动刷新网格
    console.log('No valid moves, shuffling grid...');
    this.shuffleGrid();
  }

  // 打乱网格
  shuffleGrid() {
    const cells = [];
    
    // 收集所有格子
    for (let row = 0; row < GameConfig.GRID.ROWS; row++) {
      for (let col = 0; col < GameConfig.GRID.COLS; col++) {
        if (this.grid[row][col]) {
          cells.push(this.grid[row][col].type);
        }
      }
    }
    
    // 打乱数组
    Utils.Array.shuffle(cells);
    
    // 重新分配
    let index = 0;
    for (let row = 0; row < GameConfig.GRID.ROWS; row++) {
      for (let col = 0; col < GameConfig.GRID.COLS; col++) {
        if (this.grid[row][col]) {
          this.grid[row][col].type = cells[index++];
        }
      }
    }
  }

  // 结束游戏
  endGame(isWin) {
    this.isGameRunning = false;
    this.isGameOver = true;
    
    if (isWin) {
      console.log('Game won!');
      this.resourceManager.playEffect(GameConfig.AUDIO.EFFECTS.WIN);
      
      // 检查是否有下一关
      if (this.currentLevel < GameConfig.LEVELS.length) {
        // 显示胜利界面，询问是否继续下一关
        this.showWinDialog();
      } else {
        // 全部通关
        this.showCompleteDialog();
      }
    } else {
      console.log('Game lost!');
      this.resourceManager.playEffect(GameConfig.AUDIO.EFFECTS.LOSE);
      this.showLoseDialog();
    }
    
    // 保存最高分
    this.saveHighScore();
  }

  // 显示胜利对话框
  showWinDialog() {
    // 简单实现：直接进入下一关
    setTimeout(() => {
      this.pageManager.navigateTo('game', { level: this.currentLevel + 1 });
    }, 2000);
  }

  // 显示完成对话框
  showCompleteDialog() {
    // 简单实现：返回主页
    setTimeout(() => {
      this.pageManager.navigateTo('home');
    }, 3000);
  }

  // 显示失败对话框
  showLoseDialog() {
    // 简单实现：返回主页
    setTimeout(() => {
      this.pageManager.navigateTo('home');
    }, 2000);
  }

  // 保存最高分
  saveHighScore() {
    const currentHighScore = Utils.Storage.get(GameConfig.STORAGE_KEYS.HIGH_SCORE, 0);
    if (this.score > currentHighScore) {
      Utils.Storage.set(GameConfig.STORAGE_KEYS.HIGH_SCORE, this.score);
    }
    
    const currentBestCombo = Utils.Storage.get(GameConfig.STORAGE_KEYS.BEST_COMBO, 0);
    if (this.combo > currentBestCombo) {
      Utils.Storage.set(GameConfig.STORAGE_KEYS.BEST_COMBO, this.combo);
    }
  }

  // 渲染页面
  render() {
    if (!this.isInitialized) return;
    
    const ctx = this.gameEngine.ctx;
    const { width, height } = this.gameEngine.getDesignSize();
    
    // 绘制背景
    this.renderBackground(ctx, width, height);
    
    // 绘制UI
    this.renderUI(ctx, width, height);
    
    // 绘制网格
    this.renderGrid(ctx);
    
    // 绘制道具栏
    this.renderProps(ctx);
    
    // 绘制连击显示
    this.renderComboDisplay(ctx);
    
    // 绘制游戏结束界面
    if (this.isGameOver) {
      this.renderGameOverlay(ctx, width, height);
    }
  }

  // 绘制背景
  renderBackground(ctx, width, height) {
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, GameConfig.COLORS.PRIMARY);
    gradient.addColorStop(1, '#FFF8DC');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  }

  // 绘制UI
  renderUI(ctx, width, height) {
    // 绘制返回按钮
    this.renderBackButton(ctx);
    
    // 绘制统计栏
    this.renderStatsBar(ctx, width);
  }

  // 绘制返回按钮
  renderBackButton(ctx) {
    const btn = this.backButton;
    
    ctx.save();
    ctx.fillStyle = GameConfig.COLORS.BUTTON_NORMAL;
    ctx.fillRect(btn.x, btn.y, btn.width, btn.height);
    
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = 2;
    ctx.strokeRect(btn.x, btn.y, btn.width, btn.height);
    
    ctx.fillStyle = GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = GameConfig.FONTS.BUTTON;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(btn.text, btn.x + btn.width / 2, btn.y + btn.height / 2);
    ctx.restore();
  }

  // 绘制统计栏
  renderStatsBar(ctx, width) {
    const levelConfig = GameConfig.LEVELS[this.currentLevel - 1];
    const statsY = this.gameEngine.getDesignSize().height * GameConfig.SCREEN.SAFE_AREA_TOP + 50;
    
    ctx.save();
    
    // 绘制背景
    ctx.fillStyle = GameConfig.COLORS.TRANSPARENT_WHITE;
    ctx.fillRect(width * 0.1, statsY, width * 0.8, 120);
    
    // 绘制关卡名称
    ctx.fillStyle = GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = GameConfig.FONTS.SUBTITLE;
    ctx.textAlign = 'center';
    ctx.fillText(levelConfig.name, width / 2, statsY + 25);
    
    // 绘制分数
    ctx.font = GameConfig.FONTS.SCORE;
    ctx.textAlign = 'left';
    ctx.fillText(`分数: ${this.score}/${levelConfig.targetScore}`, width * 0.15, statsY + 55);
    
    // 绘制时间
    ctx.textAlign = 'right';
    ctx.fillText(`时间: ${Utils.Time.formatTime(this.timeRemaining)}`, width * 0.85, statsY + 55);
    
    // 绘制连击
    if (this.combo > 0) {
      ctx.textAlign = 'center';
      ctx.fillStyle = this.combo >= 5 ? GameConfig.COLORS.COMBO_HIGH : GameConfig.COLORS.COMBO_NORMAL;
      ctx.fillText(`${this.combo}连击!`, width / 2, statsY + 85);
    }
    
    ctx.restore();
  }

  // 绘制网格
  renderGrid(ctx) {
    for (let row = 0; row < GameConfig.GRID.ROWS; row++) {
      for (let col = 0; col < GameConfig.GRID.COLS; col++) {
        this.renderCell(ctx, row, col);
      }
    }
  }

  // 绘制单个格子
  renderCell(ctx, row, col) {
    const cell = this.grid[row][col];
    if (!cell) return;
    
    const x = this.gridX + col * (this.cellSize + GameConfig.GRID.PADDING);
    const y = this.gridY + row * (this.cellSize + GameConfig.GRID.PADDING);
    
    ctx.save();
    
    // 应用下落动画
    const fallAnim = this.fallingAnimations.find(anim => anim.row === row && anim.col === col);
    if (fallAnim) {
      const fallOffset = Utils.Math.lerp(0, cell.fallDistance * (this.cellSize + GameConfig.GRID.PADDING), 
                                        Utils.Math.easeOut(fallAnim.progress));
      ctx.translate(0, -fallOffset);
    }
    
    // 应用消除动画
    const elimAnim = this.eliminationAnimations.find(anim => anim.row === row && anim.col === col);
    if (elimAnim) {
      const scale = 1 - Utils.Math.easeIn(elimAnim.progress);
      const alpha = 1 - elimAnim.progress;
      ctx.globalAlpha = alpha;
      ctx.translate(x + this.cellSize / 2, y + this.cellSize / 2);
      ctx.scale(scale, scale);
      ctx.translate(-this.cellSize / 2, -this.cellSize / 2);
    } else {
      ctx.translate(x, y);
    }
    
    // 绘制格子背景
    ctx.fillStyle = GameConfig.COLORS.BUTTON_NORMAL;
    ctx.fillRect(0, 0, this.cellSize, this.cellSize);
    
    // 绘制选中状态
    if (this.selectedCell && this.selectedCell.row === row && this.selectedCell.col === col) {
      ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
      ctx.lineWidth = 3;
      ctx.strokeRect(0, 0, this.cellSize, this.cellSize);
    }
    
    // 绘制动物图片
    const animal = GameConfig.ANIMALS[cell.type];
    const image = this.resourceManager.getImage(animal.image);
    if (image) {
      const padding = 4;
      ctx.drawImage(image, padding, padding, this.cellSize - padding * 2, this.cellSize - padding * 2);
    } else {
      // 备用：绘制颜色块
      ctx.fillStyle = animal.color;
      ctx.fillRect(4, 4, this.cellSize - 8, this.cellSize - 8);
    }
    
    ctx.restore();
  }

  // 绘制道具栏
  renderProps(ctx) {
    for (const propBtn of this.propButtons) {
      this.renderPropButton(ctx, propBtn);
    }
  }

  // 绘制道具按钮
  renderPropButton(ctx, propBtn) {
    ctx.save();
    
    // 绘制按钮背景
    ctx.fillStyle = propBtn.isSelected ? GameConfig.COLORS.BUTTON_HOVER : GameConfig.COLORS.BUTTON_NORMAL;
    ctx.fillRect(propBtn.x, propBtn.y, propBtn.width, propBtn.height);
    
    // 绘制边框
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = propBtn.isSelected ? 3 : 1;
    ctx.strokeRect(propBtn.x, propBtn.y, propBtn.width, propBtn.height);
    
    // 绘制道具图片
    const image = this.resourceManager.getImage(propBtn.image);
    if (image) {
      ctx.drawImage(image, propBtn.x + 5, propBtn.y + 5, propBtn.width - 10, propBtn.height - 10);
    }
    
    // 绘制数量
    ctx.fillStyle = GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = '12px Arial';
    ctx.textAlign = 'right';
    ctx.fillText(propBtn.count.toString(), propBtn.x + propBtn.width - 5, propBtn.y + propBtn.height - 5);
    
    ctx.restore();
  }

  // 绘制连击显示
  renderComboDisplay(ctx) {
    if (!this.comboDisplay) return;
    
    const display = this.comboDisplay;
    const alpha = display.life / display.maxLife;
    
    ctx.save();
    ctx.globalAlpha = alpha;
    ctx.fillStyle = display.color;
    ctx.font = '36px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(display.text, display.x, display.y);
    ctx.restore();
  }

  // 绘制游戏结束覆盖层
  renderGameOverlay(ctx, width, height) {
    ctx.save();
    
    // 绘制半透明背景
    ctx.fillStyle = GameConfig.COLORS.OVERLAY;
    ctx.fillRect(0, 0, width, height);
    
    // 绘制结果文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = GameConfig.FONTS.TITLE;
    ctx.textAlign = 'center';
    
    const resultText = this.score >= GameConfig.LEVELS[this.currentLevel - 1].targetScore ? '胜利!' : '失败!';
    ctx.fillText(resultText, width / 2, height / 2 - 50);
    
    ctx.font = GameConfig.FONTS.SUBTITLE;
    ctx.fillText(`最终分数: ${this.score}`, width / 2, height / 2 + 20);
    
    ctx.restore();
  }

  // 处理输入开始
  onInputStart(x, y) {
    // 检查返回按钮
    if (Utils.Collision.pointInRect(x, y, this.backButton.x, this.backButton.y, 
                                   this.backButton.width, this.backButton.height)) {
      this.pageManager.goBack();
      return;
    }
    
    // 检查道具按钮
    for (const propBtn of this.propButtons) {
      if (Utils.Collision.pointInRect(x, y, propBtn.x, propBtn.y, propBtn.width, propBtn.height)) {
        this.selectProp(propBtn);
        return;
      }
    }
    
    // 检查网格
    const gridCell = this.getGridCellAt(x, y);
    if (gridCell) {
      this.handleGridInput(gridCell, x, y);
    }
  }

  // 处理输入移动
  onInputMove(x, y) {
    if (this.isDragging) {
      const gridCell = this.getGridCellAt(x, y);
      if (gridCell && gridCell !== this.dragStartCell) {
        this.handleSwap(this.dragStartCell, gridCell);
        this.isDragging = false;
        this.dragStartCell = null;
      }
    }
  }

  // 处理输入结束
  onInputEnd() {
    this.isDragging = false;
    this.dragStartCell = null;
  }

  // 获取指定位置的网格格子
  getGridCellAt(x, y) {
    const col = Math.floor((x - this.gridX) / (this.cellSize + GameConfig.GRID.PADDING));
    const row = Math.floor((y - this.gridY) / (this.cellSize + GameConfig.GRID.PADDING));
    
    if (row >= 0 && row < GameConfig.GRID.ROWS && col >= 0 && col < GameConfig.GRID.COLS) {
      return { row, col };
    }
    
    return null;
  }

  // 处理网格输入
  handleGridInput(gridCell, x, y) {
    if (this.selectedProp) {
      this.useProp(this.selectedProp, gridCell);
    } else {
      this.selectedCell = gridCell;
      this.dragStartCell = gridCell;
      this.isDragging = true;
    }
  }

  // 选择道具
  selectProp(propBtn) {
    if (propBtn.count <= 0) return;
    
    // 取消之前的选择
    this.propButtons.forEach(btn => btn.isSelected = false);
    
    // 选择当前道具
    propBtn.isSelected = true;
    this.selectedProp = propBtn;
    
    this.resourceManager.playEffect(GameConfig.AUDIO.EFFECTS.CAT, 0.5);
  }

  // 使用道具
  useProp(prop, gridCell) {
    if (prop.count <= 0) return;
    
    prop.count--;
    prop.isSelected = false;
    this.selectedProp = null;
    
    switch (prop.id) {
      case 'refresh':
        this.shuffleGrid();
        break;
      case 'bomb':
        this.useBombProp(gridCell);
        break;
      case 'clear':
        this.useClearProp();
        break;
      case 'levelDown':
        this.useLevelDownProp();
        break;
    }
    
    // 播放道具音效
    if (prop.audio) {
      this.resourceManager.playEffect(prop.audio);
    }
  }

  // 使用炸弹道具
  useBombProp(gridCell) {
    const { row, col } = gridCell;
    const eliminatedCells = [];
    
    // 5x5范围消除
    for (let r = Math.max(0, row - 2); r <= Math.min(GameConfig.GRID.ROWS - 1, row + 2); r++) {
      for (let c = Math.max(0, col - 2); c <= Math.min(GameConfig.GRID.COLS - 1, col + 2); c++) {
        if (this.grid[r][c]) {
          eliminatedCells.push({ row: r, col: c });
        }
      }
    }
    
    this.eliminateCells(eliminatedCells);
    this.addScore(GameConfig.SCORE.SPECIAL_BOMB);
  }

  // 使用清屏道具
  useClearProp() {
    const eliminatedCells = [];
    
    for (let row = 0; row < GameConfig.GRID.ROWS; row++) {
      for (let col = 0; col < GameConfig.GRID.COLS; col++) {
        if (this.grid[row][col]) {
          eliminatedCells.push({ row, col });
        }
      }
    }
    
    this.eliminateCells(eliminatedCells);
    this.addScore(GameConfig.SCORE.SPECIAL_CLEAR);
  }

  // 使用降级道具
  useLevelDownProp() {
    const levelConfig = GameConfig.LEVELS[this.currentLevel - 1];
    if (levelConfig.animalTypes <= 3) return; // 最少保留3种
    
    // 找出最少的动物类型并移除
    const typeCounts = new Array(levelConfig.animalTypes).fill(0);
    
    for (let row = 0; row < GameConfig.GRID.ROWS; row++) {
      for (let col = 0; col < GameConfig.GRID.COLS; col++) {
        if (this.grid[row][col]) {
          typeCounts[this.grid[row][col].type]++;
        }
      }
    }
    
    const minCount = Math.min(...typeCounts);
    const removeType = typeCounts.indexOf(minCount);
    
    // 将该类型替换为其他类型
    for (let row = 0; row < GameConfig.GRID.ROWS; row++) {
      for (let col = 0; col < GameConfig.GRID.COLS; col++) {
        if (this.grid[row][col] && this.grid[row][col].type === removeType) {
          this.grid[row][col].type = Utils.Math.randomInt(0, levelConfig.animalTypes - 2);
          if (this.grid[row][col].type >= removeType) {
            this.grid[row][col].type++;
          }
        }
      }
    }
  }

  // 处理交换
  handleSwap(cell1, cell2) {
    if (!cell1 || !cell2) return;
    if (cell1.row === cell2.row && cell1.col === cell2.col) return;
    
    // 检查是否相邻
    const rowDiff = Math.abs(cell1.row - cell2.row);
    const colDiff = Math.abs(cell1.col - cell2.col);
    
    if ((rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)) {
      this.swapCells(cell1, cell2);
    }
  }

  // 交换格子
  swapCells(cell1, cell2) {
    const temp = this.grid[cell1.row][cell1.col].type;
    this.grid[cell1.row][cell1.col].type = this.grid[cell2.row][cell2.col].type;
    this.grid[cell2.row][cell2.col].type = temp;
    
    // 检查是否产生匹配
    const matches = this.findMatches();
    if (matches.length > 0) {
      this.processMatches(matches);
      this.resourceManager.playEffect(GameConfig.AUDIO.EFFECTS.NORMAL_MATCH);
    } else {
      // 无匹配，交换回来
      const temp = this.grid[cell1.row][cell1.col].type;
      this.grid[cell1.row][cell1.col].type = this.grid[cell2.row][cell2.col].type;
      this.grid[cell2.row][cell2.col].type = temp;
    }
    
    this.selectedCell = null;
  }

  // 处理匹配
  processMatches(matches) {
    let totalScore = 0;
    
    for (const match of matches) {
      const matchScore = this.calculateMatchScore(match.length);
      totalScore += matchScore;
      
      // 创建消除动画
      for (const cell of match) {
        this.eliminationAnimations.push({
          row: cell.row,
          col: cell.col,
          progress: 0
        });
      }
    }
    
    // 增加连击
    this.addCombo();
    
    // 应用连击倍率
    const comboMultiplier = this.getComboMultiplier();
    totalScore = Math.floor(totalScore * comboMultiplier);
    
    this.addScore(totalScore);
    
    // 延迟消除格子
    setTimeout(() => {
      this.eliminateMatches(matches);
    }, GameConfig.ANIMATION.ELIMINATION_DURATION);
  }

  // 计算匹配分数
  calculateMatchScore(matchLength) {
    switch (matchLength) {
      case 3: return GameConfig.SCORE.MATCH_3;
      case 4: return GameConfig.SCORE.MATCH_4;
      case 5: return GameConfig.SCORE.MATCH_5;
      default: return GameConfig.SCORE.MATCH_5 + (matchLength - 5) * 10;
    }
  }

  // 增加连击
  addCombo() {
    this.combo++;
    this.comboTimer = GameConfig.SCORE.COMBO_TIMEOUT;
    
    // 显示连击
    this.showComboDisplay();
    
    // 播放连击音效
    if (this.combo === 3) {
      this.resourceManager.playEffect(GameConfig.AUDIO.EFFECTS.COMBO_3);
    } else if (this.combo >= 5) {
      this.resourceManager.playEffect(GameConfig.AUDIO.EFFECTS.COMBO_5);
    }
  }

  // 获取连击倍率
  getComboMultiplier() {
    const index = Math.min(this.combo - 1, GameConfig.SCORE.COMBO_MULTIPLIER.length - 1);
    return GameConfig.SCORE.COMBO_MULTIPLIER[Math.max(0, index)];
  }

  // 显示连击
  showComboDisplay() {
    if (this.combo < 2) return;
    
    const { width, height } = this.gameEngine.getDesignSize();
    
    this.comboDisplay = {
      text: `${this.combo}连击!`,
      x: width / 2,
      y: height / 2,
      color: this.combo >= 5 ? GameConfig.COLORS.COMBO_HIGH : GameConfig.COLORS.COMBO_NORMAL,
      life: GameConfig.ANIMATION.COMBO_DISPLAY_DURATION,
      maxLife: GameConfig.ANIMATION.COMBO_DISPLAY_DURATION
    };
  }

  // 增加分数
  addScore(points) {
    this.score += points;
    console.log(`Score added: ${points}, total: ${this.score}`);
  }

  // 消除匹配
  eliminateMatches(matches) {
    for (const match of matches) {
      for (const cell of match) {
        this.grid[cell.row][cell.col] = null;
      }
    }
    
    // 应用重力
    this.applyGravity();
  }

  // 消除指定格子
  eliminateCells(cells) {
    for (const cell of cells) {
      this.eliminationAnimations.push({
        row: cell.row,
        col: cell.col,
        progress: 0
      });
    }
    
    setTimeout(() => {
      for (const cell of cells) {
        this.grid[cell.row][cell.col] = null;
      }
      this.applyGravity();
    }, GameConfig.ANIMATION.ELIMINATION_DURATION);
  }

  // 应用重力
  applyGravity() {
    for (let col = 0; col < GameConfig.GRID.COLS; col++) {
      // 收集该列的非空格子
      const column = [];
      for (let row = GameConfig.GRID.ROWS - 1; row >= 0; row--) {
        if (this.grid[row][col]) {
          column.push(this.grid[row][col]);
        }
      }
      
      // 清空该列
      for (let row = 0; row < GameConfig.GRID.ROWS; row++) {
        this.grid[row][col] = null;
      }
      
      // 重新填充该列
      for (let i = 0; i < column.length; i++) {
        const newRow = GameConfig.GRID.ROWS - 1 - i;
        this.grid[newRow][col] = column[i];
        this.grid[newRow][col].y = newRow;
        
        // 创建下落动画
        const fallDistance = (GameConfig.GRID.ROWS - 1 - column[i].y) - newRow;
        if (fallDistance > 0) {
          this.fallingAnimations.push({
            row: newRow,
            col: col,
            progress: 0
          });
          this.grid[newRow][col].fallDistance = fallDistance;
        }
      }
      
      // 填充新格子
      const levelConfig = GameConfig.LEVELS[this.currentLevel - 1];
      for (let row = 0; row < GameConfig.GRID.ROWS - column.length; row++) {
        this.grid[row][col] = {
          type: Utils.Math.randomInt(0, levelConfig.animalTypes - 1),
          x: col,
          y: row,
          isEliminating: false,
          isFalling: false,
          fallDistance: GameConfig.GRID.ROWS - column.length - row
        };
        
        // 创建下落动画
        this.fallingAnimations.push({
          row: row,
          col: col,
          progress: 0
        });
      }
    }
    
    // 延迟检查新的匹配
    setTimeout(() => {
      const newMatches = this.findMatches();
      if (newMatches.length > 0) {
        this.processMatches(newMatches);
      }
    }, 350);
  }

  // 销毁页面
  destroy() {
    console.log('Destroying GamePage');
    
    // 停止游戏
    this.isGameRunning = false;
    
    // 销毁资源管理器
    if (this.resourceManager) {
      this.resourceManager.destroy();
      this.resourceManager = null;
    }
    
    // 清理数据
    this.grid = [];
    this.eliminationAnimations = [];
    this.fallingAnimations = [];
    this.comboDisplay = null;
    this.selectedCell = null;
    this.dragStartCell = null;
    this.selectedProp = null;
    this.props = [];
    this.propButtons = [];
    this.backButton = null;
    this.isInitialized = false;
    
    // 清理引用
    this.gameEngine = null;
    this.pageManager = null;
  }
}

// 导出类到全局作用域
if (typeof global !== 'undefined') {
  global.GamePage = GamePage;
}
if (typeof window !== 'undefined') {
  window.GamePage = GamePage;
}
// 抖音小游戏环境
if (typeof tt !== 'undefined') {
  this.GamePage = GamePage;
}
