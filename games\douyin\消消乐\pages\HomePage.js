// 主页面类
class HomePage {
  constructor(gameEngine, pageManager) {
    this.gameEngine = gameEngine;
    this.pageManager = pageManager;
    this.resourceManager = null;
    
    // 动画状态
    this.titleFloatTime = 0;
    this.particles = [];
    this.buttons = [];
    
    // 输入状态
    this.hoveredButton = null;
    this.pressedButton = null;
    
    // 页面状态
    this.isInitialized = false;
  }

  // 初始化页面
  async init(params = {}) {
    console.log('Initializing HomePage');
    
    try {
      // 创建资源管理器
      this.resourceManager = new ResourceManager();
      
      // 预加载基础资源
      await this.resourceManager.preloadBasicResources();
      
      // 初始化按钮
      this.initButtons();
      
      // 初始化粒子系统
      this.initParticles();
      
      // 播放背景音乐
      this.resourceManager.playBackgroundMusic();
      
      this.isInitialized = true;
      console.log('HomePage initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize HomePage:', error);
      throw error;
    }
  }

  // 初始化按钮
  initButtons() {
    const { width, height } = this.gameEngine.getDesignSize();
    const buttonY = height - 200;
    const buttonSpacing = GameConfig.BUTTONS.WIDTH + GameConfig.BUTTONS.SPACING;
    const totalWidth = buttonSpacing * 3 - GameConfig.BUTTONS.SPACING;
    const startX = (width - totalWidth) / 2;

    this.buttons = [
      {
        id: 'start',
        text: '开始游戏',
        x: startX,
        y: buttonY,
        width: GameConfig.BUTTONS.WIDTH,
        height: GameConfig.BUTTONS.HEIGHT,
        image: 'images/button/start.png',
        action: () => this.pageManager.navigateTo('game')
      },
      {
        id: 'rank',
        text: '排行榜',
        x: startX + buttonSpacing,
        y: buttonY,
        width: GameConfig.BUTTONS.WIDTH,
        height: GameConfig.BUTTONS.HEIGHT,
        image: 'images/button/ranking.png',
        action: () => this.pageManager.navigateTo('rank')
      },
      {
        id: 'setting',
        text: '设置',
        x: startX + buttonSpacing * 2,
        y: buttonY,
        width: GameConfig.BUTTONS.WIDTH,
        height: GameConfig.BUTTONS.HEIGHT,
        image: 'images/icon/icon.png',
        action: () => this.pageManager.navigateTo('setting')
      }
    ];
  }

  // 初始化粒子系统
  initParticles() {
    this.particles = [];
    this.lastParticleTime = 0;
  }

  // 更新逻辑
  update(deltaTime) {
    if (!this.isInitialized) return;

    // 更新标题浮动动画
    this.titleFloatTime += deltaTime;

    // 更新粒子系统
    this.updateParticles(deltaTime);

    // 生成新粒子
    this.spawnParticles(deltaTime);
  }

  // 更新粒子
  updateParticles(deltaTime) {
    for (let i = this.particles.length - 1; i >= 0; i--) {
      const particle = this.particles[i];
      
      // 更新位置
      particle.y += particle.speed * deltaTime / 1000;
      particle.x += Math.sin(particle.y * 0.01) * particle.sway;
      
      // 更新生命周期
      particle.life -= deltaTime;
      particle.alpha = Math.max(0, particle.life / particle.maxLife);
      
      // 移除死亡粒子
      if (particle.life <= 0 || particle.y > this.gameEngine.getDesignSize().height) {
        this.particles.splice(i, 1);
      }
    }
  }

  // 生成粒子
  spawnParticles(deltaTime) {
    this.lastParticleTime += deltaTime;
    const spawnInterval = 1000 / GameConfig.ANIMATION.PARTICLE_SPAWN_RATE;
    
    if (this.lastParticleTime >= spawnInterval) {
      this.createParticle();
      this.lastParticleTime = 0;
    }
  }

  // 创建单个粒子
  createParticle() {
    const { width } = this.gameEngine.getDesignSize();
    
    const particle = {
      x: Utils.Math.randomFloat(0, width),
      y: -20,
      speed: Utils.Math.randomFloat(50, 100),
      sway: Utils.Math.randomFloat(-0.5, 0.5),
      size: Utils.Math.randomFloat(8, 16),
      color: Utils.Array.randomElement(['#FF6B8B', '#4ECDC4', '#45B7D1', '#96CEB4']),
      life: Utils.Math.randomFloat(3000, 5000),
      maxLife: 0,
      alpha: 1,
      rotation: Utils.Math.randomFloat(0, Math.PI * 2),
      rotationSpeed: Utils.Math.randomFloat(-0.002, 0.002)
    };
    
    particle.maxLife = particle.life;
    this.particles.push(particle);
  }

  // 渲染页面
  render() {
    if (!this.isInitialized) return;

    const ctx = this.gameEngine.ctx;
    const { width, height } = this.gameEngine.getDesignSize();

    // 绘制背景
    this.renderBackground(ctx, width, height);
    
    // 绘制粒子
    this.renderParticles(ctx);
    
    // 绘制标题
    this.renderTitle(ctx, width, height);
    
    // 绘制版本信息
    this.renderVersion(ctx, width, height);
    
    // 绘制按钮
    this.renderButtons(ctx);
  }

  // 绘制背景
  renderBackground(ctx, width, height) {
    // 绘制渐变背景
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, GameConfig.COLORS.PRIMARY);
    gradient.addColorStop(1, '#FFF8DC');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  }

  // 绘制粒子
  renderParticles(ctx) {
    ctx.save();
    
    for (const particle of this.particles) {
      ctx.save();
      ctx.globalAlpha = particle.alpha;
      ctx.translate(particle.x, particle.y);
      ctx.rotate(particle.rotation);
      
      // 绘制爱心形状
      ctx.fillStyle = particle.color;
      this.drawHeart(ctx, 0, 0, particle.size);
      
      ctx.restore();
      
      // 更新旋转
      particle.rotation += particle.rotationSpeed;
    }
    
    ctx.restore();
  }

  // 绘制爱心
  drawHeart(ctx, x, y, size) {
    const scale = size / 20;
    ctx.save();
    ctx.scale(scale, scale);
    
    ctx.beginPath();
    ctx.moveTo(0, -8);
    ctx.bezierCurveTo(-12, -18, -25, -8, -12, 2);
    ctx.bezierCurveTo(-12, 2, 0, 12, 0, 12);
    ctx.bezierCurveTo(0, 12, 12, 2, 12, 2);
    ctx.bezierCurveTo(25, -8, 12, -18, 0, -8);
    ctx.closePath();
    ctx.fill();
    
    ctx.restore();
  }

  // 绘制标题
  renderTitle(ctx, width, height) {
    const titleY = height * 0.3;
    const floatOffset = Math.sin(this.titleFloatTime / GameConfig.ANIMATION.TITLE_FLOAT_DURATION * Math.PI * 2) * 5;
    
    ctx.save();
    
    // 绘制标题阴影
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.font = GameConfig.FONTS.TITLE;
    ctx.textAlign = 'center';
    ctx.fillText(GameConfig.GAME.TITLE, width / 2 + 3, titleY + floatOffset + 3);
    
    // 绘制标题渐变
    const titleGradient = ctx.createLinearGradient(0, titleY - 30, 0, titleY + 30);
    titleGradient.addColorStop(0, GameConfig.COLORS.SECONDARY);
    titleGradient.addColorStop(1, '#FF8FA3');
    
    ctx.fillStyle = titleGradient;
    ctx.fillText(GameConfig.GAME.TITLE, width / 2, titleY + floatOffset);
    
    ctx.restore();
  }

  // 绘制版本信息
  renderVersion(ctx, width, height) {
    ctx.save();
    ctx.fillStyle = GameConfig.COLORS.TEXT_SECONDARY;
    ctx.font = GameConfig.FONTS.VERSION;
    ctx.textAlign = 'right';
    ctx.fillText(GameConfig.GAME.VERSION, width - 20, height - 20);
    ctx.restore();
  }

  // 绘制按钮
  renderButtons(ctx) {
    for (const button of this.buttons) {
      this.renderButton(ctx, button);
    }
  }

  // 绘制单个按钮
  renderButton(ctx, button) {
    ctx.save();
    
    // 计算按钮状态
    const isHovered = this.hoveredButton === button;
    const isPressed = this.pressedButton === button;
    
    let scale = 1;
    if (isPressed) {
      scale = GameConfig.ANIMATION.BUTTON_PRESS_SCALE;
    } else if (isHovered) {
      scale = GameConfig.ANIMATION.BUTTON_HOVER_SCALE;
    }
    
    // 应用变换
    ctx.translate(button.x + button.width / 2, button.y + button.height / 2);
    ctx.scale(scale, scale);
    ctx.translate(-button.width / 2, -button.height / 2);
    
    // 绘制按钮阴影
    if (!isPressed) {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
      ctx.fillRect(2, 2, button.width, button.height);
    }
    
    // 绘制按钮背景
    ctx.fillStyle = isPressed ? GameConfig.COLORS.BUTTON_HOVER : GameConfig.COLORS.BUTTON_NORMAL;
    ctx.fillRect(0, 0, button.width, button.height);
    
    // 绘制按钮边框
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = 2;
    ctx.strokeRect(0, 0, button.width, button.height);
    
    // 绘制按钮文字
    ctx.fillStyle = GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = GameConfig.FONTS.BUTTON;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(button.text, button.width / 2, button.height / 2);
    
    ctx.restore();
  }

  // 处理输入开始
  onInputStart(x, y) {
    const button = this.getButtonAt(x, y);
    if (button) {
      this.pressedButton = button;
      this.hoveredButton = button;
      
      // 播放按钮音效
      this.resourceManager.playEffect(GameConfig.AUDIO.EFFECTS.CAT, 0.5);
      
      // 触发震动
      Utils.Device.vibrate();
    }
  }

  // 处理输入移动
  onInputMove(x, y) {
    const button = this.getButtonAt(x, y);
    this.hoveredButton = button;
    
    // 如果移出按钮区域，取消按下状态
    if (this.pressedButton && button !== this.pressedButton) {
      this.pressedButton = null;
    }
  }

  // 处理输入结束
  onInputEnd() {
    if (this.pressedButton && this.hoveredButton === this.pressedButton) {
      // 执行按钮动作
      this.pressedButton.action();
      
      // 30%概率生成粒子效果
      if (Math.random() < 0.3) {
        this.createButtonParticles(this.pressedButton);
      }
    }
    
    this.pressedButton = null;
    this.hoveredButton = null;
  }

  // 获取指定位置的按钮
  getButtonAt(x, y) {
    for (const button of this.buttons) {
      if (Utils.Collision.pointInRect(x, y, button.x, button.y, button.width, button.height)) {
        return button;
      }
    }
    return null;
  }

  // 创建按钮粒子效果
  createButtonParticles(button) {
    const centerX = button.x + button.width / 2;
    const centerY = button.y + button.height / 2;
    
    for (let i = 0; i < 10; i++) {
      const particle = {
        x: centerX + Utils.Math.randomFloat(-20, 20),
        y: centerY + Utils.Math.randomFloat(-20, 20),
        speed: Utils.Math.randomFloat(100, 200),
        sway: Utils.Math.randomFloat(-1, 1),
        size: Utils.Math.randomFloat(4, 8),
        color: '#FFFFFF',
        life: Utils.Math.randomFloat(500, 1000),
        maxLife: 0,
        alpha: 1,
        rotation: Utils.Math.randomFloat(0, Math.PI * 2),
        rotationSpeed: Utils.Math.randomFloat(-0.01, 0.01)
      };
      
      particle.maxLife = particle.life;
      this.particles.push(particle);
    }
  }

  // 销毁页面
  destroy() {
    console.log('Destroying HomePage');
    
    // 停止背景音乐
    if (this.resourceManager) {
      this.resourceManager.stopBackgroundMusic();
      this.resourceManager.destroy();
      this.resourceManager = null;
    }
    
    // 清理数据
    this.particles = [];
    this.buttons = [];
    this.hoveredButton = null;
    this.pressedButton = null;
    this.isInitialized = false;
    
    // 清理引用
    this.gameEngine = null;
    this.pageManager = null;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = HomePage;
} else if (typeof window !== 'undefined') {
  window.HomePage = HomePage;
}
