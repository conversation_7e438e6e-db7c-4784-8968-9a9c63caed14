// 排行榜页面类
class RankPage {
  constructor(gameEngine, pageManager) {
    this.gameEngine = gameEngine;
    this.pageManager = pageManager;
    this.resourceManager = null;
    
    // 排行榜数据
    this.personalData = {
      highScore: 0,
      bestCombo: 0,
      bestTime: 0
    };
    
    // 榜单数据
    this.rankData = {
      score: [],
      time: [],
      combo: []
    };
    
    // UI状态
    this.currentTab = 'score'; // score, time, combo
    this.tabs = [
      { id: 'score', name: '分数榜', icon: '🏆' },
      { id: 'time', name: '时间榜', icon: '⏱️' },
      { id: 'combo', name: '连击榜', icon: '🔥' }
    ];
    
    // UI元素
    this.backButton = null;
    this.tabButtons = [];
    this.scrollOffset = 0;
    this.maxScroll = 0;
    
    // 输入状态
    this.isDragging = false;
    this.lastTouchY = 0;
    this.velocity = 0;
    
    // 页面状态
    this.isInitialized = false;
  }

  // 初始化页面
  async init(params = {}) {
    console.log('Initializing RankPage');
    
    try {
      // 创建资源管理器
      this.resourceManager = new ResourceManager();
      
      // 预加载基础资源
      await this.resourceManager.preloadBasicResources();
      
      // 加载个人数据
      this.loadPersonalData();
      
      // 加载排行榜数据
      this.loadRankData();
      
      // 初始化UI
      this.initUI();
      
      this.isInitialized = true;
      console.log('RankPage initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize RankPage:', error);
      throw error;
    }
  }

  // 加载个人数据
  loadPersonalData() {
    this.personalData = {
      highScore: Utils.Storage.get(GameConfig.STORAGE_KEYS.HIGH_SCORE, 0),
      bestCombo: Utils.Storage.get(GameConfig.STORAGE_KEYS.BEST_COMBO, 0),
      bestTime: Utils.Storage.get(GameConfig.STORAGE_KEYS.BEST_TIME, 0)
    };
    
    console.log('Personal data loaded:', this.personalData);
  }

  // 加载排行榜数据
  loadRankData() {
    // 从本地存储加载排行榜数据
    const savedRankData = Utils.Storage.get(GameConfig.STORAGE_KEYS.RANK_DATA, null);
    
    if (savedRankData) {
      this.rankData = savedRankData;
    } else {
      // 生成模拟数据
      this.generateMockRankData();
    }
    
    // 添加当前玩家数据
    this.addPlayerToRank();
    
    console.log('Rank data loaded:', this.rankData);
  }

  // 生成模拟排行榜数据
  generateMockRankData() {
    const mockNames = [
      '萌宠大师', '消消达人', '连击王者', '时间猎手', '分数狂魔',
      '游戏高手', '挑战者', '冠军选手', '超级玩家', '传奇大神'
    ];
    
    // 分数榜
    this.rankData.score = [];
    for (let i = 0; i < 10; i++) {
      this.rankData.score.push({
        name: mockNames[i],
        value: Utils.Math.randomInt(5000, 15000),
        level: Utils.Math.randomInt(1, 3),
        date: new Date(Date.now() - Utils.Math.randomInt(0, 30) * 24 * 60 * 60 * 1000).toLocaleDateString()
      });
    }
    this.rankData.score.sort((a, b) => b.value - a.value);
    
    // 时间榜（最快通关时间）
    this.rankData.time = [];
    for (let i = 0; i < 10; i++) {
      this.rankData.time.push({
        name: mockNames[i],
        value: Utils.Math.randomInt(60, 300), // 秒
        level: Utils.Math.randomInt(1, 3),
        date: new Date(Date.now() - Utils.Math.randomInt(0, 30) * 24 * 60 * 60 * 1000).toLocaleDateString()
      });
    }
    this.rankData.time.sort((a, b) => a.value - b.value); // 时间越短越好
    
    // 连击榜
    this.rankData.combo = [];
    for (let i = 0; i < 10; i++) {
      this.rankData.combo.push({
        name: mockNames[i],
        value: Utils.Math.randomInt(5, 20),
        level: Utils.Math.randomInt(1, 3),
        date: new Date(Date.now() - Utils.Math.randomInt(0, 30) * 24 * 60 * 60 * 1000).toLocaleDateString()
      });
    }
    this.rankData.combo.sort((a, b) => b.value - a.value);
  }

  // 添加玩家到排行榜
  addPlayerToRank() {
    const playerName = '我';
    const currentDate = new Date().toLocaleDateString();
    
    // 添加到分数榜
    if (this.personalData.highScore > 0) {
      this.rankData.score.push({
        name: playerName,
        value: this.personalData.highScore,
        level: Utils.Storage.get(GameConfig.STORAGE_KEYS.CURRENT_LEVEL, 1),
        date: currentDate,
        isPlayer: true
      });
      this.rankData.score.sort((a, b) => b.value - a.value);
      this.rankData.score = this.rankData.score.slice(0, 10);
    }
    
    // 添加到连击榜
    if (this.personalData.bestCombo > 0) {
      this.rankData.combo.push({
        name: playerName,
        value: this.personalData.bestCombo,
        level: Utils.Storage.get(GameConfig.STORAGE_KEYS.CURRENT_LEVEL, 1),
        date: currentDate,
        isPlayer: true
      });
      this.rankData.combo.sort((a, b) => b.value - a.value);
      this.rankData.combo = this.rankData.combo.slice(0, 10);
    }
    
    // 添加到时间榜
    if (this.personalData.bestTime > 0) {
      this.rankData.time.push({
        name: playerName,
        value: this.personalData.bestTime,
        level: Utils.Storage.get(GameConfig.STORAGE_KEYS.CURRENT_LEVEL, 1),
        date: currentDate,
        isPlayer: true
      });
      this.rankData.time.sort((a, b) => a.value - b.value);
      this.rankData.time = this.rankData.time.slice(0, 10);
    }
  }

  // 初始化UI
  initUI() {
    const { width, height } = this.gameEngine.getDesignSize();
    const safeAreaTop = height * GameConfig.SCREEN.SAFE_AREA_TOP;
    
    // 返回按钮
    this.backButton = {
      x: 20,
      y: safeAreaTop,
      width: 60,
      height: 40,
      text: '返回'
    };
    
    // 标签按钮
    this.initTabButtons();
    
    // 计算最大滚动距离
    this.calculateMaxScroll();
  }

  // 初始化标签按钮
  initTabButtons() {
    const { width } = this.gameEngine.getDesignSize();
    const tabWidth = width / this.tabs.length;
    
    this.tabButtons = this.tabs.map((tab, index) => ({
      ...tab,
      x: index * tabWidth,
      y: 200,
      width: tabWidth,
      height: 50,
      isActive: tab.id === this.currentTab
    }));
  }

  // 计算最大滚动距离
  calculateMaxScroll() {
    const { height } = this.gameEngine.getDesignSize();
    const listStartY = 300;
    const listEndY = height - 50;
    const availableHeight = listEndY - listStartY;
    const itemHeight = 60;
    const totalHeight = this.getCurrentRankList().length * itemHeight;
    
    this.maxScroll = Math.max(0, totalHeight - availableHeight);
  }

  // 获取当前排行榜列表
  getCurrentRankList() {
    return this.rankData[this.currentTab] || [];
  }

  // 更新逻辑
  update(deltaTime) {
    if (!this.isInitialized) return;
    
    // 更新滚动惯性
    this.updateScrollInertia(deltaTime);
  }

  // 更新滚动惯性
  updateScrollInertia(deltaTime) {
    if (!this.isDragging && Math.abs(this.velocity) > 0.1) {
      this.scrollOffset += this.velocity * deltaTime / 16;
      this.velocity *= 0.95; // 阻尼
      
      // 限制滚动范围
      this.scrollOffset = Utils.Math.clamp(this.scrollOffset, 0, this.maxScroll);
      
      if (Math.abs(this.velocity) < 0.1) {
        this.velocity = 0;
      }
    }
  }

  // 渲染页面
  render() {
    if (!this.isInitialized) return;
    
    const ctx = this.gameEngine.ctx;
    const { width, height } = this.gameEngine.getDesignSize();
    
    // 绘制背景
    this.renderBackground(ctx, width, height);
    
    // 绘制返回按钮
    this.renderBackButton(ctx);
    
    // 绘制标题
    this.renderTitle(ctx, width);
    
    // 绘制个人数据区
    this.renderPersonalData(ctx, width);
    
    // 绘制标签栏
    this.renderTabs(ctx);
    
    // 绘制排行榜列表
    this.renderRankList(ctx, width, height);
  }

  // 绘制背景
  renderBackground(ctx, width, height) {
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, GameConfig.COLORS.PRIMARY);
    gradient.addColorStop(1, '#FFF8DC');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  }

  // 绘制返回按钮
  renderBackButton(ctx) {
    const btn = this.backButton;
    
    ctx.save();
    ctx.fillStyle = GameConfig.COLORS.BUTTON_NORMAL;
    ctx.fillRect(btn.x, btn.y, btn.width, btn.height);
    
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = 2;
    ctx.strokeRect(btn.x, btn.y, btn.width, btn.height);
    
    ctx.fillStyle = GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = GameConfig.FONTS.BUTTON;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(btn.text, btn.x + btn.width / 2, btn.y + btn.height / 2);
    ctx.restore();
  }

  // 绘制标题
  renderTitle(ctx, width) {
    ctx.save();
    ctx.fillStyle = GameConfig.COLORS.SECONDARY;
    ctx.font = GameConfig.FONTS.TITLE;
    ctx.textAlign = 'center';
    ctx.fillText('排行榜', width / 2, 80);
    ctx.restore();
  }

  // 绘制个人数据区
  renderPersonalData(ctx, width) {
    const dataY = 120;
    const dataHeight = 60;
    
    ctx.save();
    
    // 绘制背景
    ctx.fillStyle = GameConfig.COLORS.TRANSPARENT_GRAY;
    ctx.fillRect(width * 0.1, dataY, width * 0.8, dataHeight);
    
    // 绘制数据
    ctx.fillStyle = GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = '16px Arial';
    ctx.textAlign = 'left';
    
    const startX = width * 0.15;
    ctx.fillText(`最高分: ${this.personalData.highScore}`, startX, dataY + 20);
    ctx.fillText(`最高连击: ${this.personalData.bestCombo}`, startX, dataY + 40);
    
    ctx.textAlign = 'right';
    const endX = width * 0.85;
    ctx.fillText(`最佳时间: ${Utils.Time.formatTime(this.personalData.bestTime)}`, endX, dataY + 30);
    
    ctx.restore();
  }

  // 绘制标签栏
  renderTabs(ctx) {
    for (const tab of this.tabButtons) {
      this.renderTab(ctx, tab);
    }
  }

  // 绘制单个标签
  renderTab(ctx, tab) {
    ctx.save();
    
    // 绘制背景
    ctx.fillStyle = tab.isActive ? GameConfig.COLORS.SECONDARY : GameConfig.COLORS.BUTTON_NORMAL;
    ctx.fillRect(tab.x, tab.y, tab.width, tab.height);
    
    // 绘制边框
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = tab.isActive ? 3 : 1;
    ctx.strokeRect(tab.x, tab.y, tab.width, tab.height);
    
    // 绘制文字
    ctx.fillStyle = tab.isActive ? '#FFFFFF' : GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = GameConfig.FONTS.BUTTON;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(`${tab.icon} ${tab.name}`, tab.x + tab.width / 2, tab.y + tab.height / 2);
    
    ctx.restore();
  }

  // 绘制排行榜列表
  renderRankList(ctx, width, height) {
    const listStartY = 300;
    const listEndY = height - 50;
    const itemHeight = 60;
    const rankList = this.getCurrentRankList();
    
    // 设置裁剪区域
    ctx.save();
    ctx.beginPath();
    ctx.rect(0, listStartY, width, listEndY - listStartY);
    ctx.clip();
    
    // 绘制列表项
    for (let i = 0; i < rankList.length; i++) {
      const item = rankList[i];
      const itemY = listStartY + i * itemHeight - this.scrollOffset;
      
      // 只绘制可见的项目
      if (itemY + itemHeight >= listStartY && itemY <= listEndY) {
        this.renderRankItem(ctx, item, i + 1, width * 0.1, itemY, width * 0.8, itemHeight - 5);
      }
    }
    
    ctx.restore();
    
    // 绘制滚动指示器
    this.renderScrollIndicator(ctx, width, listStartY, listEndY - listStartY);
  }

  // 绘制排行榜项目
  renderRankItem(ctx, item, rank, x, y, width, height) {
    ctx.save();
    
    // 绘制背景
    ctx.fillStyle = item.isPlayer ? GameConfig.COLORS.SECONDARY : GameConfig.COLORS.TRANSPARENT_WHITE;
    ctx.fillRect(x, y, width, height);
    
    // 绘制边框
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = item.isPlayer ? 2 : 1;
    ctx.strokeRect(x, y, width, height);
    
    // 绘制排名
    ctx.fillStyle = item.isPlayer ? '#FFFFFF' : GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    const rankText = rank <= 3 ? ['🥇', '🥈', '🥉'][rank - 1] : rank.toString();
    ctx.fillText(rankText, x + 30, y + height / 2);
    
    // 绘制名称
    ctx.font = '18px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(item.name, x + 60, y + height / 2 - 8);
    
    // 绘制数值
    ctx.textAlign = 'right';
    const valueText = this.formatRankValue(item.value);
    ctx.fillText(valueText, x + width - 20, y + height / 2 - 8);
    
    // 绘制日期
    ctx.font = '12px Arial';
    ctx.fillStyle = item.isPlayer ? 'rgba(255, 255, 255, 0.8)' : GameConfig.COLORS.TEXT_SECONDARY;
    ctx.fillText(item.date, x + width - 20, y + height / 2 + 10);
    
    ctx.restore();
  }

  // 格式化排行榜数值
  formatRankValue(value) {
    switch (this.currentTab) {
      case 'score':
        return value.toLocaleString();
      case 'time':
        return Utils.Time.formatTime(value);
      case 'combo':
        return `${value}连击`;
      default:
        return value.toString();
    }
  }

  // 绘制滚动指示器
  renderScrollIndicator(ctx, width, startY, height) {
    if (this.maxScroll <= 0) return;
    
    const indicatorWidth = 4;
    const indicatorX = width - 10;
    const indicatorHeight = height * (height / (height + this.maxScroll));
    const indicatorY = startY + (this.scrollOffset / this.maxScroll) * (height - indicatorHeight);
    
    ctx.save();
    ctx.fillStyle = GameConfig.COLORS.SECONDARY;
    ctx.fillRect(indicatorX, indicatorY, indicatorWidth, indicatorHeight);
    ctx.restore();
  }

  // 处理输入开始
  onInputStart(x, y) {
    // 检查返回按钮
    if (Utils.Collision.pointInRect(x, y, this.backButton.x, this.backButton.y, 
                                   this.backButton.width, this.backButton.height)) {
      this.pageManager.goBack();
      return;
    }
    
    // 检查标签按钮
    for (const tab of this.tabButtons) {
      if (Utils.Collision.pointInRect(x, y, tab.x, tab.y, tab.width, tab.height)) {
        this.switchTab(tab.id);
        return;
      }
    }
    
    // 开始拖拽滚动
    this.isDragging = true;
    this.lastTouchY = y;
    this.velocity = 0;
  }

  // 处理输入移动
  onInputMove(x, y) {
    if (this.isDragging) {
      const deltaY = y - this.lastTouchY;
      this.scrollOffset -= deltaY;
      this.scrollOffset = Utils.Math.clamp(this.scrollOffset, 0, this.maxScroll);
      
      this.velocity = -deltaY;
      this.lastTouchY = y;
    }
  }

  // 处理输入结束
  onInputEnd() {
    this.isDragging = false;
  }

  // 切换标签
  switchTab(tabId) {
    if (this.currentTab === tabId) return;
    
    this.currentTab = tabId;
    
    // 更新标签状态
    this.tabButtons.forEach(tab => {
      tab.isActive = tab.id === tabId;
    });
    
    // 重置滚动
    this.scrollOffset = 0;
    this.velocity = 0;
    
    // 重新计算滚动范围
    this.calculateMaxScroll();
    
    // 播放音效
    this.resourceManager.playEffect(GameConfig.AUDIO.EFFECTS.CAT, 0.5);
    
    console.log(`Switched to tab: ${tabId}`);
  }

  // 保存排行榜数据
  saveRankData() {
    Utils.Storage.set(GameConfig.STORAGE_KEYS.RANK_DATA, this.rankData);
  }

  // 销毁页面
  destroy() {
    console.log('Destroying RankPage');
    
    // 保存排行榜数据
    this.saveRankData();
    
    // 销毁资源管理器
    if (this.resourceManager) {
      this.resourceManager.destroy();
      this.resourceManager = null;
    }
    
    // 清理数据
    this.personalData = {};
    this.rankData = {};
    this.tabButtons = [];
    this.backButton = null;
    this.isInitialized = false;
    
    // 清理引用
    this.gameEngine = null;
    this.pageManager = null;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RankPage;
} else if (typeof window !== 'undefined') {
  window.RankPage = RankPage;
}
