// 设置页面类
class SettingPage {
  constructor(gameEngine, pageManager) {
    this.gameEngine = gameEngine;
    this.pageManager = pageManager;
    this.resourceManager = null;
    
    // 设置数据
    this.settings = {
      backgroundVolume: 30,
      effectVolume: 70,
      isMuted: false,
      enableParticles: true,
      enableVibration: true
    };
    
    // UI元素
    this.backButton = null;
    this.sliders = [];
    this.switches = [];
    
    // 输入状态
    this.isDragging = false;
    this.dragTarget = null;
    
    // 页面状态
    this.isInitialized = false;
  }

  // 初始化页面
  async init(params = {}) {
    console.log('Initializing SettingPage');
    
    try {
      // 创建资源管理器
      this.resourceManager = new ResourceManager();
      
      // 预加载基础资源
      await this.resourceManager.preloadBasicResources();
      
      // 加载设置数据
      this.loadSettings();
      
      // 初始化UI
      this.initUI();
      
      this.isInitialized = true;
      console.log('SettingPage initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize SettingPage:', error);
      throw error;
    }
  }

  // 加载设置数据
  loadSettings() {
    const savedSettings = Utils.Storage.get(GameConfig.STORAGE_KEYS.SETTINGS, GameConfig.DEFAULT_SETTINGS);
    this.settings = { ...GameConfig.DEFAULT_SETTINGS, ...savedSettings };
    
    console.log('Settings loaded:', this.settings);
  }

  // 初始化UI
  initUI() {
    const { width, height } = this.gameEngine.getDesignSize();
    const safeAreaTop = height * GameConfig.SCREEN.SAFE_AREA_TOP;
    
    // 返回按钮
    this.backButton = {
      x: 20,
      y: safeAreaTop,
      width: 60,
      height: 40,
      text: '返回'
    };
    
    // 初始化滑块
    this.initSliders();
    
    // 初始化开关
    this.initSwitches();
  }

  // 初始化滑块
  initSliders() {
    const { width } = this.gameEngine.getDesignSize();
    const sliderWidth = width * 0.6;
    const sliderX = width * 0.2;
    let currentY = 200;
    
    this.sliders = [
      {
        id: 'backgroundVolume',
        label: '背景音量',
        x: sliderX,
        y: currentY,
        width: sliderWidth,
        height: 20,
        value: this.settings.backgroundVolume,
        min: 0,
        max: 100,
        step: 1
      },
      {
        id: 'effectVolume',
        label: '音效音量',
        x: sliderX,
        y: currentY + 80,
        width: sliderWidth,
        height: 20,
        value: this.settings.effectVolume,
        min: 0,
        max: 100,
        step: 1
      }
    ];
  }

  // 初始化开关
  initSwitches() {
    const { width } = this.gameEngine.getDesignSize();
    const switchX = width * 0.7;
    let currentY = 360;
    
    this.switches = [
      {
        id: 'isMuted',
        label: '静音模式',
        x: switchX,
        y: currentY,
        width: 60,
        height: 30,
        value: this.settings.isMuted
      },
      {
        id: 'enableParticles',
        label: '粒子效果',
        x: switchX,
        y: currentY + 60,
        width: 60,
        height: 30,
        value: this.settings.enableParticles
      },
      {
        id: 'enableVibration',
        label: '震动反馈',
        x: switchX,
        y: currentY + 120,
        width: 60,
        height: 30,
        value: this.settings.enableVibration
      }
    ];
  }

  // 更新逻辑
  update(deltaTime) {
    if (!this.isInitialized) return;
    
    // 这里可以添加动画更新逻辑
  }

  // 渲染页面
  render() {
    if (!this.isInitialized) return;
    
    const ctx = this.gameEngine.ctx;
    const { width, height } = this.gameEngine.getDesignSize();
    
    // 绘制背景
    this.renderBackground(ctx, width, height);
    
    // 绘制返回按钮
    this.renderBackButton(ctx);
    
    // 绘制标题
    this.renderTitle(ctx, width);
    
    // 绘制设置面板
    this.renderSettingsPanel(ctx, width, height);
    
    // 绘制滑块
    this.renderSliders(ctx);
    
    // 绘制开关
    this.renderSwitches(ctx);
  }

  // 绘制背景
  renderBackground(ctx, width, height) {
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, GameConfig.COLORS.PRIMARY);
    gradient.addColorStop(1, '#FFF8DC');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  }

  // 绘制返回按钮
  renderBackButton(ctx) {
    const btn = this.backButton;
    
    ctx.save();
    ctx.fillStyle = GameConfig.COLORS.BUTTON_NORMAL;
    ctx.fillRect(btn.x, btn.y, btn.width, btn.height);
    
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = 2;
    ctx.strokeRect(btn.x, btn.y, btn.width, btn.height);
    
    ctx.fillStyle = GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = GameConfig.FONTS.BUTTON;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(btn.text, btn.x + btn.width / 2, btn.y + btn.height / 2);
    ctx.restore();
  }

  // 绘制标题
  renderTitle(ctx, width) {
    ctx.save();
    ctx.fillStyle = GameConfig.COLORS.SECONDARY;
    ctx.font = GameConfig.FONTS.TITLE;
    ctx.textAlign = 'center';
    ctx.fillText('游戏设置', width / 2, 80);
    ctx.restore();
  }

  // 绘制设置面板
  renderSettingsPanel(ctx, width, height) {
    const panelX = width * 0.1;
    const panelY = 150;
    const panelWidth = width * 0.8;
    const panelHeight = height - 300;
    
    ctx.save();
    
    // 绘制面板背景
    ctx.fillStyle = GameConfig.COLORS.TRANSPARENT_WHITE;
    ctx.fillRect(panelX, panelY, panelWidth, panelHeight);
    
    // 绘制面板边框
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = 2;
    ctx.strokeRect(panelX, panelY, panelWidth, panelHeight);
    
    ctx.restore();
  }

  // 绘制滑块
  renderSliders(ctx) {
    for (const slider of this.sliders) {
      this.renderSlider(ctx, slider);
    }
  }

  // 绘制单个滑块
  renderSlider(ctx, slider) {
    ctx.save();
    
    // 绘制标签
    ctx.fillStyle = GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = GameConfig.FONTS.BUTTON;
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';
    ctx.fillText(slider.label, slider.x, slider.y - 15);
    
    // 绘制数值
    ctx.textAlign = 'right';
    ctx.fillText(`${slider.value}%`, slider.x + slider.width, slider.y - 15);
    
    // 绘制滑块轨道
    ctx.fillStyle = '#DDDDDD';
    ctx.fillRect(slider.x, slider.y, slider.width, slider.height);
    
    // 绘制滑块轨道边框
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = 1;
    ctx.strokeRect(slider.x, slider.y, slider.width, slider.height);
    
    // 绘制滑块进度
    const progressWidth = (slider.value - slider.min) / (slider.max - slider.min) * slider.width;
    ctx.fillStyle = GameConfig.COLORS.SECONDARY;
    ctx.fillRect(slider.x, slider.y, progressWidth, slider.height);
    
    // 绘制滑块手柄
    const handleX = slider.x + progressWidth - 10;
    const handleY = slider.y - 5;
    const handleWidth = 20;
    const handleHeight = slider.height + 10;
    
    ctx.fillStyle = GameConfig.COLORS.BUTTON_NORMAL;
    ctx.fillRect(handleX, handleY, handleWidth, handleHeight);
    
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = 2;
    ctx.strokeRect(handleX, handleY, handleWidth, handleHeight);
    
    ctx.restore();
  }

  // 绘制开关
  renderSwitches(ctx) {
    for (const switchItem of this.switches) {
      this.renderSwitch(ctx, switchItem);
    }
  }

  // 绘制单个开关
  renderSwitch(ctx, switchItem) {
    ctx.save();
    
    // 绘制标签
    ctx.fillStyle = GameConfig.COLORS.TEXT_PRIMARY;
    ctx.font = GameConfig.FONTS.BUTTON;
    ctx.textAlign = 'right';
    ctx.textBaseline = 'middle';
    ctx.fillText(switchItem.label, switchItem.x - 20, switchItem.y + switchItem.height / 2);
    
    // 绘制开关背景
    ctx.fillStyle = switchItem.value ? GameConfig.COLORS.SECONDARY : '#DDDDDD';
    ctx.fillRect(switchItem.x, switchItem.y, switchItem.width, switchItem.height);
    
    // 绘制开关边框
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = 2;
    ctx.strokeRect(switchItem.x, switchItem.y, switchItem.width, switchItem.height);
    
    // 绘制开关按钮
    const buttonSize = switchItem.height - 4;
    const buttonX = switchItem.value ? 
      switchItem.x + switchItem.width - buttonSize - 2 : 
      switchItem.x + 2;
    const buttonY = switchItem.y + 2;
    
    ctx.fillStyle = GameConfig.COLORS.BUTTON_NORMAL;
    ctx.fillRect(buttonX, buttonY, buttonSize, buttonSize);
    
    ctx.strokeStyle = GameConfig.COLORS.SECONDARY;
    ctx.lineWidth = 1;
    ctx.strokeRect(buttonX, buttonY, buttonSize, buttonSize);
    
    // 绘制状态文字
    ctx.fillStyle = switchItem.value ? '#FFFFFF' : GameConfig.COLORS.TEXT_SECONDARY;
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(switchItem.value ? 'ON' : 'OFF', 
                switchItem.x + switchItem.width / 2, 
                switchItem.y + switchItem.height / 2);
    
    ctx.restore();
  }

  // 处理输入开始
  onInputStart(x, y) {
    // 检查返回按钮
    if (Utils.Collision.pointInRect(x, y, this.backButton.x, this.backButton.y, 
                                   this.backButton.width, this.backButton.height)) {
      this.pageManager.goBack();
      return;
    }
    
    // 检查滑块
    for (const slider of this.sliders) {
      if (this.isPointInSlider(x, y, slider)) {
        this.isDragging = true;
        this.dragTarget = slider;
        this.updateSliderValue(slider, x);
        return;
      }
    }
    
    // 检查开关
    for (const switchItem of this.switches) {
      if (Utils.Collision.pointInRect(x, y, switchItem.x, switchItem.y, 
                                     switchItem.width, switchItem.height)) {
        this.toggleSwitch(switchItem);
        return;
      }
    }
  }

  // 处理输入移动
  onInputMove(x, y) {
    if (this.isDragging && this.dragTarget) {
      this.updateSliderValue(this.dragTarget, x);
    }
  }

  // 处理输入结束
  onInputEnd() {
    this.isDragging = false;
    this.dragTarget = null;
  }

  // 检查点是否在滑块区域内
  isPointInSlider(x, y, slider) {
    return Utils.Collision.pointInRect(x, y, slider.x - 10, slider.y - 10, 
                                      slider.width + 20, slider.height + 20);
  }

  // 更新滑块值
  updateSliderValue(slider, x) {
    const relativeX = x - slider.x;
    const percentage = Utils.Math.clamp(relativeX / slider.width, 0, 1);
    const newValue = Math.round(slider.min + percentage * (slider.max - slider.min));
    
    if (newValue !== slider.value) {
      slider.value = newValue;
      this.settings[slider.id] = newValue;
      
      // 实时保存设置
      this.saveSettings();
      
      // 播放反馈音效
      this.resourceManager.playEffect(GameConfig.AUDIO.EFFECTS.CAT, 0.3);
      
      // 如果是背景音量，实时调整
      if (slider.id === 'backgroundVolume') {
        this.updateBackgroundVolume();
      }
      
      console.log(`${slider.id} updated to ${newValue}`);
    }
  }

  // 切换开关
  toggleSwitch(switchItem) {
    switchItem.value = !switchItem.value;
    this.settings[switchItem.id] = switchItem.value;
    
    // 实时保存设置
    this.saveSettings();
    
    // 播放反馈音效
    this.resourceManager.playEffect(GameConfig.AUDIO.EFFECTS.CAT, 0.5);
    
    // 触发震动（如果启用）
    if (this.settings.enableVibration) {
      Utils.Device.vibrate();
    }
    
    // 特殊处理
    if (switchItem.id === 'isMuted') {
      this.updateMuteState();
    }
    
    console.log(`${switchItem.id} toggled to ${switchItem.value}`);
  }

  // 更新背景音量
  updateBackgroundVolume() {
    const bgAudio = this.resourceManager.getAudio(GameConfig.AUDIO.BACKGROUND);
    if (bgAudio && !this.settings.isMuted) {
      bgAudio.volume = this.settings.backgroundVolume / 100;
    }
  }

  // 更新静音状态
  updateMuteState() {
    const bgAudio = this.resourceManager.getAudio(GameConfig.AUDIO.BACKGROUND);
    if (bgAudio) {
      if (this.settings.isMuted) {
        bgAudio.pause();
      } else {
        bgAudio.volume = this.settings.backgroundVolume / 100;
        bgAudio.play();
      }
    }
  }

  // 保存设置
  saveSettings() {
    Utils.Storage.set(GameConfig.STORAGE_KEYS.SETTINGS, this.settings);
    console.log('Settings saved:', this.settings);
  }

  // 重置设置
  resetSettings() {
    this.settings = { ...GameConfig.DEFAULT_SETTINGS };
    
    // 更新UI
    for (const slider of this.sliders) {
      slider.value = this.settings[slider.id];
    }
    
    for (const switchItem of this.switches) {
      switchItem.value = this.settings[switchItem.id];
    }
    
    // 保存设置
    this.saveSettings();
    
    // 应用设置
    this.updateBackgroundVolume();
    this.updateMuteState();
    
    console.log('Settings reset to default');
  }

  // 销毁页面
  destroy() {
    console.log('Destroying SettingPage');
    
    // 确保设置已保存
    this.saveSettings();
    
    // 销毁资源管理器
    if (this.resourceManager) {
      this.resourceManager.destroy();
      this.resourceManager = null;
    }
    
    // 清理数据
    this.settings = {};
    this.sliders = [];
    this.switches = [];
    this.backButton = null;
    this.dragTarget = null;
    this.isInitialized = false;
    
    // 清理引用
    this.gameEngine = null;
    this.pageManager = null;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SettingPage;
} else if (typeof window !== 'undefined') {
  window.SettingPage = SettingPage;
}
