// 安全初始化 - 避免 this 上下文问题
console.log('=== 安全初始化开始 ===');

// 全局变量检查和修复函数
function checkAndFixGlobals() {
  console.log('检查和修复全局变量...');
  
  var fixes = [];
  
  // 检查 GameConfig
  if (typeof GameConfig === 'undefined') {
    // 尝试从其他位置获取
    if (typeof window !== 'undefined' && window.GameConfig) {
      GameConfig = window.GameConfig;
      fixes.push('GameConfig from window');
    } else if (typeof global !== 'undefined' && global.GameConfig) {
      GameConfig = global.GameConfig;
      fixes.push('GameConfig from global');
    } else if (typeof globalThis !== 'undefined' && globalThis.GameConfig) {
      GameConfig = globalThis.GameConfig;
      fixes.push('GameConfig from globalThis');
    } else {
      console.error('❌ 无法找到 GameConfig');
      return false;
    }
  } else {
    console.log('✅ GameConfig 可用');
  }
  
  // 检查 Utils
  if (typeof Utils === 'undefined') {
    if (typeof window !== 'undefined' && window.Utils) {
      Utils = window.Utils;
      fixes.push('Utils from window');
    } else if (typeof global !== 'undefined' && global.Utils) {
      Utils = global.Utils;
      fixes.push('Utils from global');
    } else if (typeof globalThis !== 'undefined' && globalThis.Utils) {
      Utils = globalThis.Utils;
      fixes.push('Utils from globalThis');
    } else {
      console.error('❌ 无法找到 Utils');
      return false;
    }
  } else {
    console.log('✅ Utils 可用');
  }
  
  // 检查 GameEngine
  if (typeof GameEngine === 'undefined') {
    if (typeof window !== 'undefined' && window.GameEngine) {
      GameEngine = window.GameEngine;
      fixes.push('GameEngine from window');
    } else if (typeof global !== 'undefined' && global.GameEngine) {
      GameEngine = global.GameEngine;
      fixes.push('GameEngine from global');
    } else if (typeof globalThis !== 'undefined' && globalThis.GameEngine) {
      GameEngine = globalThis.GameEngine;
      fixes.push('GameEngine from globalThis');
    } else {
      console.error('❌ 无法找到 GameEngine');
      return false;
    }
  } else {
    console.log('✅ GameEngine 可用');
  }
  
  // 检查其他核心类
  var coreClasses = ['PageManager', 'ResourceManager', 'HomePage'];
  for (var i = 0; i < coreClasses.length; i++) {
    var className = coreClasses[i];
    if (typeof window[className] === 'undefined') {
      if (typeof window !== 'undefined' && window[className]) {
        window[className] = window[className];
        fixes.push(className + ' from window');
      } else if (typeof global !== 'undefined' && global[className]) {
        window[className] = global[className];
        fixes.push(className + ' from global');
      }
    }
  }
  
  if (fixes.length > 0) {
    console.log('✅ 修复了以下引用:', fixes);
  }
  
  return true;
}

// 创建安全的游戏初始化函数
function safeInitGame() {
  console.log('开始安全初始化游戏...');
  
  // 检查抖音API
  if (typeof tt === 'undefined') {
    console.error('❌ 抖音小游戏API不可用');
    return false;
  }
  
  // 检查和修复全局变量
  if (!checkAndFixGlobals()) {
    console.error('❌ 核心组件缺失，无法启动游戏');
    return false;
  }
  
  // 验证配置
  try {
    console.log('验证配置...');
    console.log('   游戏版本:', GameConfig.GAME.VERSION);
    console.log('   网格尺寸:', GameConfig.GRID.ROWS + 'x' + GameConfig.GRID.COLS);
    
    // 测试工具类
    var testNum = Utils.Math.randomInt(1, 5);
    console.log('   工具类测试:', testNum);
    
  } catch (e) {
    console.error('❌ 配置验证失败:', e.message);
    return false;
  }
  
  console.log('✅ 安全初始化检查通过');
  return true;
}

// 延迟执行安全初始化
setTimeout(function() {
  if (safeInitGame()) {
    console.log('🎉 可以安全启动游戏！');
    
    // 如果原始初始化函数存在，调用它
    if (typeof initGame === 'function') {
      console.log('调用原始初始化函数...');
      initGame().catch(function(error) {
        console.error('原始初始化失败:', error);
        
        // 尝试创建最小化游戏实例
        console.log('尝试创建最小化游戏实例...');
        try {
          if (typeof Game !== 'undefined') {
            var gameInstance = new Game();
            console.log('✅ 游戏实例创建成功');
          }
        } catch (e) {
          console.error('❌ 最小化游戏实例创建失败:', e);
        }
      });
    }
  } else {
    console.error('❌ 安全初始化失败');
  }
}, 300);

// 导出安全初始化函数
if (typeof window !== 'undefined') {
  window.safeInitGame = safeInitGame;
  window.checkAndFixGlobals = checkAndFixGlobals;
}

console.log('=== 安全初始化脚本加载完成 ===');
