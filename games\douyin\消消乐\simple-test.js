// 简化测试 - 只测试基础配置和工具类
console.log('=== 简化测试开始 ===');

// 延迟执行，确保脚本加载完成
setTimeout(() => {
  console.log('开始测试基础组件...');
  
  // 测试 GameConfig
  try {
    if (typeof GameConfig !== 'undefined') {
      console.log('✅ GameConfig 可用');
      console.log('   - 游戏版本:', GameConfig.GAME.VERSION);
      console.log('   - 网格尺寸:', GameConfig.GRID.ROWS + 'x' + GameConfig.GRID.COLS);
      console.log('   - 关卡数量:', GameConfig.LEVELS.length);
    } else {
      console.error('❌ GameConfig 不可用');
    }
  } catch (error) {
    console.error('❌ GameConfig 测试失败:', error);
  }
  
  // 测试 Utils
  try {
    if (typeof Utils !== 'undefined') {
      console.log('✅ Utils 可用');
      
      // 测试数学工具
      const randomNum = Utils.Math.randomInt(1, 10);
      console.log('   - 随机数测试:', randomNum);
      
      // 测试数组工具
      const testArray = [1, 2, 3, 4, 5];
      const shuffled = Utils.Array.shuffle(testArray);
      console.log('   - 数组打乱测试:', shuffled);
      
      // 测试时间工具
      const timeStr = Utils.Time.formatTime(125);
      console.log('   - 时间格式化测试:', timeStr);
      
    } else {
      console.error('❌ Utils 不可用');
    }
  } catch (error) {
    console.error('❌ Utils 测试失败:', error);
  }
  
  // 测试其他类的存在性
  const classChecks = {
    GameEngine: typeof GameEngine !== 'undefined',
    PageManager: typeof PageManager !== 'undefined',
    ResourceManager: typeof ResourceManager !== 'undefined',
    HomePage: typeof HomePage !== 'undefined',
    GamePage: typeof GamePage !== 'undefined',
    RankPage: typeof RankPage !== 'undefined',
    SettingPage: typeof SettingPage !== 'undefined'
  };

  for (const [className, isLoaded] of Object.entries(classChecks)) {
    if (isLoaded) {
      console.log(`✅ ${className} 类已定义`);
    } else {
      console.error(`❌ ${className} 类未定义`);
    }
  }
  
  console.log('=== 简化测试完成 ===');
  
}, 200);

// 导出测试函数
if (typeof this !== 'undefined') {
  this.runSimpleTest = () => {
    console.log('手动运行简化测试...');
    
    // 立即检查当前状态
    console.log('当前全局对象包含:');
    const globalKeys = Object.keys(this).filter(key => 
      key.includes('Config') || 
      key.includes('Utils') || 
      key.includes('Engine') || 
      key.includes('Manager') || 
      key.includes('Page')
    );
    console.log(globalKeys);
    
    // 检查特定对象
    console.log('GameConfig 类型:', typeof GameConfig);
    console.log('Utils 类型:', typeof Utils);
    console.log('GameEngine 类型:', typeof GameEngine);
  };
}
