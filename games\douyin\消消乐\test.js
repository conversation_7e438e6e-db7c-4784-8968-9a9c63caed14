// 测试文件 - 验证所有组件是否正确加载
console.log('=== 萌宠爱消消 组件测试 ===');

// 测试配置文件
function testConfig() {
  console.log('\n1. 测试配置文件...');
  
  if (typeof GameConfig === 'undefined') {
    console.error('❌ GameConfig 未加载');
    return false;
  }
  
  // 检查主要配置项
  const requiredConfigs = [
    'GAME', 'GRID', 'SCREEN', 'LEVELS', 'ANIMALS', 
    'SCORE', 'PROPS', 'AUDIO', 'ANIMATION', 'COLORS'
  ];
  
  for (const config of requiredConfigs) {
    if (!GameConfig[config]) {
      console.error(`❌ GameConfig.${config} 缺失`);
      return false;
    }
  }
  
  console.log('✅ GameConfig 配置完整');
  console.log(`   - 关卡数量: ${GameConfig.LEVELS.length}`);
  console.log(`   - 动物种类: ${GameConfig.ANIMALS.length}`);
  console.log(`   - 道具种类: ${GameConfig.PROPS.length}`);
  
  return true;
}

// 测试工具类
function testUtils() {
  console.log('\n2. 测试工具类...');
  
  if (typeof Utils === 'undefined') {
    console.error('❌ Utils 未加载');
    return false;
  }
  
  // 检查主要工具模块
  const requiredUtils = [
    'Math', 'Array', 'Color', 'Storage', 'Time', 
    'Image', 'Audio', 'Performance', 'Collision', 'Device'
  ];
  
  for (const util of requiredUtils) {
    if (!Utils[util]) {
      console.error(`❌ Utils.${util} 缺失`);
      return false;
    }
  }
  
  console.log('✅ Utils 工具类完整');
  
  // 测试一些基本功能
  try {
    const randomInt = Utils.Math.randomInt(1, 10);
    const distance = Utils.Math.distance(0, 0, 3, 4);
    const shuffled = Utils.Array.shuffle([1, 2, 3, 4, 5]);
    
    console.log(`   - 随机数测试: ${randomInt}`);
    console.log(`   - 距离计算测试: ${distance}`);
    console.log(`   - 数组打乱测试: [${shuffled.join(', ')}]`);
  } catch (error) {
    console.error('❌ Utils 功能测试失败:', error);
    return false;
  }
  
  return true;
}

// 测试核心类
function testCoreClasses() {
  console.log('\n3. 测试核心类...');
  
  const coreClasses = [
    'GameEngine', 'PageManager', 'ResourceManager'
  ];
  
  for (const className of coreClasses) {
    if (typeof window[className] === 'undefined') {
      console.error(`❌ ${className} 未加载`);
      return false;
    }
    
    // 检查是否可以实例化
    try {
      if (className === 'GameEngine') {
        // GameEngine 需要特殊处理，因为它依赖 tt API
        console.log(`✅ ${className} 类定义正确`);
      } else if (className === 'PageManager') {
        // PageManager 需要 GameEngine 实例
        console.log(`✅ ${className} 类定义正确`);
      } else if (className === 'ResourceManager') {
        const instance = new window[className]();
        console.log(`✅ ${className} 可以实例化`);
      }
    } catch (error) {
      console.error(`❌ ${className} 实例化失败:`, error);
      return false;
    }
  }
  
  return true;
}

// 测试页面类
function testPageClasses() {
  console.log('\n4. 测试页面类...');
  
  const pageClasses = [
    'HomePage', 'GamePage', 'RankPage', 'SettingPage'
  ];
  
  for (const className of pageClasses) {
    if (typeof window[className] === 'undefined') {
      console.error(`❌ ${className} 未加载`);
      return false;
    }
    
    console.log(`✅ ${className} 类定义正确`);
  }
  
  return true;
}

// 测试特效类
function testEffectClasses() {
  console.log('\n5. 测试特效类...');
  
  const effectClasses = [
    'ParticleSystem', 'AudioManager'
  ];
  
  for (const className of effectClasses) {
    if (typeof window[className] === 'undefined') {
      console.error(`❌ ${className} 未加载`);
      return false;
    }
    
    try {
      const instance = new window[className]();
      console.log(`✅ ${className} 可以实例化`);
    } catch (error) {
      console.error(`❌ ${className} 实例化失败:`, error);
      return false;
    }
  }
  
  return true;
}

// 测试资源文件
function testResources() {
  console.log('\n6. 测试资源配置...');
  
  // 检查图片资源配置
  let missingImages = 0;
  console.log('   检查动物图片配置:');
  for (const animal of GameConfig.ANIMALS) {
    if (!animal.image || !animal.name || !animal.color) {
      console.error(`   ❌ 动物配置不完整: ${animal.name || 'unknown'}`);
      missingImages++;
    }
  }
  
  console.log('   检查道具图片配置:');
  for (const prop of GameConfig.PROPS) {
    if (!prop.image || !prop.name) {
      console.error(`   ❌ 道具配置不完整: ${prop.name || 'unknown'}`);
      missingImages++;
    }
  }
  
  // 检查音频资源配置
  let missingAudios = 0;
  console.log('   检查音频配置:');
  if (!GameConfig.AUDIO.BACKGROUND) {
    console.error('   ❌ 背景音乐配置缺失');
    missingAudios++;
  }
  
  for (const [key, src] of Object.entries(GameConfig.AUDIO.EFFECTS)) {
    if (!src) {
      console.error(`   ❌ 音效配置缺失: ${key}`);
      missingAudios++;
    }
  }
  
  if (missingImages === 0 && missingAudios === 0) {
    console.log('✅ 资源配置完整');
    return true;
  } else {
    console.error(`❌ 资源配置不完整: ${missingImages} 个图片问题, ${missingAudios} 个音频问题`);
    return false;
  }
}

// 测试游戏逻辑
function testGameLogic() {
  console.log('\n7. 测试游戏逻辑...');
  
  try {
    // 测试关卡配置
    for (const level of GameConfig.LEVELS) {
      if (!level.id || !level.name || !level.targetScore || !level.animalTypes) {
        console.error(`❌ 关卡配置不完整: ${level.name || 'unknown'}`);
        return false;
      }
    }
    
    // 测试分数配置
    if (!GameConfig.SCORE.MATCH_3 || !GameConfig.SCORE.MATCH_4 || !GameConfig.SCORE.MATCH_5) {
      console.error('❌ 分数配置不完整');
      return false;
    }
    
    // 测试网格配置
    if (!GameConfig.GRID.ROWS || !GameConfig.GRID.COLS) {
      console.error('❌ 网格配置不完整');
      return false;
    }
    
    console.log('✅ 游戏逻辑配置正确');
    console.log(`   - 网格尺寸: ${GameConfig.GRID.ROWS}×${GameConfig.GRID.COLS}`);
    console.log(`   - 关卡数量: ${GameConfig.LEVELS.length}`);
    console.log(`   - 分数配置: ${GameConfig.SCORE.MATCH_3}/${GameConfig.SCORE.MATCH_4}/${GameConfig.SCORE.MATCH_5}`);
    
    return true;
  } catch (error) {
    console.error('❌ 游戏逻辑测试失败:', error);
    return false;
  }
}

// 运行所有测试
function runAllTests() {
  console.log('开始组件测试...\n');
  
  const tests = [
    testConfig,
    testUtils,
    testCoreClasses,
    testPageClasses,
    testEffectClasses,
    testResources,
    testGameLogic
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      if (test()) {
        passedTests++;
      }
    } catch (error) {
      console.error('❌ 测试执行失败:', error);
    }
  }
  
  console.log('\n=== 测试结果 ===');
  console.log(`通过: ${passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！游戏组件加载正常。');
    console.log('\n📋 下一步:');
    console.log('1. 在抖音开发者工具中运行游戏');
    console.log('2. 测试游戏功能是否正常');
    console.log('3. 检查性能和用户体验');
    return true;
  } else {
    console.log('⚠️  部分测试失败，请检查相关组件。');
    return false;
  }
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== 'undefined') {
  // 延迟执行，确保所有脚本都已加载
  setTimeout(runAllTests, 100);
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests };
} else if (typeof window !== 'undefined') {
  window.runGameTests = runAllTests;
}
