// 简化工具类 - 确保在抖音小游戏环境中正确加载
console.log('开始加载简化工具类...');

// 使用立即执行函数创建工具类
(function() {
  'use strict';
  
  // 获取全局对象
  function getGlobalObject() {
    try {
      return globalThis;
    } catch (e) {
      try {
        return window;
      } catch (e) {
        try {
          return global;
        } catch (e) {
          try {
            return Function('return this')();
          } catch (e) {
            return {};
          }
        }
      }
    }
  }
  
  var globalObj = getGlobalObject();
  
  // 创建工具类
  var Utils = {
    // 数学工具
    Math: {
      // 生成指定范围内的随机整数
      randomInt: function(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
      },
      
      // 生成指定范围内的随机浮点数
      randomFloat: function(min, max) {
        return Math.random() * (max - min) + min;
      },
      
      // 限制数值在指定范围内
      clamp: function(value, min, max) {
        return Math.min(Math.max(value, min), max);
      },
      
      // 计算两点间距离
      distance: function(x1, y1, x2, y2) {
        var dx = x2 - x1;
        var dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
      },
      
      // 线性插值
      lerp: function(start, end, t) {
        return start + (end - start) * t;
      }
    },
    
    // 数组工具
    Array: {
      // 打乱数组
      shuffle: function(array) {
        var result = array.slice();
        for (var i = result.length - 1; i > 0; i--) {
          var j = Math.floor(Math.random() * (i + 1));
          var temp = result[i];
          result[i] = result[j];
          result[j] = temp;
        }
        return result;
      },
      
      // 随机选择数组元素
      randomElement: function(array) {
        return array[Math.floor(Math.random() * array.length)];
      },
      
      // 移除数组元素
      remove: function(array, element) {
        var index = array.indexOf(element);
        if (index > -1) {
          array.splice(index, 1);
        }
        return array;
      }
    },
    
    // 颜色工具
    Color: {
      // 十六进制转RGB
      hexToRgb: function(hex) {
        var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16)
        } : null;
      },
      
      // RGB转十六进制
      rgbToHex: function(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
      }
    },
    
    // 存储工具
    Storage: {
      // 获取存储值
      get: function(key, defaultValue) {
        try {
          if (typeof tt !== 'undefined' && tt.getStorageSync) {
            var value = tt.getStorageSync(key);
            return value !== undefined ? value : defaultValue;
          }
          return defaultValue;
        } catch (e) {
          console.warn('Storage get failed:', e);
          return defaultValue;
        }
      },
      
      // 设置存储值
      set: function(key, value) {
        try {
          if (typeof tt !== 'undefined' && tt.setStorageSync) {
            tt.setStorageSync(key, value);
            return true;
          }
          return false;
        } catch (e) {
          console.warn('Storage set failed:', e);
          return false;
        }
      },
      
      // 移除存储值
      remove: function(key) {
        try {
          if (typeof tt !== 'undefined' && tt.removeStorageSync) {
            tt.removeStorageSync(key);
            return true;
          }
          return false;
        } catch (e) {
          console.warn('Storage remove failed:', e);
          return false;
        }
      }
    },
    
    // 时间工具
    Time: {
      // 格式化时间（秒转为 mm:ss）
      formatTime: function(seconds) {
        var minutes = Math.floor(seconds / 60);
        var remainingSeconds = seconds % 60;
        return minutes + ':' + (remainingSeconds < 10 ? '0' : '') + remainingSeconds;
      },
      
      // 获取当前时间戳
      now: function() {
        return Date.now();
      }
    },
    
    // 碰撞检测工具
    Collision: {
      // 点是否在矩形内
      pointInRect: function(x, y, rectX, rectY, rectWidth, rectHeight) {
        return x >= rectX && x <= rectX + rectWidth && 
               y >= rectY && y <= rectY + rectHeight;
      },
      
      // 两个矩形是否相交
      rectIntersect: function(x1, y1, w1, h1, x2, y2, w2, h2) {
        return x1 < x2 + w2 && x1 + w1 > x2 && y1 < y2 + h2 && y1 + h1 > y2;
      }
    },
    
    // 设备工具
    Device: {
      // 触发震动
      vibrate: function(duration) {
        try {
          if (typeof tt !== 'undefined' && tt.vibrateShort) {
            tt.vibrateShort();
          }
        } catch (e) {
          console.warn('Vibrate failed:', e);
        }
      },
      
      // 获取系统信息
      getSystemInfo: function() {
        try {
          if (typeof tt !== 'undefined' && tt.getSystemInfoSync) {
            return tt.getSystemInfoSync();
          }
          return {
            windowWidth: 375,
            windowHeight: 667,
            model: 'Unknown',
            system: 'Unknown'
          };
        } catch (e) {
          console.warn('Get system info failed:', e);
          return {
            windowWidth: 375,
            windowHeight: 667,
            model: 'Unknown',
            system: 'Unknown'
          };
        }
      }
    }
  };
  
  // 强制设置到全局作用域
  try {
    // 方法1: 直接赋值
    if (typeof Utils === 'undefined') {
      Utils = Utils;
    }
    
    // 方法2: 设置到全局对象
    if (globalObj) {
      globalObj.Utils = Utils;
    }
    
    // 方法3: 使用 eval（如果支持）
    try {
      eval('var Utils = arguments[0];')(Utils);
    } catch (e) {
      // eval 不支持，忽略
    }
    
    console.log('✅ 简化工具类加载成功');
    
  } catch (error) {
    console.error('❌ 简化工具类设置失败:', error);
  }
  
  // 验证设置结果
  setTimeout(function() {
    if (typeof Utils !== 'undefined') {
      console.log('✅ Utils 全局验证成功');
      
      // 测试基本功能
      try {
        var testNum = Utils.Math.randomInt(1, 5);
        console.log('   随机数测试:', testNum);
      } catch (e) {
        console.log('   功能测试失败:', e.message);
      }
    } else {
      console.log('❌ Utils 全局验证失败');
    }
  }, 50);
  
})();

console.log('简化工具类脚本加载完成');
