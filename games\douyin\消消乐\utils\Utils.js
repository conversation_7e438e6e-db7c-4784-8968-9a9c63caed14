// 工具类 - 提供通用的工具函数
var Utils = class Utils {
  // 数学工具
  static Math = {
    // 线性插值
    lerp(start, end, t) {
      return start + (end - start) * t;
    },

    // 限制数值范围
    clamp(value, min, max) {
      return Math.min(Math.max(value, min), max);
    },

    // 随机整数
    randomInt(min, max) {
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },

    // 随机浮点数
    randomFloat(min, max) {
      return Math.random() * (max - min) + min;
    },

    // 距离计算
    distance(x1, y1, x2, y2) {
      const dx = x2 - x1;
      const dy = y2 - y1;
      return Math.sqrt(dx * dx + dy * dy);
    },

    // 角度转弧度
    degToRad(degrees) {
      return degrees * Math.PI / 180;
    },

    // 弧度转角度
    radToDeg(radians) {
      return radians * 180 / Math.PI;
    },

    // 缓动函数
    easeInOut(t) {
      return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    },

    easeOut(t) {
      return 1 - Math.pow(1 - t, 3);
    },

    easeIn(t) {
      return t * t * t;
    }
  };

  // 数组工具
  static Array = {
    // 打乱数组
    shuffle(array) {
      const result = [...array];
      for (let i = result.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [result[i], result[j]] = [result[j], result[i]];
      }
      return result;
    },

    // 随机选择元素
    randomElement(array) {
      return array[Math.floor(Math.random() * array.length)];
    },

    // 移除元素
    remove(array, element) {
      const index = array.indexOf(element);
      if (index > -1) {
        array.splice(index, 1);
      }
      return array;
    },

    // 创建二维数组
    create2D(rows, cols, defaultValue = null) {
      return Array(rows).fill().map(() => Array(cols).fill(defaultValue));
    }
  };

  // 颜色工具
  static Color = {
    // 十六进制转RGB
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    },

    // RGB转十六进制
    rgbToHex(r, g, b) {
      return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    },

    // 颜色插值
    lerpColor(color1, color2, t) {
      const c1 = this.hexToRgb(color1);
      const c2 = this.hexToRgb(color2);
      if (!c1 || !c2) return color1;

      const r = Math.round(Utils.Math.lerp(c1.r, c2.r, t));
      const g = Math.round(Utils.Math.lerp(c1.g, c2.g, t));
      const b = Math.round(Utils.Math.lerp(c1.b, c2.b, t));
      
      return this.rgbToHex(r, g, b);
    }
  };

  // 存储工具
  static Storage = {
    // 设置数据
    set(key, value) {
      try {
        const data = JSON.stringify(value);
        tt.setStorageSync(key, data);
        return true;
      } catch (error) {
        console.error('Storage set error:', error);
        return false;
      }
    },

    // 获取数据
    get(key, defaultValue = null) {
      try {
        const data = tt.getStorageSync(key);
        return data ? JSON.parse(data) : defaultValue;
      } catch (error) {
        console.error('Storage get error:', error);
        return defaultValue;
      }
    },

    // 移除数据
    remove(key) {
      try {
        tt.removeStorageSync(key);
        return true;
      } catch (error) {
        console.error('Storage remove error:', error);
        return false;
      }
    },

    // 清空所有数据
    clear() {
      try {
        tt.clearStorageSync();
        return true;
      } catch (error) {
        console.error('Storage clear error:', error);
        return false;
      }
    }
  };

  // 时间工具
  static Time = {
    // 格式化时间（秒转分:秒）
    formatTime(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // 获取当前时间戳
    now() {
      return Date.now();
    },

    // 延迟执行
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }
  };

  // 图片工具
  static Image = {
    // 加载图片
    load(src) {
      return new Promise((resolve, reject) => {
        const image = tt.createImage();
        image.onload = () => resolve(image);
        image.onerror = reject;
        image.src = src;
      });
    },

    // 批量加载图片
    async loadBatch(sources) {
      const promises = sources.map(src => this.load(src));
      try {
        return await Promise.all(promises);
      } catch (error) {
        console.error('Batch image load error:', error);
        throw error;
      }
    }
  };

  // 音频工具
  static Audio = {
    // 创建音频实例
    create(src, options = {}) {
      const audio = tt.createInnerAudioContext();
      audio.src = src;
      audio.volume = options.volume || 1.0;
      audio.loop = options.loop || false;
      return audio;
    },

    // 播放音效
    playEffect(src, volume = 1.0) {
      const audio = this.create(src, { volume });
      audio.play();
      
      // 播放完成后销毁
      audio.onEnded(() => {
        audio.destroy();
      });
      
      return audio;
    }
  };

  // 性能工具
  static Performance = {
    // 性能监控
    monitor: {
      startTime: 0,
      endTime: 0,
      
      start() {
        this.startTime = performance.now();
      },
      
      end(label = 'Operation') {
        this.endTime = performance.now();
        const duration = this.endTime - this.startTime;
        console.log(`${label} took ${duration.toFixed(2)}ms`);
        return duration;
      }
    },

    // 节流函数
    throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },

    // 防抖函数
    debounce(func, delay) {
      let timeoutId;
      return function() {
        const args = arguments;
        const context = this;
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(context, args), delay);
      };
    }
  };

  // 碰撞检测工具
  static Collision = {
    // 点与矩形碰撞
    pointInRect(px, py, rx, ry, rw, rh) {
      return px >= rx && px <= rx + rw && py >= ry && py <= ry + rh;
    },

    // 矩形与矩形碰撞
    rectIntersect(r1x, r1y, r1w, r1h, r2x, r2y, r2w, r2h) {
      return !(r1x > r2x + r2w || r1x + r1w < r2x || r1y > r2y + r2h || r1y + r1h < r2y);
    },

    // 圆与圆碰撞
    circleIntersect(c1x, c1y, c1r, c2x, c2y, c2r) {
      const dx = c1x - c2x;
      const dy = c1y - c2y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      return distance < c1r + c2r;
    }
  };

  // 设备信息工具
  static Device = {
    // 获取设备信息
    getInfo() {
      return tt.getSystemInfoSync();
    },

    // 是否为低端设备
    isLowEndDevice() {
      const info = this.getInfo();
      // 简单判断：基于内存大小
      return info.memorySize && info.memorySize < GameConfig.PERFORMANCE.LOW_END_THRESHOLD;
    },

    // 触发震动
    vibrate(duration = 15) {
      if (tt.vibrateShort) {
        tt.vibrateShort();
      }
    }
  };
}

// 导出工具类到全局作用域
(function() {
  try {
    // 确保 Utils 在全局作用域中可用
    this.Utils = Utils;

    if (typeof global !== 'undefined') {
      global.Utils = Utils;
    }

    if (typeof window !== 'undefined') {
      window.Utils = Utils;
    }

    if (typeof globalThis !== 'undefined') {
      globalThis.Utils = Utils;
    }

    console.log('Utils export completed');
    console.log('Utils type check:', typeof Utils);

  } catch (error) {
    console.error('Utils export error:', error);
  }
})();

setTimeout(() => {
  console.log('Utils final check:', typeof Utils !== 'undefined');
}, 10);
