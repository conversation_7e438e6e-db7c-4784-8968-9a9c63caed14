# 萌宠爱消消 - 启动说明

## 📋 项目概述

这是一个完整的抖音小游戏消消乐项目，严格按照设计文档要求实现，包含完整的游戏功能、性能优化和用户体验。

## 🚀 快速启动

### 1. 环境准备

1. **下载抖音开发者工具**
   - 访问：https://developer.open-douyin.com/docs/resource/zh-CN/mini-game/develop/developer-instrument/download
   - 下载并安装最新版本的抖音开发者工具

2. **项目导入**
   - 打开抖音开发者工具
   - 选择"导入项目"
   - 选择 `games/douyin/消消乐` 文件夹
   - 输入项目名称：萌宠爱消消

### 2. 项目配置

项目已包含完整的配置文件：

- `game.json` - 游戏基础配置
- `project.config.json` - 项目配置
- `config.js` - 游戏参数配置

### 3. 文件结构检查

确保以下文件都存在：

```
✅ config.js                 # 游戏配置
✅ game.js                   # 主入口文件
✅ core/GameEngine.js        # 游戏引擎
✅ core/PageManager.js       # 页面管理
✅ core/ResourceManager.js   # 资源管理
✅ pages/HomePage.js         # 主页面
✅ pages/GamePage.js         # 游戏页面
✅ pages/RankPage.js         # 排行榜页面
✅ pages/SettingPage.js      # 设置页面
✅ utils/Utils.js            # 工具类
✅ effects/ParticleSystem.js # 粒子系统
✅ effects/AudioManager.js   # 音效管理
✅ images/                   # 图片资源
✅ audios/                   # 音频资源
```

### 4. 运行游戏

1. 在抖音开发者工具中点击"编译"
2. 等待编译完成
3. 在模拟器中查看游戏效果
4. 可以使用真机调试测试实际效果

## 🎮 游戏功能

### 主要页面

1. **主页面** - 游戏入口，包含开始游戏、排行榜、设置按钮
2. **游戏页面** - 核心游戏逻辑，8×10网格消消乐
3. **排行榜页面** - 分数榜、时间榜、连击榜
4. **设置页面** - 音量控制、静音开关等

### 核心功能

- ✅ 8×10网格消消乐游戏
- ✅ 三消、四消、五消机制
- ✅ 连击系统和倍率加成
- ✅ 4种道具系统（刷新、炸弹、清屏、降级）
- ✅ 3个难度关卡
- ✅ 完整音效系统
- ✅ 粒子特效系统
- ✅ 排行榜和数据存储
- ✅ 设置和音量控制
- ✅ 屏幕自适应
- ✅ 性能优化

## 🔧 技术特性

### 性能优化

- **帧率稳定**: 目标50FPS，实时监控和优化
- **内存管理**: 页面切换时自动销毁资源
- **对象池**: 粒子系统使用对象池减少GC
- **异步加载**: 资源分批加载，避免阻塞
- **动态适配**: 根据设备性能调整特效质量

### 屏幕适配

- 支持各种屏幕尺寸和分辨率
- 自动计算缩放比例和偏移量
- 保持游戏元素比例和可用性
- 安全区域适配

### 数据持久化

- 最高分记录
- 最佳连击记录
- 最快通关时间
- 游戏设置（音量、静音等）
- 排行榜数据

## 🎯 游戏配置

### 关卡配置

在 `config.js` 中可以调整关卡参数：

```javascript
LEVELS: [
  {
    id: 1,
    name: '萌宠新手村',
    targetScore: 1000,    // 目标分数
    animalTypes: 5,       // 动物种类数
    timeLimit: 180        // 时间限制(秒)
  }
  // 可以添加更多关卡
]
```

### 网格配置

```javascript
GRID: {
  ROWS: 10,           // 网格行数
  COLS: 8,            // 网格列数
  CELL_SIZE: 64,      // 基础格子大小
  PADDING: 10         // 格子间距
}
```

### 分数配置

```javascript
SCORE: {
  MATCH_3: 30,        // 三消分数
  MATCH_4: 40,        // 四消分数
  MATCH_5: 50,        // 五消分数
  COMBO_MULTIPLIER: [1.0, 1.5, 2.0, 2.5, 3.0]  // 连击倍率
}
```

## 🐛 调试模式

### 启用调试信息

在游戏运行后，可以通过以下方式启用调试模式：

```javascript
// 在控制台中执行
gameInstance.gameEngine.showDebugInfo = true;
```

### 调试信息包含

- 当前FPS
- 缩放比例
- 屏幕偏移量
- 内存使用情况
- 粒子数量统计

## 📱 测试建议

### 功能测试

1. **页面切换** - 测试所有页面间的跳转
2. **游戏逻辑** - 测试消除、连击、道具功能
3. **音效系统** - 测试背景音乐和音效播放
4. **设置功能** - 测试音量调节和静音功能
5. **数据存储** - 测试分数保存和排行榜

### 性能测试

1. **帧率监控** - 观察FPS是否稳定在50+
2. **内存使用** - 检查内存是否有泄漏
3. **页面切换** - 确保切换时资源正确释放
4. **长时间运行** - 测试游戏长时间运行的稳定性

### 兼容性测试

1. **不同设备** - 在不同型号手机上测试
2. **不同分辨率** - 测试屏幕适配效果
3. **低端设备** - 在低配置设备上测试性能

## 🔍 常见问题

### Q: 游戏启动失败怎么办？

A: 检查以下几点：
1. 确保所有文件都已正确放置
2. 检查抖音开发者工具版本是否最新
3. 查看控制台错误信息
4. 确认项目配置文件正确

### Q: 音效无法播放？

A: 可能的原因：
1. 音频文件路径不正确
2. 音频格式不支持
3. 设备静音或音量为0
4. 抖音小游戏音频权限问题

### Q: 游戏卡顿怎么办？

A: 优化建议：
1. 减少粒子数量限制
2. 降低动画帧率
3. 检查是否有内存泄漏
4. 在低端设备上禁用部分特效

### Q: 屏幕适配问题？

A: 检查要点：
1. 确认设计尺寸配置正确
2. 检查缩放计算逻辑
3. 测试不同屏幕比例
4. 调整安全区域设置

## 📞 技术支持

如果遇到技术问题，可以：

1. 查看控制台错误信息
2. 检查抖音开发者文档
3. 参考项目中的注释说明
4. 使用调试模式分析问题

---

**祝您游戏开发顺利！** 🎮✨
