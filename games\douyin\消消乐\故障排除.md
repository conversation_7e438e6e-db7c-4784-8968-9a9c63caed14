# 故障排除指南

## 🚨 常见问题及解决方案

### 1. 依赖加载失败

**问题**: 控制台显示 "Missing dependencies" 错误

**原因**: 脚本文件未按正确顺序加载

**解决方案**:
1. 检查 `game.json` 中的 `scripts` 配置是否正确
2. 确保所有文件都存在于指定路径
3. 检查文件名是否正确（区分大小写）

**正确的 scripts 配置**:
```json
"scripts": [
    "config.js",
    "utils/Utils.js", 
    "core/GameEngine.js",
    "core/PageManager.js",
    "core/ResourceManager.js", 
    "effects/ParticleSystem.js",
    "effects/AudioManager.js",
    "pages/HomePage.js",
    "pages/GamePage.js", 
    "pages/RankPage.js",
    "pages/SettingPage.js",
    "debug.js",
    "game.js"
]
```

### 2. 抖音小游戏API不可用

**问题**: 控制台显示 "抖音小游戏API不可用"

**原因**: 
- 不在抖音开发者工具环境中运行
- 抖音开发者工具版本过旧

**解决方案**:
1. 确保在抖音开发者工具中打开项目
2. 更新抖音开发者工具到最新版本
3. 检查项目配置是否正确

### 3. 资源加载失败

**问题**: 图片或音频无法加载

**原因**:
- 资源文件路径错误
- 资源文件不存在
- 文件格式不支持

**解决方案**:
1. 检查 `images/` 和 `audios/` 目录下的文件
2. 确保文件路径与配置中的路径一致
3. 使用支持的文件格式（PNG、JPG、MP3等）

### 4. 游戏卡顿或性能问题

**问题**: 游戏运行缓慢，FPS低于预期

**原因**:
- 设备性能不足
- 粒子数量过多
- 内存泄漏

**解决方案**:
1. 在控制台执行以下命令调整性能:
```javascript
// 减少粒子数量
GameConfig.PERFORMANCE.MAX_PARTICLES = 50;

// 降低粒子生成率
GameConfig.ANIMATION.PARTICLE_SPAWN_RATE = 2;

// 启用调试信息查看FPS
gameInstance.gameEngine.showDebugInfo = true;
```

2. 检查内存使用情况
3. 确保页面切换时正确销毁资源

### 5. 音效无法播放

**问题**: 背景音乐或音效不播放

**原因**:
- 音频文件路径错误
- 设备静音
- 音频权限问题

**解决方案**:
1. 检查音频文件是否存在
2. 确认设备音量和游戏音量设置
3. 在设置页面检查静音状态

### 6. 屏幕适配问题

**问题**: 游戏界面显示不完整或比例错误

**原因**:
- 屏幕分辨率特殊
- 适配算法问题

**解决方案**:
1. 检查设计尺寸配置:
```javascript
// 在控制台查看当前适配信息
console.log('屏幕信息:', tt.getSystemInfoSync());
console.log('适配比例:', gameInstance.gameEngine.scale);
```

2. 调整适配参数:
```javascript
// 修改最小/最大缩放比例
GameConfig.SCREEN.MIN_SCALE = 0.3;
GameConfig.SCREEN.MAX_SCALE = 3.0;
```

## 🔧 调试工具

### 启用调试模式

在控制台执行以下命令:
```javascript
// 显示性能信息
gameInstance.gameEngine.showDebugInfo = true;

// 查看游戏状态
debugGame();

// 查看粒子系统状态
if (gameInstance.pageManager.currentPage.particleSystem) {
    console.log(gameInstance.pageManager.currentPage.particleSystem.getStats());
}
```

### 性能监控

```javascript
// 查看当前FPS
console.log('当前FPS:', gameInstance.performanceMonitor.currentFPS);

// 查看内存使用
console.log('内存使用:', gameInstance.getMemoryUsage());

// 强制垃圾回收
gameInstance.performGarbageCollection();
```

### 资源管理调试

```javascript
// 查看资源加载状态
if (gameInstance.pageManager.currentPage.resourceManager) {
    console.log(gameInstance.pageManager.currentPage.resourceManager.getLoadingStatus());
}

// 手动触发资源清理
gameInstance.pageManager.currentPage.resourceManager.unloadInactiveResources();
```

## 📱 设备兼容性

### 低端设备优化

如果在低端设备上运行缓慢，可以手动调整以下参数:

```javascript
// 降低粒子效果
GameConfig.PERFORMANCE.MAX_PARTICLES = 30;
GameConfig.ANIMATION.PARTICLE_SPAWN_RATE = 1;

// 禁用部分特效
GameConfig.DEFAULT_SETTINGS.enableParticles = false;

// 降低动画质量
GameConfig.ANIMATION.ELIMINATION_DURATION = 200;
```

### 高端设备增强

在高端设备上可以提升视觉效果:

```javascript
// 增加粒子数量
GameConfig.PERFORMANCE.MAX_PARTICLES = 200;
GameConfig.ANIMATION.PARTICLE_SPAWN_RATE = 8;

// 提升动画质量
GameConfig.ANIMATION.ELIMINATION_DURATION = 400;
```

## 🐛 错误日志分析

### 常见错误信息

1. **"GameConfig is not defined"**
   - 检查 config.js 是否正确加载
   - 确认 scripts 配置中 config.js 在第一位

2. **"Cannot read property of undefined"**
   - 检查对象是否正确初始化
   - 确认页面生命周期是否正确

3. **"Audio context was not allowed to start"**
   - 用户需要先进行交互才能播放音频
   - 这是浏览器的安全限制，属于正常现象

4. **"Failed to load resource"**
   - 检查资源文件路径
   - 确认文件是否存在

## 📞 获取帮助

如果以上方法都无法解决问题:

1. **查看控制台完整错误信息**
2. **记录复现步骤**
3. **提供设备和环境信息**
4. **检查抖音开发者工具版本**

### 收集调试信息

```javascript
// 收集完整的调试信息
const debugInfo = {
    gameVersion: GameConfig.GAME.VERSION,
    systemInfo: tt.getSystemInfoSync(),
    gameState: gameInstance ? {
        isInitialized: gameInstance.isInitialized,
        currentPage: gameInstance.pageManager.getCurrentPageName(),
        fps: gameInstance.performanceMonitor.currentFPS
    } : null,
    timestamp: new Date().toISOString()
};

console.log('调试信息:', JSON.stringify(debugInfo, null, 2));
```

---

**记住**: 大多数问题都可以通过检查控制台错误信息和确认文件配置来解决。
