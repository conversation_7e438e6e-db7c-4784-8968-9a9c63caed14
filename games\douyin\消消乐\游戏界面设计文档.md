# 游戏界面与玩法设计方案（基于抖音原生开发特性）

**核心目标**：轻量化资源（包体≤15MB） + 高流畅性（帧率≥50FPS） + 强社交互动（抖音API深度集成）

# 游戏界面与玩法设计方案（基于抖音原生开发特性）

**核心目标**：轻量化资源（包体≤15MB） + 高流畅性（帧率≥50FPS） + 强社交互动（抖音API深度集成）

## 1. 界面设计
### 1.1 游戏主页
- **视觉风格**：偏可爱风暖系背景，主色调#FFEFD5（米黄色），点缀粉色爱心粒子动画
- **标题区域**：
  - 主标题："萌宠爱消消"（48px圆润卡通字体，#FF6B8B粉色渐变）
  - 版本信息：右下角显示"v1.0.0"（14px Arial，#888888）
- **按钮区域**：
  - 位置：屏幕下方，三个按钮横向排列，间距20px
  - 样式：圆角矩形（半径15px），尺寸120×50px
  - 按钮内容：
    - 开始游戏：图标+文字（images/btn/start.png + "开始游戏"）
    - 排行榜：图标+文字（images/btn/rank.png + "排行榜"）
    - 设置：图标+文字（images/btn/setting.png + "设置"）
- **动画效果**：
  - 标题文字：轻微上下浮动（周期2s）
  - 按钮悬停：缩放至1.05倍+阴影加深
  - 背景装饰：随机飘落的彩色爱心（每秒3-5个）

### 1.2 游戏内界面
#### 布局分区：
- **返回按钮**：屏幕左侧，距离顶部15%屏幕高度位置
- **统计栏**：宽度80%屏幕宽度，高度120px，包含关卡标题与数据显示
- **游戏区域**：8×10网格布局，动态计算尺寸填充剩余空间
- **底部道具栏**：包含刷新卡、炸弹卡、清屏卡和降级卡，横向均匀分布

### 1.3 排行榜页面
- **导航元素**：左上角返回按钮（距离顶部15%屏幕高度）
- **个人数据区**：灰色透明框展示最高分、最高连击和最佳通关时间
- **榜单切换区**：分数榜/时间榜/连击榜横向Tab栏
- **榜单列表**：前10名数据展示，高度限制在屏幕可视范围内

### 1.4 设置页面
- **导航元素**：左上角返回按钮（距离顶部15%屏幕高度）
- **设置面板**：白色透明背景框，包含：
  - 背景音量滑块（0-100%）
  - 音效音量滑块（0-100%）
  - 静音开关（默认关闭）
- **交互规则**：设置实时写入缓存，无需保存按钮

## 2. 玩法系统
### 2.1 核心消除机制
- **基础消除规则**：3/4/5消分别对应30/40/50分，特殊消除生成道具
- **连击系统**：2秒内连续消除触发倍率递增（1.5×→2.0×）
- **特殊方块交互**：火箭/炸弹组合产生不同范围消除效果
- **分数计算**：基础分数+连击倍率+特殊消除奖励

### 2.2 道具系统
| 道具类型 | 触发方式 | 效果描述 | 音效 |
|---------|---------|---------|------|
| 刷新卡 | 点击 | 打乱所有格子位置 | - |
| 炸弹卡 | 拖拽 | 5×5范围爆炸消除（+500分） | audios/bomb.mp3 |
| 清屏卡 | 点击 | 全屏消除（+800分） | - |
| 降级卡 | 点击 | 减少一种萌宠类型 | - |

## 3. 关卡设计
### 3.1 关卡配置
- **第一关**：《萌宠新手村》，目标分数1000分，5种萌宠
- **第二关**：《萌宠总动员》，目标分数4000分，7种萌宠
- **第三关**：《萌宠修罗场》，目标分数8000分，9种萌宠

### 3.2 初始化与胜负条件
- **初始化算法**：随机填充网格，确保无初始3连消
- **胜利条件**：分数≥目标值，自动跳转下一关
- **失败条件**：无解检测触发，支持2次复活（网格刷新）

## 4. 视听效果
### 4.1 动画规范
- **消除动画**：爆炸感粒子特效，帧率≥45FPS
- **连击显示**：3-4连击绿色数字，5+连击渐变为红色
- **页面过渡**：爱心扩散转场效果（时长0.5s）

### 4.2 音效配置
| 触发场景 | 音效文件 |
|---------|---------|
| 普通消除 | audios/so.mp3 |
| 3连击 | audios/wa.mp3 |
| 5连击 | audios/good.mp3 |
| 小火箭消除 | audios/shua.mp3 |
| 炸弹消除 | audios/bomb.mp3 |

## 5. 性能优化
### 5.1 资源管理
- 动态加载：按关卡分阶段加载萌宠贴图，非活跃资源即时卸载
- 纹理合批：使用128×128px图集，单Draw Call渲染

### 5.2 渲染优化
- Native原生渲染：平台GPU加速API
- 异步动画：Worker线程处理粒子效果
- 低端机适配：资源分辨率动态降级

### 5.3 内存管理
- 页面跳转时销毁当前页所有动画实例
- 初始化时预创建所有组件，避免运行时开销

## 6. 社交功能
- **直播联动**：观众弹幕触发道具掉落
- **UGC激励**：高连击自动生成15秒短视频，支持分享至抖音话题

## 7. 无障碍设计
- 高对比模式：萌宠图标2px白色描边
- 触控扩展：点击区域扩大至图标外围10px

## 引用依据
- 界面分层设计参考网页8的手持设备UI模型
- 社交功能集成结合网页1的抖音直播交互分析
- 性能优化策略借鉴网页7的Unity引擎优化方案