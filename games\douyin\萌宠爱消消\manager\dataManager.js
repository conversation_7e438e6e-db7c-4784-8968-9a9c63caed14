/**
 * 萌宠爱消消 - 数据管理器
 * 负责游戏数据的存储、读取和管理
 */

const CONFIG = require('../config.js');

class DataManager {
    constructor() {
        this.data = {
            highScore: 0,
            currentLevel: 1,
            coins: 100,
            items: {
                refresh: 2,
                bomb: 1,
                clear: 1
            },
            settings: {
                soundEnabled: true,
                musicEnabled: true,
                vibrationEnabled: true,
                theme: 'default'
            },
            unlockedLevels: [1],
            achievements: [],
            dailyTasks: [],
            lastLoginDate: new Date().toDateString()
        };

        this.init();
    }

    /**
     * 初始化数据管理器
     */
    init() {
        this.loadAllData();
    }

    /**
     * 加载所有游戏数据
     */
    loadAllData() {
        try {
            // 加载高分
            this.data.highScore = tt.getStorageSync(CONFIG.storage.highScore) || 0;
            
            // 加载当前关卡
            this.data.currentLevel = tt.getStorageSync(CONFIG.storage.currentLevel) || 1;
            
            // 加载金币
            this.data.coins = tt.getStorageSync(CONFIG.storage.coins) || 100;
            
            // 加载道具
            const savedItems = tt.getStorageSync(CONFIG.storage.items);
            if (savedItems) {
                this.data.items = { ...this.data.items, ...savedItems };
            }
            
            // 加载设置
            const savedSettings = tt.getStorageSync(CONFIG.storage.settings);
            if (savedSettings) {
                this.data.settings = { ...this.data.settings, ...savedSettings };
            }
            
            // 加载解锁关卡
            this.data.unlockedLevels = tt.getStorageSync(CONFIG.storage.unlockedLevels) || [1];
            
            // 加载成就
            this.data.achievements = tt.getStorageSync(CONFIG.storage.achievements) || [];
            
            console.log('游戏数据加载完成');
        } catch (error) {
            console.error('加载游戏数据失败:', error);
        }
    }

    /**
     * 保存所有游戏数据
     */
    saveAllData() {
        try {
            tt.setStorageSync(CONFIG.storage.highScore, this.data.highScore);
            tt.setStorageSync(CONFIG.storage.currentLevel, this.data.currentLevel);
            tt.setStorageSync(CONFIG.storage.coins, this.data.coins);
            tt.setStorageSync(CONFIG.storage.items, this.data.items);
            tt.setStorageSync(CONFIG.storage.settings, this.data.settings);
            tt.setStorageSync(CONFIG.storage.unlockedLevels, this.data.unlockedLevels);
            tt.setStorageSync(CONFIG.storage.achievements, this.data.achievements);
            
            console.log('游戏数据保存完成');
        } catch (error) {
            console.error('保存游戏数据失败:', error);
        }
    }

    /**
     * 获取高分
     */
    getHighScore() {
        return this.data.highScore;
    }

    /**
     * 设置高分
     */
    setHighScore(score) {
        if (score > this.data.highScore) {
            this.data.highScore = score;
            this.saveAllData();
            return true;
        }
        return false;
    }

    /**
     * 获取当前关卡
     */
    getCurrentLevel() {
        return this.data.currentLevel;
    }

    /**
     * 设置当前关卡
     */
    setCurrentLevel(level) {
        this.data.currentLevel = level;
        
        // 解锁新关卡
        if (!this.data.unlockedLevels.includes(level)) {
            this.data.unlockedLevels.push(level);
        }
        
        this.saveAllData();
    }

    /**
     * 获取设置
     */
    getSettings() {
        return { ...this.data.settings };
    }

    /**
     * 更新设置
     */
    updateSettings(newSettings) {
        this.data.settings = { ...this.data.settings, ...newSettings };
        this.saveAllData();
    }

    /**
     * 获取金币
     */
    getCoins() {
        return this.data.coins;
    }

    /**
     * 增加金币
     */
    addCoins(amount) {
        this.data.coins += amount;
        this.saveAllData();
    }

    /**
     * 减少金币
     */
    spendCoins(amount) {
        if (this.data.coins >= amount) {
            this.data.coins -= amount;
            this.saveAllData();
            return true;
        }
        return false;
    }

    /**
     * 获取道具数量
     */
    getItemCount(itemType) {
        return this.data.items[itemType] || 0;
    }

    /**
     * 增加道具
     */
    addItem(itemType, count = 1) {
        if (!this.data.items[itemType]) {
            this.data.items[itemType] = 0;
        }
        this.data.items[itemType] += count;
        this.saveAllData();
    }

    /**
     * 使用道具
     */
    useItem(itemType) {
        if (this.data.items[itemType] && this.data.items[itemType] > 0) {
            this.data.items[itemType]--;
            this.saveAllData();
            return true;
        }
        return false;
    }

    /**
     * 检查关卡是否解锁
     */
    isLevelUnlocked(level) {
        return this.data.unlockedLevels.includes(level);
    }

    /**
     * 获取解锁的关卡列表
     */
    getUnlockedLevels() {
        return [...this.data.unlockedLevels];
    }

    /**
     * 添加成就
     */
    addAchievement(achievementId) {
        if (!this.data.achievements.includes(achievementId)) {
            this.data.achievements.push(achievementId);
            this.saveAllData();
            return true;
        }
        return false;
    }

    /**
     * 获取成就列表
     */
    getAchievements() {
        return [...this.data.achievements];
    }

    /**
     * 重置所有数据
     */
    resetAllData() {
        this.data = {
            highScore: 0,
            currentLevel: 1,
            coins: 100,
            items: {
                refresh: 2,
                bomb: 1,
                clear: 1
            },
            settings: {
                soundEnabled: true,
                musicEnabled: true,
                vibrationEnabled: true,
                theme: 'default'
            },
            unlockedLevels: [1],
            achievements: [],
            dailyTasks: [],
            lastLoginDate: new Date().toDateString()
        };
        
        this.saveAllData();
        console.log('所有游戏数据已重置');
    }

    /**
     * 导出游戏数据
     */
    exportData() {
        return JSON.stringify(this.data);
    }

    /**
     * 导入游戏数据
     */
    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            this.data = { ...this.data, ...data };
            this.saveAllData();
            console.log('游戏数据导入成功');
            return true;
        } catch (error) {
            console.error('导入游戏数据失败:', error);
            return false;
        }
    }
}

module.exports = DataManager;
