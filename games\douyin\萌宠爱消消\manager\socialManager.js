/**
 * 萌宠爱消消 - 社交功能管理器
 * 负责分享、邀请等社交功能
 */

const CONFIG = require('../config.js');

class SocialManager {
    constructor(app) {
        this.app = app;
        this.shareCount = 0;
        this.inviteCount = 0;
        
        this.init();
    }
    
    /**
     * 初始化社交管理器
     */
    init() {
        this.loadSocialData();
    }
    
    /**
     * 加载社交数据
     */
    loadSocialData() {
        try {
            this.shareCount = tt.getStorageSync('social_share_count') || 0;
            this.inviteCount = tt.getStorageSync('social_invite_count') || 0;
        } catch (error) {
            console.error('加载社交数据失败:', error);
        }
    }
    
    /**
     * 保存社交数据
     */
    saveSocialData() {
        try {
            tt.setStorageSync('social_share_count', this.shareCount);
            tt.setStorageSync('social_invite_count', this.inviteCount);
        } catch (error) {
            console.error('保存社交数据失败:', error);
        }
    }
    
    /**
     * 分享游戏
     */
    shareGame() {
        return new Promise((resolve, reject) => {
            try {
                const shareConfig = CONFIG.social.share;
                
                tt.shareAppMessage({
                    title: shareConfig.title,
                    desc: shareConfig.description,
                    imageUrl: shareConfig.imageUrl,
                    query: 'from=share',
                    success: (res) => {
                        console.log('分享成功');
                        this.handleShareSuccess();
                        resolve(true);
                    },
                    fail: (err) => {
                        console.error('分享失败:', err);
                        reject(err);
                    }
                });
            } catch (error) {
                console.error('分享功能不可用:', error);
                // 在开发环境中模拟分享成功
                this.handleShareSuccess();
                resolve(true);
            }
        });
    }
    
    /**
     * 处理分享成功
     */
    handleShareSuccess() {
        this.shareCount++;
        this.saveSocialData();
        
        // 给予奖励
        const reward = CONFIG.social.share.reward;
        this.app.dataManager.addItem(reward.type, reward.count);
        
        // 显示奖励提示
        this.showRewardMessage(`分享成功！获得${CONFIG.props[reward.type].name} x${reward.count}`);
        
        // 播放音效
        this.app.playSound('good');
    }
    
    /**
     * 邀请好友
     */
    inviteFriend() {
        return new Promise((resolve, reject) => {
            try {
                const inviteConfig = CONFIG.social.invite;
                
                tt.shareAppMessage({
                    title: inviteConfig.title,
                    desc: inviteConfig.description,
                    imageUrl: CONFIG.social.share.imageUrl,
                    query: 'from=invite',
                    success: (res) => {
                        console.log('邀请成功');
                        this.handleInviteSuccess();
                        resolve(true);
                    },
                    fail: (err) => {
                        console.error('邀请失败:', err);
                        reject(err);
                    }
                });
            } catch (error) {
                console.error('邀请功能不可用:', error);
                // 在开发环境中模拟邀请成功
                this.handleInviteSuccess();
                resolve(true);
            }
        });
    }
    
    /**
     * 处理邀请成功
     */
    handleInviteSuccess() {
        this.inviteCount++;
        this.saveSocialData();
        
        // 给予奖励
        const reward = CONFIG.social.invite.reward;
        this.app.dataManager.addItem(reward.type, reward.count);
        
        // 显示奖励提示
        this.showRewardMessage(`邀请成功！获得${CONFIG.props[reward.type].name} x${reward.count}`);
        
        // 播放音效
        this.app.playSound('good');
    }
    
    /**
     * 显示奖励消息
     */
    showRewardMessage(message) {
        // 创建奖励提示UI
        const ctx = this.app.ctx;
        const displayWidth = this.app.displayWidth;
        const displayHeight = this.app.displayHeight;
        
        // 半透明背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(0, 0, displayWidth, displayHeight);
        
        // 奖励卡片
        const cardWidth = displayWidth * 0.8;
        const cardHeight = 120;
        const cardX = (displayWidth - cardWidth) / 2;
        const cardY = (displayHeight - cardHeight) / 2;
        
        ctx.fillStyle = CONFIG.ui.colors.white;
        ctx.beginPath();
        ctx.roundRect(cardX, cardY, cardWidth, cardHeight, 12);
        ctx.fill();
        
        // 奖励文字
        ctx.fillStyle = CONFIG.ui.colors.primary;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.large}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText(message, displayWidth / 2, cardY + cardHeight / 2);
        
        // 2秒后消失
        setTimeout(() => {
            if (this.app.currentPage === 'game' && this.app.currentPageInstance) {
                this.app.currentPageInstance.draw();
            } else {
                this.app.drawHomePage();
            }
        }, 2000);
    }
    
    /**
     * 获取分享次数
     */
    getShareCount() {
        return this.shareCount;
    }
    
    /**
     * 获取邀请次数
     */
    getInviteCount() {
        return this.inviteCount;
    }
    
    /**
     * 检查是否可以获得每日分享奖励
     */
    canGetDailyShareReward() {
        const today = new Date().toDateString();
        const lastShareDate = tt.getStorageSync('last_share_date');
        return lastShareDate !== today;
    }
    
    /**
     * 标记今日已分享
     */
    markTodayShared() {
        const today = new Date().toDateString();
        tt.setStorageSync('last_share_date', today);
    }
    
    /**
     * 处理来自分享的启动
     */
    handleLaunchFromShare(query) {
        if (query && query.from === 'share') {
            console.log('用户通过分享链接进入游戏');
            // 可以给分享者额外奖励
        } else if (query && query.from === 'invite') {
            console.log('用户通过邀请链接进入游戏');
            // 可以给邀请者额外奖励
        }
    }
    
    /**
     * 销毁社交管理器
     */
    destroy() {
        this.saveSocialData();
    }
}

module.exports = SocialManager;
