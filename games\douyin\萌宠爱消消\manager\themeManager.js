/**
 * 萌宠爱消消 - 主题管理器
 * 负责游戏主题的统一管理和安全访问
 */

class ThemeManager {
    constructor() {
        this.defaultTheme = 'default';
        
        // 主题配置
        this.themes = {
            default: {
                name: "萌宠粉色主题",
                backgroundColor: "#F9F0FA",
                primaryColor: "#FF85A1",
                secondaryColor: "#FFB6C1",
                accentColor: "#FFD1DC",
                textColor: "#333333",
                textLightColor: "#666666",
                whiteColor: "#FFFFFF",
                titleImage: "images/title.png",
                buttonLayout: "vertical",
                buttonSpacing: 20,
                buttonImages: {
                    start: "images/button/start.png",
                    rank: "images/button/ranking.png",
                    setting: "images/button/setting.png"
                },
                // 布局配置
                layout: {
                    titlePosition: {x: 0.5, y: 0.2},
                    buttonArea: {x: 0.5, y: 0.6},
                    buttonDirection: "vertical",
                    backgroundEffect: "gradient"
                }
            }
        };
    }

    /**
     * 安全获取主题配置
     * @param {string} themeName - 主题名称
     * @returns {object} 主题配置对象
     */
    getTheme(themeName) {
        return this.themes[themeName] || this.themes[this.defaultTheme];
    }

    /**
     * 获取默认主题名称
     * @returns {string} 默认主题名称
     */
    getDefaultTheme() {
        return this.defaultTheme;
    }

    /**
     * 获取所有可用主题
     * @returns {Array} 主题列表
     */
    getAvailableThemes() {
        return Object.keys(this.themes).map(key => ({
            key,
            name: this.themes[key].name
        }));
    }

    /**
     * 创建主页按钮配置
     * @param {number} x - 按钮中心X坐标
     * @param {number} y - 按钮中心Y坐标
     * @param {string} type - 按钮类型 ('start' | 'rank' | 'setting')
     * @param {number} screenWidth - 屏幕宽度
     * @returns {object} 按钮配置对象
     */
    createHomeButton(x, y, type, screenWidth) {
        // 统一按钮尺寸：屏幕宽度的60%，高度为宽度的25%
        const buttonWidth = screenWidth * 0.6;
        const buttonHeight = buttonWidth * 0.25;
        
        // 按钮配置
        const buttonConfigs = {
            start: {
                text: '开始游戏',
                colorKey: 'primaryColor',
                textColor: '#FFFFFF'
            },
            rank: {
                text: '排行榜',
                colorKey: 'primaryColor',
                textColor: '#FFFFFF'
            },
            setting: {
                text: '设置',
                colorKey: 'primaryColor',
                textColor: '#FFFFFF'
            }
        };

        const config = buttonConfigs[type];
        if (!config) {
            throw new Error(`Invalid button type: ${type}`);
        }

        return {
            x: x - buttonWidth / 2, // 转换为左上角坐标
            y: y - buttonHeight / 2,
            width: buttonWidth,
            height: buttonHeight,
            text: config.text,
            colorKey: config.colorKey,
            textColor: config.textColor,
            cornerRadius: buttonHeight * 0.3, // 圆角半径为高度的30%
            fontSize: Math.min(buttonHeight * 0.4, 24), // 字体大小为高度的40%，最大24px
            shadowOffset: 4,
            shadowBlur: 8
        };
    }

    /**
     * 获取主页按钮颜色
     * @param {string} themeName - 主题名称
     * @param {string} colorKey - 颜色键名
     * @returns {string|Array} 颜色值（可能是数组表示渐变色）
     */
    getButtonColor(themeName, colorKey) {
        const theme = this.getTheme(themeName);
        return theme[colorKey] || theme.primaryColor;
    }

    /**
     * 获取主题颜色
     * @param {string} themeName - 主题名称
     * @param {string} colorKey - 颜色键名
     * @returns {string} 颜色值
     */
    getColor(themeName, colorKey) {
        const theme = this.getTheme(themeName);
        return theme[colorKey] || '#000000';
    }

    /**
     * 应用主题到Canvas上下文
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {string} themeName - 主题名称
     */
    applyThemeToContext(ctx, themeName) {
        const theme = this.getTheme(themeName);
        
        // 设置默认样式
        ctx.fillStyle = theme.textColor;
        ctx.strokeStyle = theme.primaryColor;
        ctx.font = '16px HarmonyOS Sans SC, Arial, sans-serif';
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
    }

    /**
     * 创建渐变背景
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     * @param {string} themeName - 主题名称
     * @returns {CanvasGradient} 渐变对象
     */
    createGradientBackground(ctx, width, height, themeName) {
        const theme = this.getTheme(themeName);
        const gradient = ctx.createLinearGradient(0, 0, 0, height);
        
        gradient.addColorStop(0, theme.backgroundColor);
        gradient.addColorStop(1, theme.secondaryColor);
        
        return gradient;
    }

    /**
     * 绘制主题化按钮
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {object} buttonConfig - 按钮配置
     * @param {string} themeName - 主题名称
     */
    drawThemedButton(ctx, buttonConfig, themeName) {
        const theme = this.getTheme(themeName);
        
        // 绘制按钮背景
        ctx.fillStyle = theme[buttonConfig.colorKey] || theme.primaryColor;
        ctx.beginPath();
        ctx.roundRect(
            buttonConfig.x, 
            buttonConfig.y, 
            buttonConfig.width, 
            buttonConfig.height, 
            buttonConfig.cornerRadius
        );
        ctx.fill();
        
        // 绘制按钮阴影
        ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        ctx.shadowOffsetX = buttonConfig.shadowOffset;
        ctx.shadowOffsetY = buttonConfig.shadowOffset;
        ctx.shadowBlur = buttonConfig.shadowBlur;
        
        // 绘制按钮文字
        ctx.fillStyle = buttonConfig.textColor;
        ctx.font = `bold ${buttonConfig.fontSize}px HarmonyOS Sans SC, Arial, sans-serif`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(
            buttonConfig.text,
            buttonConfig.x + buttonConfig.width / 2,
            buttonConfig.y + buttonConfig.height / 2
        );
        
        // 重置阴影
        ctx.shadowColor = 'transparent';
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        ctx.shadowBlur = 0;
    }

    /**
     * 检查主题是否存在
     * @param {string} themeName - 主题名称
     * @returns {boolean} 是否存在
     */
    hasTheme(themeName) {
        return this.themes.hasOwnProperty(themeName);
    }

    /**
     * 设置主题
     * @param {string} themeName - 主题名称
     * @returns {boolean} 设置是否成功
     */
    setTheme(themeName) {
        if (this.hasTheme(themeName)) {
            this.currentTheme = themeName;
            return true;
        }
        return false;
    }

    /**
     * 获取当前主题名称
     * @returns {string} 当前主题名称
     */
    getCurrentTheme() {
        return this.currentTheme || this.defaultTheme;
    }
}

module.exports = ThemeManager;
