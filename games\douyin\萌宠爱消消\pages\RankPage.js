/**
 * 萌宠爱消消 - 排行榜页面
 * 显示玩家排名和分数信息
 */

const CONFIG = require('../config.js');
const BackgroundRenderer = require('../utils/BackgroundRenderer.js');
const UIComponents = require('../utils/UIComponents.js');
const CanvasUtils = require('../utils/CanvasUtils.js');

class RankPage {
    constructor(app) {
        this.app = app;
        this.canvas = app.canvas;
        this.ctx = app.ctx;

        // 页面状态
        this.currentTab = 'all'; // 'all', 'region', 'friends'
        this.rankData = {
            all: [],
            region: [],
            friends: []
        };
        this.userRank = {
            rank: 12,
            score: 8500
        };

        this.init();
    }



    /**
     * 初始化排行榜页面
     */
    init() {
        // 优化Canvas渲染设置
        CanvasUtils.optimizeCanvasRendering(this.ctx);

        this.loadRankData();
        this.draw();
        this.bindEvents();
    }

    /**
     * 加载排行榜数据
     */
    loadRankData() {
        // 模拟排行榜数据
        this.rankData.all = [
            { rank: 1, name: '萌宠大师', score: 15000, avatar: '🐱' },
            { rank: 2, name: '消除达人', score: 12500, avatar: '🐶' },
            { rank: 3, name: '连击王者', score: 11000, avatar: '🐼' },
            { rank: 4, name: '萌宠新星', score: 9800, avatar: '🐰' },
            { rank: 5, name: '消消乐手', score: 9200, avatar: '🦊' },
            { rank: 6, name: '萌宠爱好者', score: 8800, avatar: '🐸' },
            { rank: 7, name: '游戏高手', score: 8600, avatar: '🐵' },
            { rank: 8, name: '萌宠收集家', score: 8400, avatar: '🐘' },
            { rank: 9, name: '消除专家', score: 8200, avatar: '🐯' },
            { rank: 10, name: '萌宠守护者', score: 8000, avatar: '🐱' }
        ];

        // 复制数据到其他标签
        this.rankData.region = [...this.rankData.all];
        this.rankData.friends = this.rankData.all.slice(0, 5);
    }

    /**
     * 绘制排行榜页面
     */
    draw() {
        this.clearCanvas();
        this.drawBackground();
        this.drawHeader();
        this.drawUserRank();
        this.drawTabs();
        this.drawRankList();
        this.drawFooter();
    }

    /**
     * 清空画布
     */
    clearCanvas() {
        this.ctx.clearRect(0, 0, this.app.displayWidth, this.app.displayHeight);
    }

    /**
     * 绘制背景
     */
    drawBackground() {
        BackgroundRenderer.drawGradientBackground(this.ctx, this.app.displayWidth, this.app.displayHeight);
    }

    /**
     * 绘制页面头部
     */
    drawHeader() {
        const ctx = this.ctx;
        const headerY = 80; // 向下移动，避免刘海屏遮挡

        // 返回按钮 - 使用统一样式
        const buttonSize = 40;
        const buttonX = 20;
        const buttonY = 60;

        UIComponents.drawBackButton(ctx, buttonX, buttonY, buttonSize);

        // 页面标题
        ctx.fillStyle = CONFIG.ui.colors.primary;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.xlarge}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText('排行榜', this.app.displayWidth / 2, headerY);

        // 存储返回按钮位置
        this.backButton = {
            x: buttonX,
            y: buttonY,
            width: buttonSize,
            height: buttonSize
        };
    }

    /**
     * 绘制用户排名区域
     */
    drawUserRank() {
        const ctx = this.ctx;
        const startY = 120;
        const height = 80; // 减少高度，去掉头像

        // 背景卡片
        ctx.fillStyle = '#FFFFFF';
        ctx.strokeStyle = '#E0E0E0';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.roundRect(16, startY, this.app.displayWidth - 32, height, 12);
        ctx.fill();
        ctx.stroke();

        // 我的排名标题
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText('我的排名', 30, startY + 30);

        // 排名和分数信息 - 在同一行显示
        ctx.fillStyle = CONFIG.ui.colors.primary;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.large}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText(`第${this.userRank.rank}名`, 30, startY + 55);

        // 分数显示在右侧
        ctx.textAlign = 'right';
        ctx.fillText(`${this.userRank.score}分`, this.app.displayWidth - 50, startY + 55);
    }

    /**
     * 绘制标签切换区
     */
    drawTabs() {
        const ctx = this.ctx;
        const startY = 220; // 调整位置
        const tabWidth = this.app.displayWidth / 3;
        const tabs = [
            { key: 'all', text: '全部' },
            { key: 'region', text: '地区' },
            { key: 'friends', text: '好友' }
        ];

        // 存储标签位置信息
        this.tabButtons = [];

        tabs.forEach((tab, index) => {
            const x = index * tabWidth;
            const isSelected = tab.key === this.currentTab;

            // 存储标签按钮信息
            this.tabButtons.push({
                key: tab.key,
                x: x,
                y: startY,
                width: tabWidth,
                height: 50
            });

            // 标签文字
            ctx.fillStyle = isSelected ? CONFIG.ui.colors.primary : CONFIG.ui.colors.textLight;
            ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.fillText(tab.text, x + tabWidth/2, startY + 20);

            // 选中指示器
            if (isSelected) {
                ctx.fillStyle = CONFIG.ui.colors.primary;
                const indicatorWidth = tabWidth * 0.8;
                ctx.fillRect(x + tabWidth * 0.1, startY + 30, indicatorWidth, 2);
            }
        });
    }

    /**
     * 绘制排行榜列表
     */
    drawRankList() {
        const ctx = this.ctx;
        const startY = 280;
        const currentData = this.rankData[this.currentTab];

        // 绘制表头
        this.drawTableHeader(startY);

        // 绘制前十名列表
        this.drawTopTenList(startY + 40, currentData.slice(0, 10));
    }

    /**
     * 绘制表头
     */
    drawTableHeader(startY) {
        const ctx = this.ctx;
        const headerHeight = 35;

        // 表头背景
        ctx.fillStyle = '#F8F9FA';
        ctx.strokeStyle = '#E0E0E0';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.roundRect(16, startY, this.app.displayWidth - 32, headerHeight, 8);
        ctx.fill();
        ctx.stroke();

        // 动态计算列宽
        const tableWidth = this.app.displayWidth - 32;
        const rankWidth = 60;  // 排名列固定宽度
        const scoreWidth = 80; // 分数列固定宽度
        const nameWidth = tableWidth - rankWidth - scoreWidth; // 昵称列自适应

        // 存储列宽信息供后续使用
        this.columnWidths = { rank: rankWidth, name: nameWidth, score: scoreWidth };

        // 表头文字
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;

        // 排名列
        ctx.textAlign = 'center';
        ctx.fillText('排名', 16 + rankWidth/2, startY + headerHeight/2 + 6);

        // 昵称列
        ctx.textAlign = 'left';
        ctx.fillText('昵称', 16 + rankWidth + 15, startY + headerHeight/2 + 6);

        // 分数列
        ctx.textAlign = 'center';
        ctx.fillText('分数', 16 + rankWidth + nameWidth + scoreWidth/2, startY + headerHeight/2 + 6);
    }

    /**
     * 绘制前十名列表
     */
    drawTopTenList(startY, players) {
        const ctx = this.ctx;
        const itemHeight = 50; // 减少行高，更紧凑
        const maxVisible = Math.min(10, players.length); // 最多显示10名

        // 确保表格不超出屏幕
        const availableHeight = this.app.displayHeight - startY - 80; // 预留底部空间
        const actualVisible = Math.min(maxVisible, Math.floor(availableHeight / itemHeight));

        players.slice(0, actualVisible).forEach((player, index) => {
            const y = startY + index * itemHeight;
            const isEvenRow = index % 2 === 0;

            // 行背景（斑马纹效果）
            ctx.fillStyle = isEvenRow ? '#FFFFFF' : '#F8F9FA';
            ctx.fillRect(16, y, this.app.displayWidth - 32, itemHeight);

            // 行边框
            ctx.strokeStyle = '#E0E0E0';
            ctx.lineWidth = 0.5;
            ctx.beginPath();
            ctx.moveTo(16, y + itemHeight);
            ctx.lineTo(this.app.displayWidth - 16, y + itemHeight);
            ctx.stroke();

            // 排名数字 - 前三名特殊颜色
            let rankColor = CONFIG.ui.colors.text;
            if (player.rank === 1) rankColor = '#FFD700'; // 金色
            else if (player.rank === 2) rankColor = '#C0C0C0'; // 银色
            else if (player.rank === 3) rankColor = '#CD7F32'; // 铜色

            ctx.fillStyle = rankColor;
            ctx.font = `bold ${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.fillText(player.rank.toString(), 16 + this.columnWidths.rank/2, y + itemHeight/2 + 6);

            // 用户名 - 自动截断过长的名称
            ctx.fillStyle = CONFIG.ui.colors.text;
            ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'left';

            // 计算昵称最大显示长度
            const maxNameWidth = this.columnWidths.name - 30; // 预留边距
            let displayName = player.name;
            let nameWidth = ctx.measureText(displayName).width;

            if (nameWidth > maxNameWidth) {
                // 逐字符截断直到适合宽度
                while (nameWidth > maxNameWidth - 20 && displayName.length > 1) { // 预留"..."的宽度
                    displayName = displayName.slice(0, -1);
                    nameWidth = ctx.measureText(displayName + '...').width;
                }
                displayName += '...';
            }

            ctx.fillText(displayName, 16 + this.columnWidths.rank + 15, y + itemHeight/2 + 6);

            // 分数
            ctx.fillStyle = CONFIG.ui.colors.primary;
            ctx.font = `bold ${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.fillText(player.score.toString(),
                        16 + this.columnWidths.rank + this.columnWidths.name + this.columnWidths.score/2,
                        y + itemHeight/2 + 6);
        });

        // 如果数据不足10条，显示提示
        if (players.length < 10) {
            const emptyY = startY + players.length * itemHeight + 20;
            ctx.fillStyle = CONFIG.ui.colors.textLight;
            ctx.font = `${CONFIG.ui.fonts.sizes.small}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.fillText('暂无更多排名数据', this.app.displayWidth / 2, emptyY);
        }
    }

    /**
     * 绘制页面底部
     */
    drawFooter() {
        const ctx = this.ctx;

        ctx.fillStyle = CONFIG.ui.colors.textLight;
        ctx.font = `${CONFIG.ui.fonts.sizes.small}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText('数据每小时更新一次',
                    this.app.displayWidth / 2,
                    this.app.displayHeight - 20);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        tt.onTouchStart(this.handleTouch.bind(this));
    }

    /**
     * 处理触摸事件
     */
    handleTouch(e) {
        const touch = e.touches[0];
        const x = touch.clientX;
        const y = touch.clientY;

        // 返回按钮
        if (this.backButton && this.isPointInRect(x, y, this.backButton.x, this.backButton.y, this.backButton.width, this.backButton.height)) {
            this.app.playSound('match');
            this.back();
            return;
        }

        // 标签切换
        if (this.tabButtons) {
            for (let i = 0; i < this.tabButtons.length; i++) {
                const tab = this.tabButtons[i];
                if (this.isPointInRect(x, y, tab.x, tab.y, tab.width, tab.height)) {
                    if (tab.key !== this.currentTab) {
                        this.currentTab = tab.key;
                        this.app.playSound('match');
                        this.draw();
                    }
                    return;
                }
            }
        }
    }

    /**
     * 检查点是否在矩形内
     */
    isPointInRect(x, y, rectX, rectY, width, height) {
        return x >= rectX && x <= rectX + width &&
               y >= rectY && y <= rectY + height;
    }

    /**
     * 返回上一页
     */
    back() {
        this.destroy();
        this.app.goBack();
    }

    /**
     * 销毁页面
     */
    destroy() {
        tt.offTouchStart();
    }
}

module.exports = RankPage;