/**
 * 萌宠爱消消 - 设置页面
 * 游戏设置和偏好配置
 */

const CONFIG = require('../config.js');
const BackgroundRenderer = require('../utils/BackgroundRenderer.js');
const UIComponents = require('../utils/UIComponents.js');
const CanvasUtils = require('../utils/CanvasUtils.js');

class SettingPage {
    constructor(app) {
        this.app = app;
        this.canvas = app.canvas;
        this.ctx = app.ctx;

        // 设置状态
        this.settings = {
            muteAll: false,
            musicVolume: 80,
            soundVolume: 80,
            theme: 'default'
        };

        // 滑块拖拽状态
        this.dragging = {
            music: false,
            sound: false
        };

        this.init();
    }



    /**
     * 初始化设置页面
     */
    init() {
        // 优化Canvas渲染设置
        CanvasUtils.optimizeCanvasRendering(this.ctx);

        this.loadSettings();
        this.draw();
        this.bindEvents();
    }

    /**
     * 加载设置
     */
    loadSettings() {
        const audioSettings = this.app.audioManager.getSettings();
        this.settings.muteAll = audioSettings.muteAll;
        this.settings.musicVolume = Math.round(audioSettings.musicVolume * 100);
        this.settings.soundVolume = Math.round(audioSettings.soundVolume * 100);
        this.settings.theme = this.app.globalData.theme;
    }

    /**
     * 绘制设置页面
     */
    draw() {
        this.clearCanvas();
        this.drawBackground();
        this.drawHeader();
        this.drawMuteSwitch();
        this.drawVolumeControls();
    }

    /**
     * 清空画布
     */
    clearCanvas() {
        this.ctx.clearRect(0, 0, this.app.displayWidth, this.app.displayHeight);
    }

    /**
     * 绘制背景
     */
    drawBackground() {
        BackgroundRenderer.drawGradientBackground(this.ctx, this.app.displayWidth, this.app.displayHeight);
    }

    /**
     * 绘制页面头部
     */
    drawHeader() {
        const ctx = this.ctx;
        const headerY = 80; // 向下移动，避免刘海屏遮挡

        // 返回按钮 - 使用统一样式
        const buttonSize = 40;
        const buttonX = 20;
        const buttonY = 60;

        UIComponents.drawBackButton(ctx, buttonX, buttonY, buttonSize);

        // 页面标题 - 添加明显的标题
        ctx.fillStyle = CONFIG.ui.colors.primary;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.xlarge}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText('游戏设置', this.app.displayWidth / 2, headerY);

        // 存储返回按钮位置
        this.backButton = {
            x: buttonX,
            y: buttonY,
            width: buttonSize,
            height: buttonSize
        };
    }

    /**
     * 绘制静音开关
     */
    drawMuteSwitch() {
        const ctx = this.ctx;
        const startY = 140; // 向下移动，避免刘海屏遮挡
        const cardHeight = 80;

        // 背景卡片 - 使用纯白背景
        ctx.fillStyle = '#FFFFFF';
        ctx.strokeStyle = '#E0E0E0';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.roundRect(16, startY, this.app.displayWidth - 32, cardHeight, 12);
        ctx.fill();
        ctx.stroke();

        // 静音文字
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText('静音模式', 30, startY + cardHeight/2 + 6);

        // 开关按钮
        this.drawSwitch(this.app.displayWidth - 80, startY + cardHeight/2, this.settings.muteAll);

        // 存储开关位置供点击检测
        this.muteSwitch = {
            x: this.app.displayWidth - 105,
            y: startY + cardHeight/2 - 15,
            width: 50,
            height: 30
        };
    }

    /**
     * 绘制音量控制
     */
    drawVolumeControls() {
        const ctx = this.ctx;
        let currentY = 250; // 向下移动，避免重叠

        // 背景音量控制
        this.drawVolumeControl(currentY, '背景音量', this.settings.musicVolume, 'music');
        currentY += 120; // 增加间距

        // 音效音量控制
        this.drawVolumeControl(currentY, '音效音量', this.settings.soundVolume, 'sound');
    }

    /**
     * 绘制单个音量控制
     */
    drawVolumeControl(y, label, volume, type) {
        const ctx = this.ctx;
        const cardHeight = 80; // 减少高度，因为滑块在同一行
        const isDisabled = this.settings.muteAll;

        // 背景卡片 - 使用纯白背景
        ctx.fillStyle = isDisabled ? '#F5F5F5' : '#FFFFFF';
        ctx.strokeStyle = '#E0E0E0';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.roundRect(16, y, this.app.displayWidth - 32, cardHeight, 12);
        ctx.fill();
        ctx.stroke();

        // 标签文字 - 左侧显示
        ctx.fillStyle = isDisabled ? CONFIG.ui.colors.textLight : CONFIG.ui.colors.text;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText(label, 30, y + cardHeight/2 + 6);

        // 滑块 - 在同一行右侧，确保不超出背景框
        const labelWidth = 100; // 为标签预留的宽度
        const padding = 20; // 右侧边距
        const sliderWidth = Math.min(
            this.app.displayWidth * 0.4, // 最大40%屏幕宽度
            this.app.displayWidth - 32 - labelWidth - padding - 60 // 减去卡片边距、标签宽度、右边距和数值显示宽度
        );
        const sliderX = 30 + labelWidth + sliderWidth / 2; // 标签后面开始
        this.drawSlider(sliderX, y + cardHeight/2, sliderWidth, volume, isDisabled, type);
    }



    /**
     * 绘制开关
     */
    drawSwitch(x, y, isOn) {
        const ctx = this.ctx;
        const width = 50;
        const height = 30;

        // 开关背景
        ctx.fillStyle = isOn ? CONFIG.ui.colors.primary : '#DDDDDD';
        ctx.beginPath();
        ctx.roundRect(x - width/2, y - height/2, width, height, height/2);
        ctx.fill();

        // 开关滑块
        ctx.fillStyle = CONFIG.ui.colors.white;
        const sliderX = isOn ? x + width/4 : x - width/4;
        ctx.beginPath();
        ctx.arc(sliderX, y, height/2 - 3, 0, 2 * Math.PI);
        ctx.fill();

        // 添加滑块边框使其更明显
        ctx.strokeStyle = '#E0E0E0';
        ctx.lineWidth = 1;
        ctx.stroke();

        // 删除静音图标，避免红色元素出现
    }

    /**
     * 绘制滑块
     */
    drawSlider(x, y, width, value, disabled = false, type = '') {
        const ctx = this.ctx;
        const height = 6;
        const sliderRadius = 12;

        // 存储滑块位置信息供点击检测使用
        if (type === 'music') {
            this.musicSlider = {
                x: x - width/2,
                y: y - 20,
                width: width,
                height: 40
            };
        } else if (type === 'sound') {
            this.soundSlider = {
                x: x - width/2,
                y: y - 20,
                width: width,
                height: 40
            };
        }

        // 滑块轨道
        ctx.fillStyle = disabled ? '#E0E0E0' : '#DDDDDD';
        ctx.beginPath();
        ctx.roundRect(x - width/2, y - height/2, width, height, height/2);
        ctx.fill();

        // 滑块进度
        const progressWidth = (width * value) / 100;
        ctx.fillStyle = disabled ? '#CCCCCC' : CONFIG.ui.colors.primary;
        ctx.beginPath();
        ctx.roundRect(x - width/2, y - height/2, progressWidth, height, height/2);
        ctx.fill();

        // 滑块圆点
        const sliderX = x - width/2 + progressWidth;
        ctx.fillStyle = disabled ? '#CCCCCC' : CONFIG.ui.colors.primary;
        ctx.beginPath();
        ctx.arc(sliderX, y, sliderRadius, 0, 2 * Math.PI);
        ctx.fill();

        // 添加白色边框使滑块更明显
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 2;
        ctx.stroke();

        // 数值显示 - 显示在滑块右侧
        ctx.fillStyle = disabled ? CONFIG.ui.colors.textLight : CONFIG.ui.colors.text;
        ctx.font = `${CONFIG.ui.fonts.sizes.small}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText(`${value}%`, x + width/2 + 10, y + 4);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        tt.onTouchStart(this.handleTouchStart.bind(this));
        tt.onTouchMove(this.handleTouchMove.bind(this));
        tt.onTouchEnd(this.handleTouchEnd.bind(this));
    }

    /**
     * 处理触摸开始
     */
    handleTouchStart(e) {
        const touch = e.touches[0];
        const x = touch.clientX;
        const y = touch.clientY;

        // 返回按钮
        if (this.backButton && this.isPointInRect(x, y, this.backButton.x, this.backButton.y, this.backButton.width, this.backButton.height)) {
            this.app.playSound('match');
            this.back();
            return;
        }

        // 静音开关
        if (this.muteSwitch && this.isPointInRect(x, y, this.muteSwitch.x, this.muteSwitch.y, this.muteSwitch.width, this.muteSwitch.height)) {
            this.toggleMute();
            return;
        }

        // 音量滑块
        if (!this.settings.muteAll) {
            // 背景音量滑块
            if (this.musicSlider && this.isPointInRect(x, y, this.musicSlider.x, this.musicSlider.y, this.musicSlider.width, this.musicSlider.height)) {
                this.dragging.music = true;
                this.updateMusicVolume(x);
                return;
            }

            // 音效音量滑块
            if (this.soundSlider && this.isPointInRect(x, y, this.soundSlider.x, this.soundSlider.y, this.soundSlider.width, this.soundSlider.height)) {
                this.dragging.sound = true;
                this.updateSoundVolume(x);
                return;
            }
        }
    }

    /**
     * 处理触摸移动
     */
    handleTouchMove(e) {
        const touch = e.touches[0];
        const x = touch.clientX;

        if (this.dragging.music) {
            this.updateMusicVolume(x);
        } else if (this.dragging.sound) {
            this.updateSoundVolume(x);
        }
    }

    /**
     * 处理触摸结束
     */
    handleTouchEnd(e) {
        this.dragging.music = false;
        this.dragging.sound = false;
    }

    /**
     * 检查点是否在矩形内
     */
    isPointInRect(x, y, rectX, rectY, width, height) {
        return x >= rectX && x <= rectX + width &&
               y >= rectY && y <= rectY + height;
    }

    /**
     * 切换静音
     */
    toggleMute() {
        this.settings.muteAll = !this.settings.muteAll;
        this.app.audioManager.toggleMute();
        this.app.playSound('match');
        this.draw();
    }

    /**
     * 更新背景音量
     */
    updateMusicVolume(x) {
        if (!this.musicSlider) return;

        const minX = this.musicSlider.x;
        const maxX = this.musicSlider.x + this.musicSlider.width;

        const clampedX = Math.max(minX, Math.min(maxX, x));
        const volume = Math.round(((clampedX - minX) / this.musicSlider.width) * 100);

        this.settings.musicVolume = volume;
        this.app.audioManager.setMusicVolume(volume / 100);
        this.draw();
    }

    /**
     * 更新音效音量
     */
    updateSoundVolume(x) {
        if (!this.soundSlider) return;

        const minX = this.soundSlider.x;
        const maxX = this.soundSlider.x + this.soundSlider.width;

        const clampedX = Math.max(minX, Math.min(maxX, x));
        const volume = Math.round(((clampedX - minX) / this.soundSlider.width) * 100);

        this.settings.soundVolume = volume;
        this.app.audioManager.setSoundVolume(volume / 100);
        this.draw();
    }

    /**
     * 返回上一页
     */
    back() {
        this.destroy();
        this.app.goBack();
    }

    /**
     * 销毁页面
     */
    destroy() {
        tt.offTouchStart();
        tt.offTouchMove();
        tt.offTouchEnd();
    }
}

module.exports = SettingPage;