/**
 * 背景渲染器 - 统一的背景绘制类
 */

class BackgroundRenderer {
    /**
     * 绘制统一的浅紫粉渐变背景
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     */
    static drawGradientBackground(ctx, width, height) {
        // 更深的紫粉渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 0, height);
        gradient.addColorStop(0, '#C084D1'); // 更深的紫色
        gradient.addColorStop(0.5, '#E896C7'); // 更深的粉色
        gradient.addColorStop(1, '#F2C4DD'); // 更深的浅粉色

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
    }
    
    /**
     * 绘制五角星
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} x - 中心X坐标
     * @param {number} y - 中心Y坐标
     * @param {number} radius - 外半径
     */
    static drawStar(ctx, x, y, radius) {
        const spikes = 5;
        const outerRadius = radius;
        const innerRadius = radius * 0.4;
        
        ctx.beginPath();
        for (let i = 0; i < spikes * 2; i++) {
            const angle = (i * Math.PI) / spikes;
            const r = i % 2 === 0 ? outerRadius : innerRadius;
            const starX = x + Math.cos(angle) * r;
            const starY = y + Math.sin(angle) * r;
            
            if (i === 0) {
                ctx.moveTo(starX, starY);
            } else {
                ctx.lineTo(starX, starY);
            }
        }
        ctx.closePath();
        ctx.fill();
    }
    
    /**
     * 绘制带透明度的卡片背景
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} width - 宽度
     * @param {number} height - 高度
     * @param {number} radius - 圆角半径
     * @param {number} alpha - 透明度 (0-1)
     */
    static drawTransparentCard(ctx, x, y, width, height, radius = 15, alpha = 0.85) {
        ctx.save();
        
        // 外层阴影
        ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 4;
        ctx.shadowBlur = 8;
        
        ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
        ctx.beginPath();
        ctx.roundRect(x, y, width, height, radius);
        ctx.fill();
        
        ctx.restore();
        
        // 内层边框
        ctx.strokeStyle = `rgba(224, 224, 224, ${alpha})`;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.roundRect(x + 2, y + 2, width - 4, height - 4, radius - 2);
        ctx.stroke();
    }
}

module.exports = BackgroundRenderer;
