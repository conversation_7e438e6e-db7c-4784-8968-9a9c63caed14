/**
 * 图片加载器 - 统一的图片资源管理
 */

const CONFIG = require('../config.js');

class ImageLoader {
    constructor() {
        this.images = {};
        this.loadPromises = {};
    }
    
    /**
     * 加载单个图片
     * @param {string} key - 图片键名
     * @param {string} src - 图片路径
     * @returns {Promise} 加载Promise
     */
    loadImage(key, src) {
        if (this.images[key]) {
            return Promise.resolve(this.images[key]);
        }

        if (this.loadPromises[key]) {
            return this.loadPromises[key];
        }

        this.loadPromises[key] = new Promise((resolve, reject) => {
            console.log(`开始加载图片: ${src}`);

            // 检查是否在小游戏环境中
            if (typeof tt !== 'undefined' && tt.createImage) {
                // 抖音小游戏环境
                const img = tt.createImage();
                img.onload = () => {
                    console.log(`图片加载成功: ${src}, 尺寸: ${img.width}x${img.height}`);
                    // 确保图片有正确的尺寸信息
                    if (!img.width || !img.height) {
                        console.warn(`图片尺寸异常: ${src}`);
                    }
                    this.images[key] = img;
                    resolve(img);
                };
                img.onerror = (error) => {
                    console.error(`图片加载失败: ${src}`, error);
                    // 加载失败时也存储一个标记，避免重复加载
                    this.images[key] = { src, complete: false, error: true };
                    resolve(this.images[key]);
                };
                img.src = src;
            } else if (typeof Image !== 'undefined') {
                // 浏览器环境
                const img = new Image();
                img.onload = () => {
                    console.log(`图片加载成功: ${src}, 尺寸: ${img.width}x${img.height}`);
                    // 确保图片有正确的尺寸信息
                    if (!img.width || !img.height) {
                        console.warn(`图片尺寸异常: ${src}`);
                    }
                    this.images[key] = img;
                    resolve(img);
                };
                img.onerror = (error) => {
                    console.error(`图片加载失败: ${src}`, error);
                    this.images[key] = { src, complete: false, error: true };
                    resolve(this.images[key]);
                };
                img.src = src;
            } else {
                // 测试环境或不支持图片的环境
                console.log(`模拟加载图片: ${src}`);
                setTimeout(() => {
                    this.images[key] = { src, complete: false, mock: true };
                    resolve(this.images[key]);
                }, 100);
            }
        });

        return this.loadPromises[key];
    }
    
    /**
     * 批量加载动物图片
     * @returns {Promise} 加载Promise
     */
    loadAnimalImages() {
        const promises = CONFIG.animals.types.map(animal =>
            this.loadImage(animal.id.toString(), animal.image)
        );
        return Promise.all(promises);
    }
    
    /**
     * 批量加载道具图片
     * @returns {Promise} 加载Promise
     */
    loadPropImages() {
        const propPaths = {
            refresh: 'images/prop/refresh.png',
            bomb: 'images/prop/bomb.png', // 炸弹卡在根目录
            clear: 'images/prop/clear.png'
        };

        const promises = Object.entries(propPaths).map(([key, path]) =>
            this.loadImage(key, path)
        );
        return Promise.all(promises);
    }
    
    /**
     * 加载标题图片
     * @returns {Promise} 加载Promise
     */
    loadTitleImage() {
        return this.loadImage('title', 'images/title.jpg');
    }
    
    /**
     * 获取图片
     * @param {string} key - 图片键名
     * @returns {Image|null} 图片对象
     */
    getImage(key) {
        return this.images[key] || null;
    }
    
    /**
     * 检查图片是否已加载
     * @param {string} key - 图片键名
     * @returns {boolean} 是否已加载
     */
    isLoaded(key) {
        const img = this.images[key];
        if (!img) return false;

        // 真实图片对象
        if (img.complete !== undefined) {
            return img.complete && !img.error;
        }

        // 模拟对象
        return img.complete === true || img.mock === true;
    }
    
    /**
     * 绘制图片（带备用方案）
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {string} key - 图片键名
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} width - 宽度
     * @param {number} height - 高度
     * @param {string} fallbackText - 备用文字
     * @param {boolean} grayscale - 是否灰度显示
     */
    drawImage(ctx, key, x, y, width, height, fallbackText = '', grayscale = false) {
        const img = this.getImage(key);

        // 检查是否有有效的图片对象
        if (img && this.isLoaded(key) && !img.error) {
            ctx.save();

            // 禁用图像平滑以获得像素完美效果（适合像素艺术）
            ctx.imageSmoothingEnabled = false;

            // 如果图片尺寸较大，启用高质量平滑
            if (img.width > width * 2 || img.height > height * 2) {
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';
            }

            if (grayscale) {
                ctx.globalAlpha = 0.5;
                ctx.filter = 'grayscale(100%)';
            }

            // 绘制真实图片 - 使用整数坐标和尺寸
            if (img.width && img.height) {
                const drawX = Math.floor(x);
                const drawY = Math.floor(y);
                const drawWidth = Math.floor(width);
                const drawHeight = Math.floor(height);

                // 如果图片尺寸和绘制尺寸相同，使用原始尺寸绘制
                if (img.width === drawWidth && img.height === drawHeight) {
                    ctx.drawImage(img, drawX, drawY);
                } else {
                    // 使用完整的drawImage参数进行缩放
                    ctx.drawImage(img, 0, 0, img.width, img.height, drawX, drawY, drawWidth, drawHeight);
                }
            } else {
                // 如果是模拟对象，绘制高清彩色方块
                ctx.fillStyle = grayscale ? '#CCCCCC' : this.getColorForKey(key);
                ctx.fillRect(Math.floor(x), Math.floor(y), Math.floor(width), Math.floor(height));

                // 添加边框提高清晰度
                ctx.strokeStyle = grayscale ? '#999999' : this.darkenColor(this.getColorForKey(key), 20);
                ctx.lineWidth = 1;
                ctx.strokeRect(Math.floor(x), Math.floor(y), Math.floor(width), Math.floor(height));
            }
            ctx.restore();
        } else {
            // 使用文字备用方案 - 高清文字
            ctx.save();
            ctx.fillStyle = grayscale ? '#666666' : '#333333';

            // 使用更清晰的字体设置
            const fontSize = Math.min(width, height) * 0.4;
            ctx.font = `bold ${Math.floor(fontSize)}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // 如果有备用文字就显示，否则显示加载状态
            const displayText = fallbackText || (img && img.error ? '❌' : '⏳');
            ctx.fillText(displayText, Math.floor(x + width/2), Math.floor(y + height/2));
            ctx.restore();
        }
    }

    /**
     * 高清图片绘制（专门用于小尺寸图片）
     */
    drawCrispImage(ctx, key, x, y, width, height, fallbackText = '', grayscale = false) {
        const img = this.getImage(key);

        if (img && this.isLoaded(key) && !img.error && img.width && img.height) {
            ctx.save();

            // 完全禁用图像平滑，获得像素完美效果
            ctx.imageSmoothingEnabled = false;

            if (grayscale) {
                ctx.globalAlpha = 0.5;
                ctx.filter = 'grayscale(100%)';
            }

            // 计算最佳缩放比例
            const scaleX = width / img.width;
            const scaleY = height / img.height;
            const scale = Math.min(scaleX, scaleY);

            // 计算居中位置
            const scaledWidth = img.width * scale;
            const scaledHeight = img.height * scale;
            const offsetX = (width - scaledWidth) / 2;
            const offsetY = (height - scaledHeight) / 2;

            // 使用整数坐标绘制
            const drawX = Math.floor(x + offsetX);
            const drawY = Math.floor(y + offsetY);
            const drawWidth = Math.floor(scaledWidth);
            const drawHeight = Math.floor(scaledHeight);

            ctx.drawImage(img, 0, 0, img.width, img.height, drawX, drawY, drawWidth, drawHeight);
            ctx.restore();
        } else {
            // 使用原来的绘制方法作为备用
            this.drawImage(ctx, key, x, y, width, height, fallbackText, grayscale);
        }
    }

    /**
     * 颜色加深函数
     */
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }

    /**
     * 为不同的键获取不同的颜色（临时方案）
     */
    getColorForKey(key) {
        const colors = {
            // 动物颜色（使用id作为key）
            '0': '#FF9800', // 猫咪 - 橙色
            '1': '#2196F3', // 小狗 - 蓝色
            '2': '#9C27B0', // 大象 - 紫色
            '3': '#FF5722', // 狐狸 - 深橙色
            '4': '#4CAF50', // 青蛙 - 绿色
            '5': '#795548', // 猴子 - 棕色
            '6': '#424242', // 熊猫 - 深灰色
            '7': '#E91E63', // 兔子 - 粉色
            '8': '#FF6F00', // 老虎 - 深橙色
            // 道具颜色
            'refresh': '#00BCD4', // 刷新 - 青色
            'bomb': '#F44336',    // 炸弹 - 红色
            'clear': '#9C27B0'    // 清屏 - 紫色
        };
        return colors[key] || '#666666';
    }
    
    /**
     * 清理资源
     */
    destroy() {
        this.images = {};
        this.loadPromises = {};
    }
}

module.exports = ImageLoader;
