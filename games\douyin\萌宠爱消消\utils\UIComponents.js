/**
 * UI组件 - 通用UI绘制组件
 */

const CONFIG = require('../config.js');

class UIComponents {
    /**
     * 绘制按钮
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} x - 中心X坐标
     * @param {number} y - 中心Y坐标
     * @param {number} width - 宽度
     * @param {number} height - 高度
     * @param {string} text - 按钮文字
     * @param {string} color - 按钮颜色
     * @param {boolean} enabled - 是否启用
     */
    static drawButton(ctx, x, y, width, height, text, color, enabled = true) {
        const cornerRadius = height * 0.3;
        
        ctx.save();
        
        // 按钮阴影
        if (enabled) {
            ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 4;
            ctx.shadowBlur = 8;
        }
        
        // 按钮背景
        ctx.fillStyle = enabled ? color : '#CCCCCC';
        ctx.beginPath();
        ctx.roundRect(x - width/2, y - height/2, width, height, cornerRadius);
        ctx.fill();
        
        ctx.restore();
        
        // 按钮文字
        ctx.fillStyle = enabled ? CONFIG.ui.colors.white : '#666666';
        const fontSize = Math.min(height * 0.4, 24);
        ctx.font = `bold ${fontSize}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x, y);
    }
    
    /**
     * 绘制统一样式的返回按钮
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} size - 按钮尺寸
     */
    static drawBackButton(ctx, x, y, size = 40) {
        // 按钮背景 - 统一样式
        ctx.save();
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 3;
        ctx.shadowBlur = 6;

        // 渐变背景
        const gradient = ctx.createLinearGradient(x, y, x, y + size);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.95)');
        gradient.addColorStop(1, 'rgba(240, 240, 240, 0.95)');

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.roundRect(x, y, size, size, 10);
        ctx.fill();

        // 边框
        ctx.strokeStyle = 'rgba(200, 200, 200, 0.8)';
        ctx.lineWidth = 1;
        ctx.stroke();

        ctx.restore();

        // 返回箭头 - 更清晰的样式
        ctx.strokeStyle = CONFIG.ui.colors.primary;
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.beginPath();
        // 箭头主体
        ctx.moveTo(x + size * 0.65, y + size * 0.3);
        ctx.lineTo(x + size * 0.35, y + size * 0.5);
        ctx.lineTo(x + size * 0.65, y + size * 0.7);
        // 箭头线条
        ctx.moveTo(x + size * 0.35, y + size * 0.5);
        ctx.lineTo(x + size * 0.7, y + size * 0.5);
        ctx.stroke();
    }
    
    /**
     * 绘制进度条
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} width - 宽度
     * @param {number} height - 高度
     * @param {number} progress - 进度 (0-1)
     * @param {string} color - 进度条颜色
     */
    static drawProgressBar(ctx, x, y, width, height, progress, color) {
        const radius = height / 2;

        // 进度条背景
        ctx.fillStyle = '#D0D0D0';
        ctx.beginPath();
        ctx.roundRect(x, y, width, height, radius);
        ctx.fill();

        // 进度条填充 - 使用更明显的颜色
        if (progress > 0) {
            // 渐变填充，更明显
            const gradient = ctx.createLinearGradient(x, y, x + width, y);
            gradient.addColorStop(0, '#FF6B35'); // 橙红色
            gradient.addColorStop(1, '#F7931E'); // 橙色

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(x, y, width * Math.min(progress, 1), height, radius);
            ctx.fill();

            // 添加光泽效果
            ctx.save();
            ctx.globalAlpha = 0.3;
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.roundRect(x, y, width * Math.min(progress, 1), height/2, radius);
            ctx.fill();
            ctx.restore();
        }
    }
    
    /**
     * 绘制对话框
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} screenWidth - 屏幕宽度
     * @param {number} screenHeight - 屏幕高度
     * @param {string} title - 标题
     * @param {string} message - 消息内容
     * @param {Array} buttons - 按钮配置数组
     */
    static drawDialog(ctx, screenWidth, screenHeight, title, message, buttons = []) {
        const dialogWidth = 280;
        const dialogHeight = 180;
        const dialogX = (screenWidth - dialogWidth) / 2;
        const dialogY = (screenHeight - dialogHeight) / 2;
        
        // 半透明背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(0, 0, screenWidth, screenHeight);
        
        // 对话框背景
        ctx.save();
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 4;
        ctx.shadowBlur = 12;
        
        ctx.fillStyle = CONFIG.ui.colors.white;
        ctx.beginPath();
        ctx.roundRect(dialogX, dialogY, dialogWidth, dialogHeight, 16);
        ctx.fill();
        
        ctx.restore();
        
        // 标题
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.large}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText(title, dialogX + dialogWidth/2, dialogY + 50);
        
        // 消息内容
        ctx.fillStyle = CONFIG.ui.colors.textLight;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        const lines = message.split('\n');
        lines.forEach((line, index) => {
            ctx.fillText(line, dialogX + dialogWidth/2, dialogY + 80 + index * 25);
        });
        
        // 按钮
        if (buttons.length > 0) {
            const buttonWidth = 100;
            const buttonHeight = 40;
            const buttonY = dialogY + 130;
            const buttonSpacing = (dialogWidth - buttons.length * buttonWidth) / (buttons.length + 1);
            
            buttons.forEach((button, index) => {
                const buttonX = dialogX + buttonSpacing + index * (buttonWidth + buttonSpacing);
                
                ctx.fillStyle = button.primary ? CONFIG.ui.colors.primary : '#9E9E9E';
                ctx.beginPath();
                ctx.roundRect(buttonX, buttonY, buttonWidth, buttonHeight, 8);
                ctx.fill();
                
                ctx.fillStyle = CONFIG.ui.colors.white;
                ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
                ctx.fillText(button.text, buttonX + buttonWidth/2, buttonY + buttonHeight/2 + 6);
                
                // 存储按钮位置信息
                button.bounds = { x: buttonX, y: buttonY, width: buttonWidth, height: buttonHeight };
            });
        }
        
        return { x: dialogX, y: dialogY, width: dialogWidth, height: dialogHeight, buttons };
    }
    
    /**
     * 检查点是否在矩形内
     * @param {number} x - 点X坐标
     * @param {number} y - 点Y坐标
     * @param {number} rectX - 矩形X坐标
     * @param {number} rectY - 矩形Y坐标
     * @param {number} width - 矩形宽度
     * @param {number} height - 矩形高度
     * @returns {boolean} 是否在矩形内
     */
    static isPointInRect(x, y, rectX, rectY, width, height) {
        return x >= rectX && x <= rectX + width && 
               y >= rectY && y <= rectY + height;
    }
}

module.exports = UIComponents;
