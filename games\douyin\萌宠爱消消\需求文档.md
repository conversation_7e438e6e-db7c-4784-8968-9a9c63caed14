# 萌宠爱消消 - 功能需求文档

## 一、项目概述
- **游戏名称**：萌宠爱消消
- **游戏类型**：宠物主题消除类休闲游戏
- **运行平台**：抖音原生小游戏
- **技术栈**：JavaScript, Canvas
- **设备适配**：竖屏模式（portrait）

## 二、核心玩法系统
### 2.1 消除机制
- **基础规则**：采用3消、4消和5消机制
  - 3消：获得基础分（20分×倍率）
  - 4消：生成横向/纵向消除的小火箭
  - 5消：生成3x3范围消除的小炸弹
- **拖拽交互**：用户拖拽萌宠图标与相邻格子（上/下/左/右）交换，松手后判断有效性
- **无效处理**：未形成消除组合时图标自动归位

### 2.2 道具交互规则
| 组合方式 | 效果描述 | 得分 |
|----------|----------|------|
| 小火箭+普通动物 | 消除整列 | 100分 |
| 小火箭+小火箭 | 触发十字消除 | 200分 |
| 小炸弹+普通动物 | 3x3范围消除 | 300分 |
| 小炸弹+小火箭 | 3列消除 | 400分 |
| 小炸弹+小炸弹 | 5x5范围爆炸 | 500分 |

### 2.3 道具卡系统
| 道具名称 | 效果描述 | 得分 | 获取方式 |
|----------|----------|------|----------|
| 刷新卡 | 打乱萌宠位置 | - | [每天登录赠送1张] |
| 炸弹卡 | 5x5范围爆炸 | 500分 | 分享游戏 |
| 清屏卡 | 消除所有动物 | 800分 | 邀请好友 |

## 三、关卡设计
| 关卡 | 难度 | 目标分数 | 萌宠种类 | 标题 |
|------|------|----------|----------|------|
| 1 | 非常简单 | 1000分 | 5种 | 萌宠新手村 |
| 2 | 简单 | 4000分 | 7种 | 萌宠大练兵 |
| 3 | 超级困难 | 8000分 | 9种 | 萌宠总动员 |

### 3.1 通关规则
- 成功条件：分数≥目标分数，自动进入下一关
- 终极通关：完成第三关后返回首页

### 3.2 失败与复活机制
- 失败判定：场上无有效可消除组合
- 复活规则：每局可免费复活2次（触发刷新卡效果），超过次数后只能重新开始或返回主页

## 四、得分系统
### 4.1 连击倍率规则
| 连击次数 | 得分倍率 |
|----------|----------|
| ≤3次 | 1.0x |
| 4-5次 | 1.5x |
| >5次 | 2.0x |

### 4.2 基础分值
- 普通消除：20分×当前倍率
- 特效消除：在基础分值上叠加特效加成

## 五、音频系统
| 游戏事件 | 音效文件 |
|----------|----------|
| 普通消除(3消) | audios/so.mp3 |
| 小火箭消除 | audios/shua.mp3 |
| 小炸弹触发 | audios/bomb.mp3 |
| 3连击 | audios/wa.mp3 |
| 5连击 | audios/good.mp3 |

## 六、主题系统
### 6.1 主题类型
| 主题类型 | 目标人群 | 风格特点 |
|----------|----------|----------|
| 通用主题 | 全年龄段 | 中性配色，简约萌宠形象 |

## 七、资源配置
### 7.1 图像资源
- 萌宠图片路径：images/animal/
- UI元素路径：images/button/, images/icon/, images/prop/

### 7.2 音频资源
- 存放路径：audios/
- 格式要求：MP3格式，单个文件≤500KB

### 7.3 配置文件 (config.js)
- **关卡参数配置**：
  ```javascript
  levels: [
    { targetScore: 1000, animalTypes: 5 },  // 第一关
    { targetScore: 4000, animalTypes: 7 },  // 第二关
    { targetScore: 8000, animalTypes: 9 }   // 第三关
  ]
  ```
- **资源路径配置**：
  ```javascript
  resources: {
    animalImages: 'images/animal/',
    uiButtons: 'images/button/',
    uiIcons: 'images/icon/',
    props: 'images/prop/',
    audios: 'audios/'
  }
  ```
- 存放路径：audios/
- 格式要求：MP3格式，单个文件≤500KB

## 八、交互反馈
- **拖拽反馈**：被拖拽图标跟随移动，目标位置高亮
- **连击特效**：显示连击次数和倍率动画
- **道具使用**：播放对应特效动画和音效

## 九、社交功能
- 分享功能：分享游戏获取炸弹卡
- 邀请功能：邀请好友获取清屏卡

## 十、UI页面设计
> **开发优先级**：最高（需优先实现以确保核心交互流程可用）

### 10.1 主页设计详情

#### 10.1.1 整体布局结构
- **屏幕适配**：竖屏9:16比例设计，适配主流手机屏幕分辨率
- **区域划分**：
  - 顶部区域（占屏幕高度30%）：主标题图片 + 版本标题
  - 中间区域（占屏幕高度40%）：三个功能按钮（垂直居中分布）
  - 底部区域（占屏幕高度30%）：游戏Logo + 版权信息
- **安全区域**：所有内容距离屏幕边缘保持16px边距，避免被设备圆角或刘海遮挡

#### 10.1.2 标题系统设计
- **主标题**：
  - 显示方式：图片形式展示，替代原有文字标题
  - 图片路径：images/title.png
  - 尺寸属性：宽度100%容器宽度，高度自动（保持原始宽高比），最大高度为顶部区域高度的70%
  - 加载效果：淡入动画（0.5秒缓入）
  - 位置：顶部区域居中显示

- **版本标题**：
  - 内容来源：从config.js读取versionTitle配置项
  - 样式规范：HarmonyOS Sans SC, Medium, 16px, #666666，居中对齐
  - 位置：主标题图片下方，占顶部区域高度的30%

#### 10.1.3 按钮系统设计
- **基础规范**：
  - 统一宽高比：2.5:1（宽度:高度）
  - 动态尺寸计算：
    ```javascript
    const BUTTON_ASPECT_RATIO = 2.5;
    function calculateButtonSize() {
      const containerWidth = document.getElementById('button-container').offsetWidth;
      const buttonWidth = containerWidth * 0.8; // 占容器宽度80%
      const buttonHeight = buttonWidth / BUTTON_ASPECT_RATIO;
      return { width: buttonWidth, height: buttonHeight };
    }
    ```
  - 响应式调整：监听窗口大小变化事件，实时重新计算按钮尺寸
  - 间距设置：按钮之间垂直间距为屏幕高度的3%

- **按钮样式**：
  - 共同样式：背景色#FF85A1，文字色#FFFFFF，1px白色描边，圆角为高度的30%，HarmonyOS Sans SC Bold字体，文字大小为按钮高度的35%，阴影0 4px 8px rgba(255, 133, 161, 0.3)
  - 按钮内容：
    1. 开始游戏按钮：「开始游戏」+→
    2. 排行榜按钮：「排行榜」+下划线
    3. 设置按钮：「设置」+齿轮图形
  - 交互效果：点击状态背景色加深10%并缩放至0.98倍，释放后恢复，支持悬停设备添加顶部渐变光效

#### 10.1.4 底部区域设计
- **游戏Logo**：高度为底部区域的20%，底部区域顶部居中，图片路径images/logo.png
- **版权信息**：© 2023 萌宠爱消消 版权所有，HarmonyOS Sans SC Regular, 12px, #999999，底部区域底部居中

#### 10.1.5 动画与过渡效果
- **页面加载动画**：标题图片0.5秒淡入，版本标题0.3秒延迟淡入，按钮组依次从下往上滑入（间隔0.15秒），底部元素0.5秒延迟淡入
- **交互反馈动画**：按钮点击缩放+颜色变化（0.2秒），页面跳转滑动过渡效果（0.3秒）

#### 10.1.6 资源引用规范
- 图片资源：主标题(images/title.png)、Logo(images/logo.png)、背景纹理(images/bg-texture.png)
- 配置文件：版本标题配置(config.js中的versionTitle字段)

#### 10.1.7 适配与兼容性
- 分辨率适配：通过动态计算实现全分辨率适配
- 设备方向：锁定竖屏显示
- 性能优化：图片资源预加载，动画使用CSS transform属性
### 10.1 设置页面（pages/SettingPagejs）
- **功能模块**：
  - **布局**：垂直排列的三个核心控制组件，组件间距24px
  - **静音模式总开关**：
    - 容器：白色半透明卡片（rgba(255,255,255,0.8)），8px圆角，4px阴影
    - 左侧：🔇 图标 + 「静音模式」文字（HarmonyOS Sans SC, Medium, 16px, #333333）
    - 右侧开关：圆角矩形滑块（50×30px），开启状态#FF85A1（附带斜杠静音图标），关闭状态#DDDDDD
    - 交互：点击切换状态，0.2秒过渡动画，开启时下方音量控件变为半透明禁用状态
  - **背景音量控制**：
    - 容器：水平布局，高度60px
    - 左侧：🎵 图标 + 「背景音量」文字（同上）
    - 右侧滑块：
      - 轨道：灰色条（300px×6px，20px圆角）
      - 滑块：圆形（20px直径），#FF85A1填充
      - 范围：0-100，默认值80
      - 刻度：每20%显示一个刻度标记
    - 交互：拖动滑块实时调整，松手后保存值
  - **音效音量控制**：
    - 左侧：🔊 图标 + 「音效音量」文字（同上）
    - 右侧滑块：与背景音量相同规格
    - 默认值：80
    - 联动效果：静音模式开启时，滑块值保留但视觉置灰
  - 主题选择：通用中性/女性向/男性向三种主题切换
- **存储机制**：本地存储用户偏好设置，无需重启即时生效

# 排行榜页面设计详情

## 1. 整体布局结构
- **适配方案**：延续9:16竖屏设计，采用卡片式分层布局
- **区域划分**：
  - 顶部导航区（80px高）：返回按钮 + 页面标题
  - 个人排名区（120px高）：用户头像 + 排名 + 得分
  - 标签切换区（60px高）：全部/地区/好友
  - 排行榜列表区（剩余高度-底部信息区）：滚动列表展示排名数据
  - 底部信息区（50px高）：更新时间提示
- **背景设计**：浅粉色渐变背景（#FFF5F8 至 #FFEFF3），顶部添加云朵装饰元素

## 2. 导航区域设计
- **返回按钮**：
  - 位置：左上角，距屏幕左边缘16px，垂直居中
  - 样式：← 图标 + 「返回」文字（HarmonyOS Sans SC, Medium, 16px, #333333）
  - 尺寸：44×44px点击区域
  - 交互：点击返回主页（game.js）

- **页面标题**：
  - 内容：「排行榜」
  - 样式：HarmonyOS Sans SC, Bold, 20px, #333333
  - 位置：导航区中央

## 3. 个人排名区设计
- **布局**：水平排列，左侧头像，中间排名，右侧得分
- **用户头像**：
  - 尺寸：60px直径圆形
  - 边框：3px solid #FF85A1
  - 位置：左侧，距左边缘24px
- **排名信息**：
  - 排名文字：「我的排名：」+ 具体名次（如12）
  - 样式：HarmonyOS Sans SC, Medium, 16px, #666666
- **得分信息**：
  - 得分文字：「我的得分：」+ 具体分数（如8500）
  - 样式：HarmonyOS Sans SC, Bold, 18px, #FF85A1
- **背景容器**：
  - 尺寸：100%屏幕宽度，120px高
  - 背景：白色半透明卡片（rgba(255,255,255,0.8)）
  - 圆角：12px
  - 阴影：0 4px 12px rgba(0,0,0,0.05)

## 4. 标签切换区设计
- **布局**：水平排列三个标签，等宽分布
- **样式规范**：
  - 未选中状态：文字#666666，底部无指示器
  - 选中状态：文字#FF85A1，底部2px高#FF85A1指示器（宽度80%文字宽度，居中）
  - 字体：HarmonyOS Sans SC, Medium, 16px
- **标签内容**：「全部」/「地区」/「好友」
- **交互效果**：点击切换标签，伴随0.3秒平滑过渡动画
- **默认选中**：全部

## 5. 排行榜列表设计
### 5.1 前三名特殊展示区
- **布局**：水平排列，占列表区顶部20%高度
- **冠军项**：
  - 背景：金色渐变卡片（#FFE5A0 至 #FFD766），8px圆角
  - 内容：「1」+ 用户名 + 分数
  - 尺寸：宽度30%屏幕宽度，高度120px
  - 位置：中间（突出显示）

- **亚军/季军项**：
  - 亚军背景：银色渐变（#E8E8E8 至 #D0D0D0），内容：「2」+ 用户名 + 分数
  - 季军背景：铜色渐变（#FFD0A0 至 #FFB366），内容：「3」+ 用户名 + 分数
  - 尺寸：宽度25%屏幕宽度，高度100px
  - 位置：冠军左右两侧，略低于冠军卡片

### 5.2 常规排名列表区
- **列表项设计**：
  - 高度：80px
  - 背景：白色半透明卡片（rgba(255,255,255,0.7)），8px圆角
  - 间距：项间距8px，左右边距16px
  - 内容布局：
    - 排名数字：左侧，HarmonyOS Sans SC, Bold, 18px
    - 用户头像：圆形（40px直径），带2px白色边框
    - 用户名：HarmonyOS Sans SC, Medium, 16px, #333333
    - 分数：右侧，HarmonyOS Sans SC, Bold, 18px, #FF85A1
  - 排名样式区分：
    - 1-3名：特殊展示区已覆盖
    - 4-10名：排名数字#FFA0B3
    - 11+名：排名数字#999999

## 10.3 游戏界面设计详情

### 1. 整体布局结构
- **适配方案**：延续9:16竖屏设计，采用上中下三部分布局
- **区域划分**：
  - **顶部统计栏**（120px高）：关卡标题 + 分数/目标/进度
  - **中部游戏区**：8×10网格 + 游戏元素
  - **底部道具栏**（100px高）：三种道具卡片 + 操作按钮
- **统一规范**：
  - 所有区域宽度均为屏幕宽度的80%，水平居中
  - 区域间距：使用`spacing_var`变量控制（默认15px，可配置范围10-20px）
    - 统计栏底部与网格顶部间距 = `spacing_var`
    - 网格底部与道具栏顶部间距 = `spacing_var`
  - 背景设计：浅紫色渐变背景（#F9F0FA 至 #F0E5F5），添加轻微颗粒质感

### 2. 顶部统计栏设计
- **关卡标题**：
  - 位置：统计栏最上方，居中对齐
  - 样式：HarmonyOS Sans SC, Bold, 22px, #333333
  - 内容：「关卡 X」（X为当前关卡数）
  - 装饰：两侧添加对称的粉色花朵图标（24×24px）

- **统计信息区**（第二行）：
  - 布局：水平三等分，每部分占统计栏宽度的1/3
  - **分数显示**：
    - 图标：钻石形状（28×28px），#FFD1DC填充
    - 文字：
      - 标题：「分数」（HarmonyOS Sans SC, Regular, 14px, #666666）
      - 数值：动态变化（HarmonyOS Sans SC, Bold, 18px, #333333）
  - **目标显示**：
    - 图标：目标旗帜（28×28px），#FFB6C1填充
    - 文字：
      - 标题：「目标」（HarmonyOS Sans SC, Regular, 14px, #666666）
      - 数值：「消除XX个」（HarmonyOS Sans SC, Bold, 18px, #333333）
  - **进度显示**：
    - 图标：进度条（28×12px），#FF85A1填充
    - 文字：
      - 标题：「进度」（HarmonyOS Sans SC, Regular, 14px, #666666）
      - 数值：「XX%」（HarmonyOS Sans SC, Bold, 18px, #333333）

### 2. 顶部信息区设计
- **分数显示**：
  - 布局：左侧，图标+数字垂直排列
  - 图标：钻石形状（30×30px），#FFD1DC填充
  - 文字：
    - 标题：「分数」（HarmonyOS Sans SC, Regular, 14px, #666666）
    - 数值：动态变化（HarmonyOS Sans SC, Bold, 24px, #333333）
  - 动画：分数增加时数字向上弹跳效果（0.3秒）

- **生命值/步数**：
  - 布局：右侧，图标+进度条/数字
  - 生命值样式：爱心图标（24×24px），填充色#FF85A1，剩余数量用图标显示
  - 步数样式：步数图标（24×24px）+ 数字（HarmonyOS Sans SC, Medium, 18px, #333333）
  - 位置：距右边缘24px，垂直居中

### 3. 中部游戏区设计
- **网格布局**：
  - 尺寸：横向8格 × 纵向10格固定布局
  - 格子计算：动态尺寸 = (屏幕宽度 × 0.8 - 7×5px) / 8
    （注：7为间隔数，5px为格子间距）
  - 容器规范：
    - 宽度：屏幕宽度的80%（与统计栏/道具栏对齐）
    - 背景：白色半透明卡片（rgba(255,255,255,0.9)），12px圆角
    - 内边距：10px
    - 位置：距离统计栏底部`spacing_var`像素（默认15px）

- **游戏元素**：
  - 元素类型：动物头像（猫、狗、兔子等），尺寸为格子大小的85%
  - 消除效果：选中时缩放至1.1倍，消除时0.2秒爆炸动画+分数弹出
  - 移动反馈：滑动时有轻微位移跟随，释放后归位或消除

### 4. 底部道具栏设计
- **整体规范**：
  - 宽度：屏幕宽度的80%（与统计栏/游戏区对齐）
  - 高度：100px
  - 位置：距离游戏区底部`spacing_var`像素（与游戏区顶部间距一致）
  - 背景：白色半透明卡片（rgba(255,255,255,0.85)），12px圆角
  - 内边距：水平20px，垂直15px

- **道具卡片**：
  - 布局：水平排列3个道具，等间距分布（间距=（容器宽度-3×60px）/4）
  - 尺寸：每个道具卡60×60px，图标50×50px居中
  - 具体道具：
    1. **刷新卡**：图标为循环箭头，#A1BFFF填充
    2. **炸弹卡**：图标为炸弹形状，#FFB3BA填充
    3. **清屏卡**：图标为全屏清除，#BAE6FD填充
  - 数量显示：
    - 位置：道具卡右上角，10px偏移（上-5px，右-5px）
    - 样式：HarmonyOS Sans SC, Bold, 14px, #FF3B30（红色）
    - 背景：白色圆形背景（18×18px），1px灰色边框
    - 示例：「3」表示剩余3个道具

- **暂停按钮**：
  - 位置：道具栏右侧，44×44px圆形
  - 样式：白色背景，#333333暂停图标（▣）
  - 交互：点击弹出暂停菜单（继续/重新开始/返回主页）

### 5. 状态提示区设计
- **关卡目标**：
  - 位置：游戏区顶部中央，悬浮显示
  - 样式：白色半透明卡片（rgba(255,255,255,0.9)），8px圆角
  - 内容：「目标：消除30个猫咪」（HarmonyOS Sans SC, Medium, 16px, #333333）

- **剩余时间**（限时模式）：
  - 位置：关卡目标右侧
  - 样式：红色倒计时数字（HarmonyOS Sans SC, Bold, 18px, #FF4D4F）
  - 动画：时间<10秒时闪烁效果

### 6. 动画与交互效果
- **元素消除**：方块爆破动画（0.2秒）+ 分数上浮效果
- **新元素生成**：从顶部下落动画（0.3秒，带缓动效果）
- **连击奖励**：连续消除时屏幕边缘闪烁金色光芒
- **关卡完成**：全屏绽放动画 + 星星收集效果

### 7. 适配与性能优化
- **分辨率适配**：游戏网格采用百分比宽度，确保在360px-420px宽度设备上显示完整
- **性能优化**：
  - 动画帧率限制为60fps
  - 非活跃元素降低渲染优先级
  - 大量元素同时消除时分批处理动画

## 十一、页面导航设计

### 11.1 导航按钮跳转逻辑
- **开始游戏**按钮：点击后跳转至`pages/GamePage.js`页面
- **排行榜**按钮：点击后跳转至`pages/RankPage.js`页面
- **设置**按钮：点击后跳转至`pages/SettingPage.js`页面

## 十二、技术实现要求

### 11.1 开发方案
- **技术架构**：采用抖音原生小游戏方案（Native方案 + 抖音官方API）
- **核心API**：使用抖音小游戏提供的tt对象API进行开发
- **渲染方式**：基于Canvas 2D上下文进行游戏画面绘制

### 11.2 环境初始化示例
```javascript
// 创建游戏画布
this.canvas = tt.createCanvas();
this.ctx = this.canvas.getContext('2d');

// 获取系统信息并设置画布尺寸
const systemInfo = tt.getSystemInfoSync();
this.canvas.width = systemInfo.windowWidth;
this.canvas.height = systemInfo.windowHeight;
```

## 十二、主页布局设计
### 11.1 标题设计
- **资源路径**：
  - 粉色主题：images/title.jpg
  - 紫色主题：images/title.png
- **显示位置**：屏幕顶部中央，占屏幕宽度80%
- **主题适配**：根据当前选中主题自动切换对应颜色的标题图片

### 11.2 按钮设计
| 按钮名称 | 位置 | 尺寸 | 图片资源 | 功能描述 |
|----------|------|------|----------|----------|
| 开始游戏 | 屏幕中央偏下 | 200×80px | images/button/start.png | 点击进入关卡选择界面 |
| 排行榜 | 开始游戏按钮左侧 | 120×60px | images/button/rank.png | 查看玩家分数排名 |
| 设置 | 开始游戏按钮右侧 | 120×60px | images/button/setting.png | 打开设置面板（主题切换、音效开关等）|

### 11.3 布局规范
- 整体采用垂直居中布局
- 标题与按钮间距：50px
- 按钮之间横向间距：30px
- 底部留出10%屏幕高度的空白区域