/**
 * 测试清屏卡数量显示
 */

// 模拟抖音小游戏环境
global.tt = {
    createCanvas: () => ({
        width: 375,
        height: 667,
        getContext: () => ({
            clearRect: () => {},
            fillRect: () => {},
            fillText: (text, x, y) => {
                if (text === '0' || text === '00') {
                    console.log(`⚠️ 发现数字显示: "${text}" at (${x}, ${y})`);
                }
            },
            strokeRect: () => {},
            beginPath: () => {},
            arc: () => {},
            fill: () => {},
            stroke: () => {},
            roundRect: () => {},
            createLinearGradient: () => ({
                addColorStop: () => {}
            }),
            scale: () => {},
            save: () => {},
            restore: () => {},
            drawImage: () => {},
            strokeText: () => {},
            moveTo: () => {},
            lineTo: () => {},
            quadraticCurveTo: () => {},
            closePath: () => {},
            measureText: () => ({ width: 50 }),
            translate: () => {},
            rotate: () => {},
            lineCap: '',
            lineJoin: '',
            globalAlpha: 1,
            filter: '',
            textAlign: 'center',
            textBaseline: 'middle',
            font: '16px Arial',
            fillStyle: '#000000',
            shadowColor: '',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            shadowBlur: 0,
            imageSmoothingEnabled: false,
            imageSmoothingQuality: 'high',
            textRenderingOptimization: 'optimizeQuality'
        }),
        style: {}
    }),
    createImage: () => ({
        src: '',
        width: 64,
        height: 64,
        complete: true,
        onload: null,
        onerror: null,
        set src(value) {
            this._src = value;
            setTimeout(() => {
                this.complete = true;
                if (this.onload) this.onload();
            }, 10);
        },
        get src() { return this._src; }
    }),
    getSystemInfoSync: () => ({
        windowWidth: 375,
        windowHeight: 667,
        pixelRatio: 2
    })
};

global.Image = () => tt.createImage();
global.requestAnimationFrame = (callback) => setTimeout(callback, 16);
global.cancelAnimationFrame = () => {};

console.log('🔧 测试清屏卡数量显示...\n');

// 测试配置
function testConfig() {
    try {
        const CONFIG = require('./config.js');
        
        console.log('✅ 配置检查：');
        console.log(`  清屏卡maxCount: ${CONFIG.props.clear.maxCount}`);
        console.log('');
        
        return CONFIG;
    } catch (error) {
        console.error('❌ 配置测试失败:', error.message);
        return null;
    }
}

// 测试GamePage
function testGamePage() {
    try {
        const mockApp = {
            displayWidth: 375,
            displayHeight: 667,
            canvas: { width: 750, height: 1334 },
            ctx: tt.createCanvas().getContext('2d'),
            audioManager: { getSettings: () => ({}) },
            globalData: { currentLevel: 1 },
            playSound: () => {},
            showHomePage: () => {}
        };
        
        const GamePage = require('./pages/GamePage.js');
        const gamePage = new GamePage(mockApp);
        
        console.log('✅ GamePage道具状态：');
        console.log(`  刷新卡数量: ${gamePage.gameState.props.refresh}`);
        console.log(`  炸弹卡数量: ${gamePage.gameState.props.bomb}`);
        console.log(`  清屏卡数量: ${gamePage.gameState.props.clear}`);
        console.log('');
        
        // 模拟绘制道具栏
        console.log('🎨 模拟绘制道具栏...');
        try {
            gamePage.drawPropBar();
            console.log('✅ 道具栏绘制完成，未发现异常数字显示');
        } catch (error) {
            console.log('⚠️ 道具栏绘制出错:', error.message);
        }
        
        return gamePage;
    } catch (error) {
        console.error('❌ GamePage测试失败:', error.message);
        return null;
    }
}

// 运行测试
(async () => {
    const config = testConfig();
    const gamePage = testGamePage();
    
    console.log('🎯 清屏卡测试完成！');
    console.log('\n📋 分析结果：');
    
    if (config && gamePage) {
        console.log('🔍 问题分析：');
        console.log(`  • 配置文件中清屏卡maxCount: ${config.props.clear.maxCount}`);
        console.log(`  • 游戏状态中清屏卡数量: ${gamePage.gameState.props.clear}`);
        console.log(`  • 显示逻辑: count > 0 时才显示徽章`);
        
        if (gamePage.gameState.props.clear === 0) {
            console.log('✅ 清屏卡数量为0，不应该显示徽章');
        } else {
            console.log('⚠️ 清屏卡数量不为0，会显示徽章');
        }
        
        console.log('\n💡 建议解决方案：');
        console.log('  1. 确认config.js中clear.maxCount = 0');
        console.log('  2. 重新启动游戏，确保使用新配置');
        console.log('  3. 检查是否有缓存的游戏状态');
        console.log('  4. 确认道具栏绘制逻辑正确');
    }
})();
