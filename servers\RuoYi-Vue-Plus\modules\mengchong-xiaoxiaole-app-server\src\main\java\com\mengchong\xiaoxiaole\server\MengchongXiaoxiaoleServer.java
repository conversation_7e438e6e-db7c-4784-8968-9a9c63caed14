package com.mengchong.xiaoxiaole.server;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MengchongXiaoxiaoleServer {
    private static final Logger logger = LoggerFactory.getLogger(MengchongXiaoxiaoleServer.class);
    private final int port;

    public MengchongXiaoxiaoleServer(int port) {
        this.port = port;
    }

    public void start() throws Exception {
        // 配置服务端NIO线程组
        EventLoopGroup bossGroup = new NioEventLoopGroup(1);
        EventLoopGroup workerGroup = new NioEventLoopGroup();

        try {
            ServerBootstrap b = new ServerBootstrap();
            b.group(bossGroup, workerGroup)
             .channel(NioServerSocketChannel.class)
             .option(ChannelOption.SO_BACKLOG, 1024)
             .childHandler(new WebSocketServerInitializer());

            // 绑定端口，同步等待成功
            ChannelFuture f = b.bind(port).sync();
            logger.info("萌宠消消乐WebSocket服务已启动，端口: {}", port);

            // 等待服务端监听端口关闭
            f.channel().closeFuture().sync();
        } finally {
            // 优雅退出，释放线程池资源
            workerGroup.shutdownGracefully();
            bossGroup.shutdownGracefully();
        }
    }

    public static void main(String[] args) throws Exception {
        int port = 8088;
        if (args.length > 0) {
            port = Integer.parseInt(args[0]);
        }
        new MengchongXiaoxiaoleServer(port).start();
    }
}