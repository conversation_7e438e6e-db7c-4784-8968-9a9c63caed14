package com.mengchong.xiaoxiaole.server.handler;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
import io.netty.handler.codec.protobuf.ProtobufDecoder;
import io.netty.handler.codec.protobuf.ProtobufEncoder;
import io.netty.handler.codec.protobuf.ProtobufVarint32FrameDecoder;
import io.netty.handler.codec.protobuf.ProtobufVarint32LengthFieldPrepender;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.stream.ChunkedWriteHandler;

public class WebSocketServerInitializer extends ChannelInitializer<SocketChannel> {

    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();

        // 日志处理器
        pipeline.addLast(new LoggingHandler(LogLevel.INFO));

        // HTTP编解码器
        pipeline.addLast(new HttpServerCodec());
        // 大数据流处理器
        pipeline.addLast(new ChunkedWriteHandler());
        // HTTP消息聚合器 (max content length 64KB)
        pipeline.addLast(new HttpObjectAggregator(65536));

        // WebSocket协议升级处理器
        pipeline.addLast(new WebSocketServerProtocolHandler("/game", null, true, 65536 * 10));

        // Protobuf帧解码器 (处理半包问题)
        pipeline.addLast(new ProtobufVarint32FrameDecoder());
        // Protobuf解码器 (需要指定初始消息类型)
        pipeline.addLast(new ProtobufDecoder(GameMessage.getDefaultInstance()));

        // Protobuf帧编码器
        pipeline.addLast(new ProtobufVarint32LengthFieldPrepender());
        // Protobuf编码器
        pipeline.addLast(new ProtobufEncoder());

        // 自定义游戏消息处理器
        pipeline.addLast(new GameMessageHandler());
    }
}