package com.mengchong.xiaoxiaole.server.message;

/**
 * 消息类型枚举
 */
public enum MessageType {
    LOGIN_REQUEST(1),
    LOGIN_RESPONSE(2),
    GAME_MOVE(3),
    MATCH_RESULT(4),
    HEARTBEAT(5),
    GAME_OVER(6);

    private final int value;

    MessageType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static MessageType fromValue(int value) {
        for (MessageType type : MessageType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}