<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.dromara</groupId>
    <artifactId>ruoyi-vue-plus</artifactId>
    <version>5.4.1</version>
  </parent>
  <groupId>org.dromara</groupId>
  <artifactId>ruoyi-admin</artifactId>
  <version>5.4.1</version>
  <description>web服务入口</description>
  <dependencies>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-doc</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-social</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-ratelimiter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-mail</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-system</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-job</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-generator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-demo</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-workflow</artifactId>
    </dependency>
    <dependency>
      <groupId>de.codecentric</groupId>
      <artifactId>spring-boot-admin-starter-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${maven-jar-plugin.version}</version>
      </plugin>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
        <version>${maven-war-plugin.version}</version>
        <configuration>
          <failOnMissingWebXml>false</failOnMissingWebXml>
          <warName>${project.artifactId}</warName>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
