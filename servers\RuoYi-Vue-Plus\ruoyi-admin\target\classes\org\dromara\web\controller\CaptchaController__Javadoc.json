{"doc": " 验证码操作处理\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "smsCode", "paramTypes": ["java.lang.String"], "doc": " 短信验证码\n\n @param phonenumber 用户手机号\n"}, {"name": "emailCode", "paramTypes": ["java.lang.String"], "doc": " 邮箱验证码\n\n @param email 邮箱\n"}, {"name": "emailCodeImpl", "paramTypes": ["java.lang.String"], "doc": " 邮箱验证码\n 独立方法避免验证码关闭之后仍然走限流\n"}, {"name": "getCode", "paramTypes": [], "doc": " 生成验证码\n"}, {"name": "getCodeImpl", "paramTypes": [], "doc": " 生成验证码\n 独立方法避免验证码关闭之后仍然走限流\n"}], "constructors": []}