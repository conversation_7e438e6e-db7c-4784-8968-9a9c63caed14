{"doc": " 任务受让人\n\n <AUTHOR>\n", "fields": [{"name": "total", "doc": " 总大小\n"}], "enumConstants": [], "methods": [{"name": "convertToHandlerList", "paramTypes": ["java.util.List", "java.util.function.Function", "java.util.function.Function", "java.util.function.Function", "java.util.function.Function", "java.util.function.Function"], "doc": " 将源列表转换为 TaskHandler 列表\n\n @param <T>              通用类型\n @param sourceList       待转换的源列表\n @param storageId        提取 storageId 的函数\n @param handlerCode      提取 handlerCode 的函数\n @param handlerName      提取 handlerName 的函数\n @param groupName        提取 groupName 的函数\n @param createTimeMapper 提取 createTime 的函数\n @return 转换后的 TaskHandler 列表\n"}], "constructors": []}