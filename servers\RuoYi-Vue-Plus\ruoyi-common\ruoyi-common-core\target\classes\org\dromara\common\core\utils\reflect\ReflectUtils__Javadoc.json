{"doc": " 反射工具类. 提供调用getter/setter方法, 访问私有变量, 调用私有方法, 获取泛型类型Class, 被AOP过的真实类等工具函数.\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "invokeGetter", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": " 调用Getter方法.\n 支持多级，如：对象名.对象名.方法\n"}, {"name": "invokeSetter", "paramTypes": ["java.lang.Object", "java.lang.String", "java.lang.Object"], "doc": " 调用Setter方法, 仅匹配方法名。\n 支持多级，如：对象名.对象名.方法\n"}], "constructors": []}