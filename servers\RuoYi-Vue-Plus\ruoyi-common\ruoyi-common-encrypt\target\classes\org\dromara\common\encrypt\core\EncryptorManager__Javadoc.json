{"doc": " 加密管理类\n\n <AUTHOR>\n @version 4.6.0\n", "fields": [{"name": "encryptorMap", "doc": " 缓存加密器\n"}, {"name": "fieldCache", "doc": " 类加密字段缓存\n"}], "enumConstants": [], "methods": [{"name": "getField<PERSON>ache", "paramTypes": ["java.lang.Class"], "doc": " 获取类加密字段缓存\n"}, {"name": "registAndGetEncryptor", "paramTypes": ["org.dromara.common.encrypt.core.EncryptContext"], "doc": " 注册加密执行者到缓存\n\n @param encryptContext 加密执行者需要的相关配置参数\n"}, {"name": "removeEncryptor", "paramTypes": ["org.dromara.common.encrypt.core.EncryptContext"], "doc": " 移除缓存中的加密执行者\n\n @param encryptContext 加密执行者需要的相关配置参数\n"}, {"name": "encrypt", "paramTypes": ["java.lang.String", "org.dromara.common.encrypt.core.EncryptContext"], "doc": " 根据配置进行加密。会进行本地缓存对应的算法和对应的秘钥信息。\n\n @param value          待加密的值\n @param encryptContext 加密相关的配置信息\n"}, {"name": "decrypt", "paramTypes": ["java.lang.String", "org.dromara.common.encrypt.core.EncryptContext"], "doc": " 根据配置进行解密\n\n @param value          待解密的值\n @param encryptContext 加密相关的配置信息\n"}, {"name": "scanEncryptClasses", "paramTypes": ["java.lang.String"], "doc": " 通过 typeAliasesPackage 设置的扫描包 扫描缓存实体\n"}, {"name": "getEncryptFieldSetFromClazz", "paramTypes": ["java.lang.Class"], "doc": " 获得一个类的加密字段集合\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String"], "doc": " 构造方法传入类加密字段缓存\n\n @param typeAliasesPackage 实体类包\n"}]}