{"doc": " 防止重复提交(参考美团GTIS防重系统)\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "doAfterReturning", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.idempotent.annotation.RepeatSubmit", "java.lang.Object"], "doc": " 处理完请求后执行\n\n @param joinPoint 切点\n"}, {"name": "doAfterThrowing", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.idempotent.annotation.RepeatSubmit", "java.lang.Exception"], "doc": " 拦截异常操作\n\n @param joinPoint 切点\n @param e         异常\n"}, {"name": "argsArrayToString", "paramTypes": ["java.lang.Object[]"], "doc": " 参数拼装\n"}, {"name": "isFilterObject", "paramTypes": ["java.lang.Object"], "doc": " 判断是否需要过滤的对象。\n\n @param o 对象信息。\n @return 如果是需要过滤的对象，则返回true；否则返回false。\n"}], "constructors": []}