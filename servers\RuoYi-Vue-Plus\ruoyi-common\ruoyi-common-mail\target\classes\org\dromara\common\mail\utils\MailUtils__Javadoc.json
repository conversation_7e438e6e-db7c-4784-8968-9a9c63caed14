{"doc": " 邮件工具类\n", "fields": [], "enumConstants": [], "methods": [{"name": "getMailAccount", "paramTypes": [], "doc": " 获取邮件发送实例\n"}, {"name": "getMailAccount", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 获取邮件发送实例 (自定义发送人以及授权码)\n\n @param user 发送人\n @param pass 授权码\n"}, {"name": "sendText", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送文本邮件，发送给单个或多个收件人<br>\n 多个收件人可以使用逗号“,”分隔，也可以通过分号“;”分隔\n\n @param to      收件人\n @param subject 标题\n @param content 正文\n @param files   附件列表\n @return message-id\n @since 3.2.0\n"}, {"name": "sendHtml", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送HTML邮件，发送给单个或多个收件人<br>\n 多个收件人可以使用逗号“,”分隔，也可以通过分号“;”分隔\n\n @param to      收件人\n @param subject 标题\n @param content 正文\n @param files   附件列表\n @return message-id\n @since 3.2.0\n"}, {"name": "send", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "boolean", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送邮件，发送单个或多个收件人<br>\n 多个收件人可以使用逗号“,”分隔，也可以通过分号“;”分隔\n\n @param to      收件人\n @param subject 标题\n @param content 正文\n @param isHtml  是否为HTML\n @param files   附件列表\n @return message-id\n"}, {"name": "send", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "boolean", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送邮件，发送单个或多个收件人<br>\n 多个收件人、抄送人、密送人可以使用逗号“,”分隔，也可以通过分号“;”分隔\n\n @param to      收件人，可以使用逗号“,”分隔，也可以通过分号“;”分隔\n @param cc      抄送人，可以使用逗号“,”分隔，也可以通过分号“;”分隔\n @param bcc     密送人，可以使用逗号“,”分隔，也可以通过分号“;”分隔\n @param subject 标题\n @param content 正文\n @param isHtml  是否为HTML\n @param files   附件列表\n @return message-id\n @since 4.0.3\n"}, {"name": "sendText", "paramTypes": ["java.util.Collection", "java.lang.String", "java.lang.String", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送文本邮件，发送给多人\n\n @param tos     收件人列表\n @param subject 标题\n @param content 正文\n @param files   附件列表\n @return message-id\n"}, {"name": "sendHtml", "paramTypes": ["java.util.Collection", "java.lang.String", "java.lang.String", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送HTML邮件，发送给多人\n\n @param tos     收件人列表\n @param subject 标题\n @param content 正文\n @param files   附件列表\n @return message-id\n @since 3.2.0\n"}, {"name": "send", "paramTypes": ["java.util.Collection", "java.lang.String", "java.lang.String", "boolean", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送邮件，发送给多人\n\n @param tos     收件人列表\n @param subject 标题\n @param content 正文\n @param isHtml  是否为HTML\n @param files   附件列表\n @return message-id\n"}, {"name": "send", "paramTypes": ["java.util.Collection", "java.util.Collection", "java.util.Collection", "java.lang.String", "java.lang.String", "boolean", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送邮件，发送给多人\n\n @param tos     收件人列表\n @param ccs     抄送人列表，可以为null或空\n @param bccs    密送人列表，可以为null或空\n @param subject 标题\n @param content 正文\n @param isHtml  是否为HTML\n @param files   附件列表\n @return message-id\n @since 4.0.3\n"}, {"name": "send", "paramTypes": ["cn.hutool.extra.mail.MailAccount", "java.lang.String", "java.lang.String", "java.lang.String", "boolean", "java.io.File[]"], "doc": " 发送邮件给多人\n\n @param mailAccount 邮件认证对象\n @param to          收件人，多个收件人逗号或者分号隔开\n @param subject     标题\n @param content     正文\n @param isHtml      是否为HTML格式\n @param files       附件列表\n @return message-id\n @since 3.2.0\n"}, {"name": "send", "paramTypes": ["cn.hutool.extra.mail.MailAccount", "java.util.Collection", "java.lang.String", "java.lang.String", "boolean", "java.io.File[]"], "doc": " 发送邮件给多人\n\n @param mailAccount 邮件帐户信息\n @param tos         收件人列表\n @param subject     标题\n @param content     正文\n @param isHtml      是否为HTML格式\n @param files       附件列表\n @return message-id\n"}, {"name": "send", "paramTypes": ["cn.hutool.extra.mail.MailAccount", "java.util.Collection", "java.util.Collection", "java.util.Collection", "java.lang.String", "java.lang.String", "boolean", "java.io.File[]"], "doc": " 发送邮件给多人\n\n @param mailAccount 邮件帐户信息\n @param tos         收件人列表\n @param ccs         抄送人列表，可以为null或空\n @param bccs        密送人列表，可以为null或空\n @param subject     标题\n @param content     正文\n @param isHtml      是否为HTML格式\n @param files       附件列表\n @return message-id\n @since 4.0.3\n"}, {"name": "sendHtml", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.util.Map", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送HTML邮件，发送给单个或多个收件人<br>\n 多个收件人可以使用逗号“,”分隔，也可以通过分号“;”分隔\n\n @param to       收件人\n @param subject  标题\n @param content  正文\n @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER\n @param files    附件列表\n @return message-id\n @since 3.2.0\n"}, {"name": "send", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.util.Map", "boolean", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送邮件，发送单个或多个收件人<br>\n 多个收件人可以使用逗号“,”分隔，也可以通过分号“;”分隔\n\n @param to       收件人\n @param subject  标题\n @param content  正文\n @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER\n @param isHtml   是否为HTML\n @param files    附件列表\n @return message-id\n"}, {"name": "send", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.util.Map", "boolean", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送邮件，发送单个或多个收件人<br>\n 多个收件人、抄送人、密送人可以使用逗号“,”分隔，也可以通过分号“;”分隔\n\n @param to       收件人，可以使用逗号“,”分隔，也可以通过分号“;”分隔\n @param cc       抄送人，可以使用逗号“,”分隔，也可以通过分号“;”分隔\n @param bcc      密送人，可以使用逗号“,”分隔，也可以通过分号“;”分隔\n @param subject  标题\n @param content  正文\n @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER\n @param isHtml   是否为HTML\n @param files    附件列表\n @return message-id\n @since 4.0.3\n"}, {"name": "sendHtml", "paramTypes": ["java.util.Collection", "java.lang.String", "java.lang.String", "java.util.Map", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送HTML邮件，发送给多人\n\n @param tos      收件人列表\n @param subject  标题\n @param content  正文\n @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER\n @param files    附件列表\n @return message-id\n @since 3.2.0\n"}, {"name": "send", "paramTypes": ["java.util.Collection", "java.lang.String", "java.lang.String", "java.util.Map", "boolean", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送邮件，发送给多人\n\n @param tos      收件人列表\n @param subject  标题\n @param content  正文\n @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER\n @param isHtml   是否为HTML\n @param files    附件列表\n @return message-id\n"}, {"name": "send", "paramTypes": ["java.util.Collection", "java.util.Collection", "java.util.Collection", "java.lang.String", "java.lang.String", "java.util.Map", "boolean", "java.io.File[]"], "doc": " 使用配置文件中设置的账户发送邮件，发送给多人\n\n @param tos      收件人列表\n @param ccs      抄送人列表，可以为null或空\n @param bccs     密送人列表，可以为null或空\n @param subject  标题\n @param content  正文\n @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER\n @param isHtml   是否为HTML\n @param files    附件列表\n @return message-id\n @since 4.0.3\n"}, {"name": "send", "paramTypes": ["cn.hutool.extra.mail.MailAccount", "java.lang.String", "java.lang.String", "java.lang.String", "java.util.Map", "boolean", "java.io.File[]"], "doc": " 发送邮件给多人\n\n @param mailAccount 邮件认证对象\n @param to          收件人，多个收件人逗号或者分号隔开\n @param subject     标题\n @param content     正文\n @param imageMap    图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER\n @param isHtml      是否为HTML格式\n @param files       附件列表\n @return message-id\n @since 3.2.0\n"}, {"name": "send", "paramTypes": ["cn.hutool.extra.mail.MailAccount", "java.util.Collection", "java.lang.String", "java.lang.String", "java.util.Map", "boolean", "java.io.File[]"], "doc": " 发送邮件给多人\n\n @param mailAccount 邮件帐户信息\n @param tos         收件人列表\n @param subject     标题\n @param content     正文\n @param imageMap    图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER\n @param isHtml      是否为HTML格式\n @param files       附件列表\n @return message-id\n @since 4.6.3\n"}, {"name": "send", "paramTypes": ["cn.hutool.extra.mail.MailAccount", "java.util.Collection", "java.util.Collection", "java.util.Collection", "java.lang.String", "java.lang.String", "java.util.Map", "boolean", "java.io.File[]"], "doc": " 发送邮件给多人\n\n @param mailAccount 邮件帐户信息\n @param tos         收件人列表\n @param ccs         抄送人列表，可以为null或空\n @param bccs        密送人列表，可以为null或空\n @param subject     标题\n @param content     正文\n @param imageMap    图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER\n @param isHtml      是否为HTML格式\n @param files       附件列表\n @return message-id\n @since 4.6.3\n"}, {"name": "getSession", "paramTypes": ["cn.hutool.extra.mail.MailAccount", "boolean"], "doc": " 根据配置文件，获取邮件客户端会话\n\n @param mailAccount 邮件账户配置\n @param isSingleton 是否单例（全局共享会话）\n @return {@link Session}\n @since 5.5.7\n"}, {"name": "send", "paramTypes": ["cn.hutool.extra.mail.MailAccount", "boolean", "java.util.Collection", "java.util.Collection", "java.util.Collection", "java.lang.String", "java.lang.String", "java.util.Map", "boolean", "java.io.File[]"], "doc": " 发送邮件给多人\n\n @param mailAccount      邮件帐户信息\n @param useGlobalSession 是否全局共享Session\n @param tos              收件人列表\n @param ccs              抄送人列表，可以为null或空\n @param bccs             密送人列表，可以为null或空\n @param subject          标题\n @param content          正文\n @param imageMap         图片与占位符，占位符格式为cid:${cid}\n @param isHtml           是否为HTML格式\n @param files            附件列表\n @return message-id\n @since 4.6.3\n"}, {"name": "splitAddress", "paramTypes": ["java.lang.String"], "doc": " 将多个联系人转为列表，分隔符为逗号或者分号\n\n @param addresses 多个联系人，如果为空返回null\n @return 联系人列表\n"}], "constructors": []}