{"doc": " 自定义 Mapper 接口, 实现 自定义扩展\n\n @param <T> table 泛型\n @param <V> vo 泛型\n <AUTHOR>\n @since 2021-05-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "currentVoClass", "paramTypes": [], "doc": " 获取当前实例对象关联的泛型类型 V 的 Class 对象\n\n @return 返回当前实例对象关联的泛型类型 V 的 Class 对象\n"}, {"name": "currentModelClass", "paramTypes": [], "doc": " 获取当前实例对象关联的泛型类型 T 的 Class 对象\n\n @return 返回当前实例对象关联的泛型类型 T 的 Class 对象\n"}, {"name": "selectList", "paramTypes": [], "doc": " 使用默认的查询条件查询并返回结果列表\n\n @return 返回查询结果的列表\n"}, {"name": "insertBatch", "paramTypes": ["java.util.Collection"], "doc": " 批量插入实体对象集合\n\n @param entityList 实体对象集合\n @return 插入操作是否成功的布尔值\n"}, {"name": "updateBatchById", "paramTypes": ["java.util.Collection"], "doc": " 批量根据ID更新实体对象集合\n\n @param entityList 实体对象集合\n @return 更新操作是否成功的布尔值\n"}, {"name": "insertOrUpdateBatch", "paramTypes": ["java.util.Collection"], "doc": " 批量插入或更新实体对象集合\n\n @param entityList 实体对象集合\n @return 插入或更新操作是否成功的布尔值\n"}, {"name": "insertBatch", "paramTypes": ["java.util.Collection", "int"], "doc": " 批量插入实体对象集合并指定批处理大小\n\n @param entityList 实体对象集合\n @param batchSize  批处理大小\n @return 插入操作是否成功的布尔值\n"}, {"name": "updateBatchById", "paramTypes": ["java.util.Collection", "int"], "doc": " 批量根据ID更新实体对象集合并指定批处理大小\n\n @param entityList 实体对象集合\n @param batchSize  批处理大小\n @return 更新操作是否成功的布尔值\n"}, {"name": "insertOrUpdateBatch", "paramTypes": ["java.util.Collection", "int"], "doc": " 批量插入或更新实体对象集合并指定批处理大小\n\n @param entityList 实体对象集合\n @param batchSize  批处理大小\n @return 插入或更新操作是否成功的布尔值\n"}, {"name": "selectVoById", "paramTypes": ["java.io.Serializable"], "doc": " 根据ID查询单个VO对象\n\n @param id 主键ID\n @return 查询到的单个VO对象\n"}, {"name": "selectVoById", "paramTypes": ["java.io.Serializable", "java.lang.Class"], "doc": " 根据ID查询单个VO对象并将其转换为指定的VO类\n\n @param id      主键ID\n @param voClass 要转换的VO类的Class对象\n @param <C>     VO类的类型\n @return 查询到的单个VO对象，经过转换为指定的VO类后返回\n"}, {"name": "selectVoByIds", "paramTypes": ["java.util.Collection"], "doc": " 根据ID集合批量查询VO对象列表\n\n @param idList 主键ID集合\n @return 查询到的VO对象列表\n"}, {"name": "selectVoByIds", "paramTypes": ["java.util.Collection", "java.lang.Class"], "doc": " 根据ID集合批量查询实体对象列表，并将其转换为指定的VO对象列表\n\n @param idList  主键ID集合\n @param voClass 要转换的VO类的Class对象\n @param <C>     VO类的类型\n @return 查询到的VO对象列表，经过转换为指定的VO类后返回\n"}, {"name": "selectVoByMap", "paramTypes": ["java.util.Map"], "doc": " 根据查询条件Map查询VO对象列表\n\n @param map 查询条件Map\n @return 查询到的VO对象列表\n"}, {"name": "selectVoByMap", "paramTypes": ["java.util.Map", "java.lang.Class"], "doc": " 根据查询条件Map查询实体对象列表，并将其转换为指定的VO对象列表\n\n @param map     查询条件Map\n @param voClass 要转换的VO类的Class对象\n @param <C>     VO类的类型\n @return 查询到的VO对象列表，经过转换为指定的VO类后返回\n"}, {"name": "selectVoOne", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 根据条件查询单个VO对象\n\n @param wrapper 查询条件Wrapper\n @return 查询到的单个VO对象\n"}, {"name": "selectVoOne", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "boolean"], "doc": " 根据条件查询单个VO对象，并根据需要决定是否抛出异常\n\n @param wrapper 查询条件Wrapper\n @param throwEx 是否抛出异常的标志\n @return 查询到的单个VO对象\n"}, {"name": "selectVoOne", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class"], "doc": " 根据条件查询单个VO对象，并指定返回的VO对象的类型\n\n @param wrapper 查询条件Wrapper\n @param voClass 返回的VO对象的Class对象\n @param <C>     返回的VO对象的类型\n @return 查询到的单个VO对象，经过类型转换为指定的VO类后返回\n"}, {"name": "selectVoOne", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class", "boolean"], "doc": " 根据条件查询单个实体对象，并将其转换为指定的VO对象\n\n @param wrapper 查询条件Wrapper\n @param voClass 要转换的VO类的Class对象\n @param throwEx 是否抛出异常的标志\n @param <C>     VO类的类型\n @return 查询到的单个VO对象，经过转换为指定的VO类后返回\n"}, {"name": "selectVoList", "paramTypes": [], "doc": " 查询所有VO对象列表\n\n @return 查询到的VO对象列表\n"}, {"name": "selectVoList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 根据条件查询VO对象列表\n\n @param wrapper 查询条件Wrapper\n @return 查询到的VO对象列表\n"}, {"name": "selectVoList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class"], "doc": " 根据条件查询实体对象列表，并将其转换为指定的VO对象列表\n\n @param wrapper 查询条件Wrapper\n @param voClass 要转换的VO类的Class对象\n @param <C>     VO类的类型\n @return 查询到的VO对象列表，经过转换为指定的VO类后返回\n"}, {"name": "selectVoPage", "paramTypes": ["com.baomidou.mybatisplus.core.metadata.IPage", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": " 根据条件分页查询VO对象列表\n\n @param page    分页信息\n @param wrapper 查询条件Wrapper\n @return 查询到的VO对象分页列表\n"}, {"name": "selectVoPage", "paramTypes": ["com.baomidou.mybatisplus.core.metadata.IPage", "com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class"], "doc": " 根据条件分页查询实体对象列表，并将其转换为指定的VO对象分页列表\n\n @param page    分页信息\n @param wrapper 查询条件Wrapper\n @param voClass 要转换的VO类的Class对象\n @param <C>     VO类的类型\n @param <P>     VO对象分页列表的类型\n @return 查询到的VO对象分页列表，经过转换为指定的VO类后返回\n"}, {"name": "selectObjs", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "java.util.function.Function"], "doc": " 根据条件查询符合条件的对象，并将其转换为指定类型的对象列表\n\n @param wrapper 查询条件Wrapper\n @param mapper  转换函数，用于将查询到的对象转换为指定类型的对象\n @param <C>     要转换的对象的类型\n @return 查询到的符合条件的对象列表，经过转换为指定类型的对象后返回\n"}], "constructors": []}