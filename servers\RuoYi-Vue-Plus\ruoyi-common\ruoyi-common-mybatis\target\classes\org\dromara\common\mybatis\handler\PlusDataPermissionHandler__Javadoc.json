{"doc": " 数据权限过滤\n\n <AUTHOR> Li\n @version 3.5.0\n", "fields": [{"name": "dataPermissionCacheMap", "doc": " 类名称与注解的映射关系缓存(由于aop无法拦截mybatis接口类上的注解 只能通过启动预扫描的方式进行)\n"}, {"name": "parser", "doc": " spel 解析器\n"}, {"name": "beanResolver", "doc": " bean解析器 用于处理 spel 表达式中对 bean 的调用\n"}], "enumConstants": [], "methods": [{"name": "getSqlSegment", "paramTypes": ["net.sf.jsqlparser.expression.Expression", "java.lang.String", "boolean"], "doc": " 获取数据过滤条件的 SQL 片段\n\n @param where             原始的查询条件表达式\n @param mappedStatementId Mapper 方法的 ID\n @param isSelect          是否为查询语句\n @return 数据过滤条件的 SQL 片段\n"}, {"name": "buildDataFilter", "paramTypes": ["org.dromara.common.mybatis.annotation.DataPermission", "boolean"], "doc": " 构建数据过滤条件的 SQL 语句\n\n @param dataPermission 数据权限注解\n @param isSelect       标志当前操作是否为查询操作，查询操作和更新或删除操作在处理过滤条件时会有不同的处理方式\n @return 构建的数据过滤条件的 SQL 语句\n @throws ServiceException 如果角色的数据范围异常或者 key 与 value 的长度不匹配，则抛出 ServiceException 异常\n"}, {"name": "scanMapperClasses", "paramTypes": ["java.lang.String"], "doc": " 扫描指定包下的 Mapper 类，并查找其中带有特定注解的方法或类\n\n @param mapperPackage Mapper 类所在的包路径\n"}, {"name": "findAnnotation", "paramTypes": ["java.lang.Class"], "doc": " 在指定的类中查找特定的注解 DataPermission，并将带有这个注解的方法或类存储到 dataPermissionCacheMap 中\n\n @param clazz 要查找的类\n"}, {"name": "getDataPermission", "paramTypes": ["java.lang.String"], "doc": " 根据映射语句 ID 或类名获取对应的 DataPermission 注解对象\n\n @param mapperId 映射语句 ID\n @return DataPermission 注解对象，如果不存在则返回 null\n"}, {"name": "invalid", "paramTypes": ["java.lang.String"], "doc": " 检查给定的映射语句 ID 是否有效，即是否能够找到对应的 DataPermission 注解对象\n\n @param mapperId 映射语句 ID\n @return 如果找到对应的 DataPermission 注解对象，则返回 false；否则返回 true\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String"], "doc": " 构造方法，扫描指定包下的 Mapper 类并初始化缓存\n\n @param mapperPackage Mapper 类所在的包路径\n"}]}