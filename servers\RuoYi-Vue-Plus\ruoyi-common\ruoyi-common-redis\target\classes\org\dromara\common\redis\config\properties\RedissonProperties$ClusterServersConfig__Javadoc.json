{"doc": "", "fields": [{"name": "clientName", "doc": " 客户端名称\n"}, {"name": "masterConnectionMinimumIdleSize", "doc": " master最小空闲连接数\n"}, {"name": "masterConnectionPoolSize", "doc": " master连接池大小\n"}, {"name": "slaveConnectionMinimumIdleSize", "doc": " slave最小空闲连接数\n"}, {"name": "slaveConnectionPoolSize", "doc": " slave连接池大小\n"}, {"name": "idleConnectionTimeout", "doc": " 连接空闲超时，单位：毫秒\n"}, {"name": "timeout", "doc": " 命令等待超时，单位：毫秒\n"}, {"name": "subscriptionConnectionPoolSize", "doc": " 发布和订阅连接池大小\n"}, {"name": "readMode", "doc": " 读取模式\n"}, {"name": "subscriptionMode", "doc": " 订阅模式\n"}], "enumConstants": [], "methods": [], "constructors": []}