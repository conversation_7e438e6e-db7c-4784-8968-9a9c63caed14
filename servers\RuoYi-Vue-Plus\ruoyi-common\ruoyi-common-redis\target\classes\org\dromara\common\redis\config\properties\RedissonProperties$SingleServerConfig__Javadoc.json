{"doc": "", "fields": [{"name": "clientName", "doc": " 客户端名称\n"}, {"name": "connectionMinimumIdleSize", "doc": " 最小空闲连接数\n"}, {"name": "connectionPoolSize", "doc": " 连接池大小\n"}, {"name": "idleConnectionTimeout", "doc": " 连接空闲超时，单位：毫秒\n"}, {"name": "timeout", "doc": " 命令等待超时，单位：毫秒\n"}, {"name": "subscriptionConnectionPoolSize", "doc": " 发布和订阅连接池大小\n"}], "enumConstants": [], "methods": [], "constructors": []}