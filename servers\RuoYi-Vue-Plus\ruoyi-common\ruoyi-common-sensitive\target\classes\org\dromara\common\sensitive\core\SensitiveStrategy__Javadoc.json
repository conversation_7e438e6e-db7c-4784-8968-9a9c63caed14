{"doc": " 脱敏策略\n\n <AUTHOR>\n @version 3.6.0\n", "fields": [], "enumConstants": [{"name": "ID_CARD", "doc": " 身份证脱敏\n"}, {"name": "PHONE", "doc": " 手机号脱敏\n"}, {"name": "ADDRESS", "doc": " 地址脱敏\n"}, {"name": "EMAIL", "doc": " 邮箱脱敏\n"}, {"name": "BANK_CARD", "doc": " 银行卡\n"}, {"name": "CHINESE_NAME", "doc": " 中文名\n"}, {"name": "FIXED_PHONE", "doc": " 固定电话\n"}, {"name": "USER_ID", "doc": " 用户ID\n"}, {"name": "PASSWORD", "doc": " 密码\n"}, {"name": "IPV4", "doc": " ipv4\n"}, {"name": "IPV6", "doc": " ipv6\n"}, {"name": "CAR_LICENSE", "doc": " 中国大陆车牌，包含普通车辆、新能源车辆\n"}, {"name": "FIRST_MASK", "doc": " 只显示第一个字符\n"}, {"name": "CLEAR", "doc": " 清空为\"\"\n"}, {"name": "CLEAR_TO_NULL", "doc": " 清空为null\n"}], "methods": [], "constructors": []}