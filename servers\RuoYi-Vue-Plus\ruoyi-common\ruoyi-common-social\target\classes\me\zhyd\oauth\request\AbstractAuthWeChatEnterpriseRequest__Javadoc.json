{"doc": " <p>\n 企业微信登录父类\n </p>\n\n <AUTHOR> (347826496(a)qq.com)\n @since 1.15.9\n", "fields": [], "enumConstants": [], "methods": [{"name": "checkResponse", "paramTypes": ["java.lang.String"], "doc": " 校验请求结果\n\n @param response 请求结果\n @return 如果请求结果正常，则返回JSONObject\n"}, {"name": "accessTokenUrl", "paramTypes": ["java.lang.String"], "doc": " 返回获取accessToken的url\n\n @param code 授权码\n @return 返回获取accessToken的url\n"}, {"name": "userInfoUrl", "paramTypes": ["me.zhyd.oauth.model.AuthToken"], "doc": " 返回获取userInfo的url\n\n @param authToken 用户授权后的token\n @return 返回获取userInfo的url\n"}, {"name": "getUserDetail", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 用户详情\n\n @param accessToken accessToken\n @param userId      企业内用户id\n @param userTicket  成员票据，用于获取用户信息或敏感信息\n @return 用户详情\n"}], "constructors": []}