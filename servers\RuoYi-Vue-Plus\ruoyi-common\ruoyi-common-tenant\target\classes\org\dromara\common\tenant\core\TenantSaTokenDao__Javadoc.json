{"doc": " SaToken 认证数据持久层 适配多租户\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "update", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 修修改指定key-value键值对 (过期时间不变)\n"}, {"name": "delete", "paramTypes": ["java.lang.String"], "doc": " 删除Value\n"}, {"name": "getTimeout", "paramTypes": ["java.lang.String"], "doc": " 获取Value的剩余存活时间 (单位: 秒)\n"}, {"name": "updateTimeout", "paramTypes": ["java.lang.String", "long"], "doc": " 修改Value的剩余存活时间 (单位: 秒)\n"}, {"name": "getObject", "paramTypes": ["java.lang.String"], "doc": " 获取Object，如无返空\n"}, {"name": "getObject", "paramTypes": ["java.lang.String", "java.lang.Class"], "doc": " 获取 Object (指定反序列化类型)，如无返空\n\n @param key 键名称\n @return object\n"}, {"name": "setObject", "paramTypes": ["java.lang.String", "java.lang.Object", "long"], "doc": " 写入Object，并设定存活时间 (单位: 秒)\n"}, {"name": "updateObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 更新Object (过期时间不变)\n"}, {"name": "deleteObject", "paramTypes": ["java.lang.String"], "doc": " 删除Object\n"}, {"name": "getObjectTimeout", "paramTypes": ["java.lang.String"], "doc": " 获取Object的剩余存活时间 (单位: 秒)\n"}, {"name": "updateObjectTimeout", "paramTypes": ["java.lang.String", "long"], "doc": " 修改Object的剩余存活时间 (单位: 秒)\n"}, {"name": "searchData", "paramTypes": ["java.lang.String", "java.lang.String", "int", "int", "boolean"], "doc": " 搜索数据\n"}], "constructors": []}